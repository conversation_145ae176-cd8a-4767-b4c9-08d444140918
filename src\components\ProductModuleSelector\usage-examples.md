# ProductModuleSelector 使用示例

## 1. 基本使用（只显示模块选择，不显示表单）

```tsx
import ProductModuleSelector from '@/components/ProductModuleSelector';

// 只需要模块选择功能，不需要表单
<ProductModuleSelector 
  ref={productModuleSelectorRef}
  needFormConfig={false}  // 不显示表单
  title="选择模块"
  selectedData={{
    productKey: 'some-product-key', // 直接传入产品key
  }}
  onChange={(data) => {
    console.log('选中的模块:', data.selectedModules);
  }}
/>
```

## 2. 自定义表单配置

```tsx
// 自定义表单配置
const customFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '选择产品',
      type: 'select',
      placeholder: '请选择产品',
      validatorRules: [{ required: true, message: '请选择产品' }],
    },
    {
      fieldName: 'customField',
      label: '自定义字段',
      type: 'input',
      placeholder: '请输入自定义内容',
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          // 自定义获取产品列表的逻辑
          return await getProductList();
        },
      },
    ],
  },
};

<ProductModuleSelector 
  ref={productModuleSelectorRef}
  needFormConfig={true}
  formConfig={customFormConfig}
  formType="edit"
  title="自定义产品模块选择"
  onChange={(data) => {
    console.log('表单数据:', data);
  }}
/>
```

## 3. 第一步配置选择（默认用法）

```tsx
<ProductModuleSelector 
  ref={productModuleSelectorRef}
  // 使用默认配置，等同于：
  // needFormConfig={true}
  // step="first"
  // formType="search"
  // title="选择产品及模块"
  onChange={(data) => {
    console.log('产品和模块:', data);
  }}
/>
```

## 4. 第二步模板编辑（向后兼容）

```tsx
<ProductModuleSelector 
  ref={productModuleSelectorRef}
  step="second"  // 自动使用第二步表单配置
  selectedData={{
    productKey: 'product-123',
    templateName: '模板名称',
    productModelNoList: ['model-1', 'model-2'],
    remark: '备注信息',
  }}
/>
```

## 5. 获取数据

```tsx
const handleGetData = async () => {
  try {
    const data = await productModuleSelectorRef.current?.getSelectedData();
    console.log('获取的数据:', data);
    
    // 数据结构：
    // {
    //   productKey: string,
    //   selectedModules: string[],
    //   // 如果有表单配置，还会包含表单字段：
    //   templateName?: string,
    //   productModelNoList?: string[],
    //   remark?: string,
    //   customField?: any, // 自定义字段
    // }
  } catch (error) {
    console.error('验证失败:', error.message);
  }
};
```

## Props 说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| selectedData | any | - | 初始选中的数据 |
| onChange | function | - | 数据变化回调 |
| step | 'first' \| 'second' | 'first' | 步骤类型（向后兼容） |
| needFormConfig | boolean | true | 是否需要表单配置 |
| formConfig | any | - | 表单配置对象 |
| formType | 'search' \| 'edit' | 'search' | 表单类型 |
| title | string | '选择产品及模块' | 组件标题 |

## 迁移指南

### 从旧版本迁移

```tsx
// 旧版本
<ProductModuleSelector 
  step="first"
  selectedData={data}
  onChange={handleChange}
/>

// 新版本（完全兼容）
<ProductModuleSelector 
  step="first"  // 保持不变
  selectedData={data}
  onChange={handleChange}
  // 新增的props都有默认值，无需修改
/>
```

### 新功能使用

```tsx
// 只要模块选择，不要表单
<ProductModuleSelector 
  needFormConfig={false}
  selectedData={{ productKey: 'known-product' }}
/>

// 自定义表单
<ProductModuleSelector 
  formConfig={myCustomConfig}
  formType="edit"
/>
```
