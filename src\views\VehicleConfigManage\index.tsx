import React, { useState, useEffect, useRef } from 'react';
import { Table, Modal, message } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  pageSizeOptions,
  SearchCondition,
  TableListType,
} from '@/utils/constant';
import {
  saveSearchValues,
  removeSearchValues,
  searchformSelector,
} from '@/redux/reducers/searchform';
import { SearchConfig, TableColumns } from './utils/column';
import { CustomButton } from '@/components/CustomButton';
import './index.scss';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import CommonForm from '@/components/CommonForm';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
const VehicleConfigManage = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const historySearchValue = useSelector(searchformSelector).searchValues;
  const initSearchCondition = {
    searchForm: {
      vehicleType: null,
      name: null,
      producType: null,
    },
    current: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValue ? historySearchValue : initSearchCondition;
    },
  );
  const formRef = useRef<any>(null);
  const [searchFormConfig, setSearchFormConfig] =
    useState<FormConfig>(SearchConfig);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  useEffect(() => {
    fetchTableData(searchCondition);
    fetchVehicleTypeNameList();
  }, []);
  const fetchTableData = (searchValues: any) => {
    setLoading(true);
    try {
      request({
        method: 'POST',
        path: '/ota/web/vehicle_conf_template_page_list',
        body: {
          vehicleTypeId: searchValues.searchForm.vehicleType?.value,
          name: searchValues.searchForm.name,
          productType: searchValues.searchForm.productType?.value,
        },
        urlParams: {
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          });
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  const fetchVehicleTypeNameList = () => {
    request({
      method: 'GET',
      path: '/ota/web/get_vehicle_type_name_list',
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          const vehicleTypeField = searchFormConfig?.fields?.find(
            (field: FieldItem) => field.fieldName === 'vehicleType',
          );
          if (vehicleTypeField) {
            vehicleTypeField.options = res.data?.map((item: any) => {
              return { label: item.name, value: item.code };
            });
          }

          setSearchFormConfig({ ...searchFormConfig });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const onDel = (record: any) => {
    Modal.confirm({
      content: (
        <p style={{ wordBreak: 'break-all' }}>确定删除{record.name}吗？</p>
      ),
      icon: <ExclamationCircleOutlined />,
      onCancel: () => {},
      onOk() {
        request({
          method: 'PUT',
          path: '/ota/web/delete_vehicle_conf_template',
          urlParams: {
            number: record.number,
          },
        })
          .then((res: any) => {
            if (res && res.code === HttpStatusCode.Success) {
              message.success(res.message);
              fetchTableData(searchCondition);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    });
  };
  const formatColumns = () => {
    return TableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'configurationFileCount':
          col.render = (text: any, record: any, index: number) => `${text}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    navigator(
                      `/vehicleConfigManage/edit?type=edit&number=${record.number}&productType=${record.productType}`,
                    );
                  }}
                >
                  编辑
                </a>
                <a onClick={() => onDel(record)}>删除</a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onSearchClick = () => {
    const data = {
      ...searchCondition,
      searchForm: formRef.current?.getFieldsValue(),
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
    fetchTableData(data);
  };
  const onResetClick = () => {
    formRef.current?.setFieldsValue(initSearchCondition.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    setSearchCondition({ ...initSearchCondition });
    fetchTableData(initSearchCondition);
  };
  return (
    <div className="vehicle-config-manage">
      <div className="searchform">
        <CommonForm
          colon={false}
          layout={'inline'}
          formConfig={searchFormConfig}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          getFormInstance={(formInstance: any) => {
            formRef.current = formInstance;
          }}
          defaultValue={historySearchValue?.searchForm}
          formType="search"
        />
      </div>
      <div className="table-container">
        <div className="middle-btns">
          <CustomButton
            onSubmitClick={() => {
              navigator('/vehicleConfigManage/add?type=add');
            }}
            title="新建"
            otherCSSProperties={{ height: 35, marginBottom: 10 }}
          />
        </div>
        <Table
          rowKey={(record) => record.number}
          loading={loading}
          bordered
          dataSource={tableList.list}
          columns={formatColumns()}
          scroll={{
            y: 600,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: searchCondition.current,
            pageSize: searchCondition.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any,
          ) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                current,
                pageSize,
              };
              dispatch(
                saveSearchValues({
                  routeName: location.pathname,
                  searchValues: newSearchValue,
                }),
              );
              setSearchCondition(newSearchValue);
              fetchTableData(newSearchValue);
            }
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(VehicleConfigManage);
