.edit-configuration {
  .basic-info-card {
    margin-bottom: 16px;

    .ant-card-head-title {
      font-weight: 600;
      color: #262626;
    }

    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .config-edit-card {
    .ant-card-head-title {
      font-weight: 600;
      color: #262626;
    }

    .ant-tabs {
      .ant-tabs-tab {
        font-weight: 500;
      }

      .ant-tabs-content-holder {
        padding: 16px 0;
      }
    }

    .block-content {
      .structured-config-section,
      .config-file-section {
        margin-bottom: 24px;

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1677ff;
          margin-bottom: 16px;
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: 8px;
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px 0;
        color: #8c8c8c;
        font-style: italic;
        background: #fafafa;
        border-radius: 6px;
        border: 1px dashed #d9d9d9;
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 40px 0;
    color: #8c8c8c;
    font-style: italic;
    background: #fafafa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
  }
}

.edit-config-file-form {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label > label {
    font-weight: 500;
    color: #262626;
  }

  .ant-input[disabled] {
    background: #f5f5f5;
    color: #595959;
  }
}
