.config-file-selector {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .section-content {
    .ant-tabs {
      margin-bottom: 16px;
    }

    .file-table-container {
      .search-bar {
        margin-bottom: 16px;
      }

      .table-info {
        margin-bottom: 12px;
        font-size: 14px;
        color: #595959;

        span {
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 4px;
          padding: 4px 8px;
          font-size: 12px;
          color: #52c41a;
        }
      }

      .ant-table {
        border: 1px solid #f0f0f0;
        border-radius: 6px;

        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 500;
          color: #262626;
        }

        .ant-table-tbody > tr > td {
          padding: 12px 16px;
        }

        .ant-table-tbody > tr:hover > td {
          background: #f5f5f5;
        }

        // 操作列样式
        a {
          color: #1677ff;
          text-decoration: none;
          font-size: 14px;

          &:hover {
            color: #4096ff;
            text-decoration: underline;
          }
        }
      }

      .ant-table-selection-column {
        width: 60px;
      }
    }
  }

  .no-product,
  .no-data {
    color: #8c8c8c;
    font-style: italic;
    text-align: center;
    padding: 40px 0;
    background: #fafafa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
  }
}

// 查看配置文件弹窗样式
.view-config-file-form {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label > label {
    font-weight: 500;
    color: #262626;
  }

  .ant-input,
  .ant-input[disabled] {
    background: #f5f5f5;
    border-color: #d9d9d9;
  }

  .ant-input[disabled] {
    color: #595959;
  }
}
