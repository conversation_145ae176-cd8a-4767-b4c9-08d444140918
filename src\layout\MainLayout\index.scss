.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-size: 18px;
  overflow-x: hidden;

  .header {
    height: 58px;
    width: 100%;
    display: flex;
    justify-content: stretch;
    position: fixed;
    z-index: 100;
  }

  .layout-content {
    margin-top: 58px;
    flex: 1;
    display: flex;
    flex-direction: row;

    .layout-menu-wrapper {
      color: rgba(255, 255, 255, 0.65);
      background: #222324;
      height: 100%;
      min-width: 200px;

      .ant-menu-item .anticon,
      .ant-menu-submenu-title .anticon {
        font-size: 18px;
      }
    }

    .main {
      z-index: 20;
      width: calc(100vw - 200px);
      max-height: calc(100vh - 58px);
      box-sizing: border-box;
      padding: 10px;
      background-color: rgba(240, 242, 245, 1);
      overflow-y: scroll;
    }
  }
}