import { Col, Form, Input, Radio, Row, Select, Flex, message } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { DataType } from '../../utils/constant';
import { cloneDeep } from 'lodash';
import './StructModule.scss';
import showModal from '@/components/commonModal';
import { sendGlobalEvent } from '@/utils/emit';
import FunctionInfo from './FunctionInfo';
import { TSLFunctionType } from '@/utils/constant';

const StructModule = React.memo(
  ({
    level,
    disabled,
    parentKey,
    initChildKeyList,
    tslRef,
    changeParentStatus,
  }: {
    level: number;
    disabled: boolean;
    parentKey: any;
    initChildKeyList: any[];
    tslRef: any;
    changeParentStatus: AnyFunc;
  }) => {
    const [curEditKey, setCurEditKey] = useState<any>(null);

    const functionType = tslRef.getTSLInfo('functionType');
    const pageType = tslRef.getTSLInfo('pageType');
    const tslInfo = tslRef.getTSLInfo('tslInfo');
    const curEditLevel = tslRef.getTSLInfo('curEditLevel');
    const maxLevel = tslRef.getTSLInfo('maxLevel');
    let formRefList = useRef<any[]>(tslRef.getTSLInfo('formInstanceList'));
    const formInstance = formRefList.current[level];
    const parentInstance = formRefList.current[level - 1];
    const [parent, setParent] = useState<any>({});

    useEffect(() => {
      setParent({
        ...tslInfo[level - 1][parentKey],
        dataSpecsList: initChildKeyList,
      });
      parentInstance.setFieldsValue({ dataSpecsList: initChildKeyList });
    }, [parentKey]);

    const addJSONProperty = () => {
      formInstance.resetFields();
      const key = String(Date.now());
      tslInfo[level] = {
        ...tslInfo[level],
        [key]: {},
      };
      tslRef.saveTSLInfo({
        tslInfo: tslInfo,
        curEditLevel: level,
      });
      setCurEditKey(key);
      changeParentStatus(false);
    };
    const handleStage = () => {
      if (level !== tslRef.getTSLInfo('curEditLevel')) {
        message.error('请先保存里面的层级');
        return;
      }
      formInstance
        .validateFields()
        .then((res: any) => {
          // 对当前项的处理：
          if (!tslInfo[level]) {
            tslInfo[level] = {};
          }
          let curInfo = cloneDeep(tslInfo[level][curEditKey]);
          if (
            res.dataType === DataType.ENUM ||
            res.dataType === DataType.INT_ENUM
          ) {
            if (!res.enumList) {
              message.error('枚举参数为空');
              sendGlobalEvent('checkRes', false);
              throw new Error('枚举参数为空');
            } else {
              tslInfo[level][curEditKey] = res;
              sendGlobalEvent('checkRes', true);
            }
          } else if (
            (res.dataType === DataType.ARRAY &&
              res.childDataType === DataType.STRUCT) ||
            res.dataType === DataType.STRUCT
          ) {
            //  当前项是结构体时，保存当前项，只保存没有被打删除标记的数据，斩断父与子的关联，来达到删除
            if (level === maxLevel - 1) {
              tslInfo[level][curEditKey] = curInfo
                ? {
                    ...curInfo,
                    ...res,
                  }
                : res;
              tslRef.saveTSLInfo({
                tslInfo: tslInfo,
              });
            }
            if (
              level < 5 &&
              curInfo?.dataSpecsList?.length > 0 &&
              curInfo?.dataSpecsList.filter((v: any) => v.type === 'del')
                .length !== curInfo.dataSpecsList?.length
            ) {
              const childList: any[] = [];
              curInfo.dataSpecsList.forEach((v: any) => {
                if (!v.type) {
                  childList.push(v);
                } else if (v.type === 'add') {
                  childList.push(v.key);
                }
              });
              tslInfo[level][curEditKey] = {
                ...curInfo,
                ...res,
                dataSpecsList: childList,
              };
              tslRef.saveTSLInfo({
                tslInfo: tslInfo,
              });
            }
          } else {
            // 当前项不是特殊项
            tslInfo[level][curEditKey] = curInfo
              ? {
                  ...curInfo,
                  ...res,
                }
              : res;
            tslRef.saveTSLInfo({
              tslInfo: tslInfo,
            });
          }

          // 对父级的处理： 该项是新增时，父级没有该项，将该项与父级关联上
          const hasCurChild =
            parent.dataSpecsList?.filter((v: any) => {
              if (typeof v === 'object') {
                return v.key === curEditKey;
              } else {
                return v === curEditKey;
              }
            }).length > 0;
          if (!hasCurChild) {
            const childKeyList = parent.dataSpecsList
              ? parent.dataSpecsList.concat([
                  level === 1 ? curEditKey : { key: curEditKey, type: 'add' },
                ])
              : [level === 1 ? curEditKey : { key: curEditKey, type: 'add' }];

            tslInfo[level - 1][parentKey] = {
              ...parent,
              dataSpecsList: childKeyList,
            };
            tslRef.saveTSLInfo({
              tslInfo: tslInfo,
            });
            parentInstance.setFieldsValue({
              dataSpecsList: childKeyList,
            });
          }

          setParent(tslInfo[level - 1][parentKey]);
          setCurEditKey(null);
          tslRef.changeCurEditLevel(curEditLevel - 1);
          changeParentStatus(true);
        })
        .catch((err: any) => {
          console.log(err);
        });
    };
    const handleCheckBack = () => {
      tslRef.changeCurEditLevel(curEditLevel - 1);
      setCurEditKey(null);
      changeParentStatus(true);
    };
    const handleBack = () => {
      if (disabled) {
        handleCheckBack();
        return;
      }
      if (level !== curEditLevel) {
        message.error('请先取消里面的层级');
        return;
      }
      showModal({
        title: '',
        width: '600px',
        content:
          '此操作会恢复本层级的上一次暂存状态（包括其子状态层级），是否确认操作？',
        footer: [
          {
            text: '取消',
            type: 'cancelBtn',
          },
          {
            text: '确定',
            type: 'notCancelBtn',
            needValidate: true,
            onClick: (cb: any) => {
              const hasCurChild =
                parent.dataSpecsList?.filter(
                  (v: any) => v === curEditKey || v.key === curEditKey,
                ).length > 0;
              const curInfo = tslInfo[level][curEditKey];
              if (!hasCurChild) {
                // 父级不包含当前删除的项
                delete tslInfo[level][curEditKey];
                tslRef.saveTSLInfo({
                  tslInfo: tslInfo,
                });
              }
              if (
                (curInfo.dataType === DataType.ARRAY &&
                  curInfo.childDataType === DataType.STRUCT) ||
                curInfo.dataType === DataType.STRUCT
              ) {
                const childList: any[] = [];
                curInfo.dataSpecsList?.forEach((v: any) => {
                  if (!v.type) {
                    childList.push(v);
                  } else if (v.type === 'del') {
                    childList.push(v.key);
                  }
                });
                tslInfo[level][curEditKey] = {
                  ...curInfo,
                  dataSpecsList: childList,
                };
                tslRef.saveTSLInfo({
                  tslInfo: tslInfo,
                });
              }
              tslRef.changeCurEditLevel(curEditLevel - 1);
              setCurEditKey(null);
              changeParentStatus(true);
              cb();
            },
          },
        ],
      });
    };

    const handleDel = (key: any, index: number) => {
      if (curEditKey && curEditKey !== key) {
        message.info('请先编辑完里面的层级');
        return;
      }
      const p = cloneDeep(parent);
      tslInfo[level - 1][parentKey] = {
        ...p,
        dataSpecsList: p.dataSpecsList.map((v: any) => {
          if (v === key || v.key === key) {
            return {
              key: key,
              type: 'del',
            };
          } else {
            return v;
          }
        }),
      };
      setParent(tslInfo[level - 1][parentKey]);
      tslRef.saveTSLInfo({
        tslInfo: tslInfo,
      });
    };

    const handleEdit = (key: any) => {
      if (curEditKey && curEditKey !== key) {
        message.info('请先保存里面的层级');
        return;
      }
      const val = tslInfo[level][key];
      tslRef.changeCurEditLevel(level);
      // formInstance.setFieldsValue(val);
      setCurEditKey(key);
      changeParentStatus(false);
    };

    return (
      <div className="struct-module">
        {functionType === TSLFunctionType.Server ? (
          ''
        ) : (
          <div className="number">{level}</div>
        )}
        <div
          className={
            functionType === TSLFunctionType.Server
              ? 'content no-border'
              : 'content'
          }
        >
          <Form.Item
            label="JSON对象"
            name="dataSpecsList"
            labelCol={{ span: 3 }}
            rules={[{ required: true, message: '结构体参数不能为空' }]}
            style={{
              display:
                functionType === TSLFunctionType.Server && level === 1
                  ? 'none'
                  : 'block',
            }}
          ></Form.Item>

          <div className="added">
            {parent.dataSpecsList &&
              parent.dataSpecsList.map((key: any, index: number) => {
                const _key = key.key ?? key;
                const info = tslInfo[level][_key];
                if (
                  info &&
                  ((!key.type && key !== curEditKey) ||
                    (key.type === 'add' && key.key !== curEditKey))
                ) {
                  return (
                    <div key={key} className="property">
                      <div className="name">参数名称：{info.name}</div>
                      <div className="btns">
                        <span
                          onClick={() => {
                            handleEdit(_key);
                          }}
                        >
                          {disabled ? '查看' : '编辑'}
                        </span>

                        {!disabled && (
                          <span
                            onClick={() => {
                              handleDel(_key, index);
                            }}
                          >
                            删除
                          </span>
                        )}
                      </div>
                    </div>
                  );
                } else {
                  return <></>;
                }
              })}
          </div>
          {!curEditKey && !disabled && (
            <div className="JSON-add-btn">
              <span onClick={addJSONProperty}>+新增参数</span>
            </div>
          )}
          {curEditKey && (
            <FunctionInfo
              key={curEditKey}
              level={level}
              curKey={curEditKey}
              tslRef={tslRef}
            />
          )}

          {curEditKey && (
            <div className="save-btns">
              {pageType !== 'check' && <span onClick={handleStage}>暂存</span>}
              <span onClick={handleBack}>取消</span>
            </div>
          )}
        </div>
      </div>
    );
  },
);

export default React.memo(StructModule);
