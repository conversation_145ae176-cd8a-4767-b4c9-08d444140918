import { request } from '@/fetch/core';

const mockDataList = {
  taskNo: 'TASK001',
  blockName: '配置块A',
  blockNo: 'BLK001',
  taskStatus: 1,
  taskStatusName: '已生效',
  effectiveTime: '2025-01-01 08:00:00',
  immediately: 1,
  immediatelyName: '立即推送',
  executionTime: '2025-01-01 08:00:00',
  batch: 1,
  batchName: '分批',
  batchCount: 10,
  batchInterval: 60,
  createUser: '张三',
  createTime: '2024-12-25 08:00:00',
};

const listData = {
  pageNum: 1,
  pageSize: 21,
  pages: 1,
  total: 21,
  list: [
    {
      taskNo: 'TASK001',
      productModelNames: 'ModelB,ModelD',
      blockNames: 'Block2',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 12,
      failureTotal: 3,
      immediately: 1,
      issueTime: '2024-07-10 09:15:00',
      deviceTotal: 15,
      createUser: '张三',
      createTime: '2024-07-10 09:15:00',
      productKey: 'pda',
    },
    {
      taskNo: 'TASK002',
      productModelNames: 'ModelA',
      blockNames: 'Block1,Block4',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 0,
      failureTotal: 8,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-03-05 14:00:00',
      deviceTotal: 8,
      createUser: '李四',
      createTime: '2024-02-20 14:00:00',
    },
    {
      taskNo: 'TASK003',
      productModelNames: 'ModelC,ModelE,ModelA',
      blockNames: 'Block3',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 5,
      failureTotal: 5,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-05-25 10:30:00',
      deviceTotal: 10,
      createUser: '王五',
      createTime: '2024-05-15 10:30:00',
    },
    {
      taskNo: 'TASK004',
      productModelNames: 'ModelD',
      blockNames: 'Block5',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-11-01 08:00:00',
      deviceTotal: 0,
      createUser: '赵六',
      createTime: '2024-11-01 08:00:00',
    },
    {
      taskNo: 'TASK005',
      productModelNames: 'ModelE',
      blockNames: 'Block1',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-04-02 16:45:00',
      deviceTotal: 0,
      createUser: '陈七',
      createTime: '2024-03-28 16:45:00',
    },
    {
      taskNo: 'TASK006',
      productModelNames: 'ModelA,ModelC',
      blockNames: 'Block2,Block3',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 7,
      failureTotal: 3,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-08-12 11:20:00',
      deviceTotal: 10,
      createUser: '张三',
      createTime: '2024-08-12 11:20:00',
    },
    {
      taskNo: 'TASK007',
      productModelNames: 'ModelB',
      blockNames: 'Block4',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 10,
      failureTotal: 5,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-09-01 00:00:00',
      deviceTotal: 15,
      createUser: '李四',
      createTime: '2024-08-25 00:00:00',
    },
    {
      taskNo: 'TASK008',
      productModelNames: 'ModelD,ModelE',
      blockNames: 'Block5',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 18,
      failureTotal: 2,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-06-15 14:10:00',
      deviceTotal: 20,
      createUser: '王五',
      createTime: '2024-06-15 14:10:00',
    },
    {
      taskNo: 'TASK009',
      productModelNames: 'ModelC',
      blockNames: 'Block1',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 8,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-10-10 09:00:00',
      deviceTotal: 8,
      createUser: '赵六',
      createTime: '2024-10-01 09:00:00',
    },
    {
      taskNo: 'TASK010',
      productModelNames: 'ModelA,ModelB,ModelC',
      blockNames: 'Block2',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-12-05 17:30:00',
      deviceTotal: 0,
      createUser: '陈七',
      createTime: '2024-12-05 17:30:00',
    },
    {
      taskNo: 'TASK011',
      productModelNames: 'ModelE',
      blockNames: 'Block3,Block4',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 9,
      failureTotal: 1,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-04-20 13:45:00',
      deviceTotal: 10,
      createUser: '张三',
      createTime: '2024-04-20 13:45:00',
    },
    {
      taskNo: 'TASK012',
      productModelNames: 'ModelD',
      blockNames: 'Block5',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 6,
      failureTotal: 4,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-07-22 08:00:00',
      deviceTotal: 10,
      createUser: '李四',
      createTime: '2024-07-15 08:00:00',
    },
    {
      taskNo: 'TASK013',
      productModelNames: 'ModelA,ModelE',
      blockNames: 'Block1',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-05-01 12:00:00',
      deviceTotal: 0,
      createUser: '王五',
      createTime: '2024-04-25 12:00:00',
    },
    {
      taskNo: 'TASK014',
      productModelNames: 'ModelB,ModelC',
      blockNames: 'Block2',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 15,
      failureTotal: 5,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-09-10 10:10:10',
      deviceTotal: 20,
      createUser: '赵六',
      createTime: '2024-09-10 10:10:10',
    },
    {
      taskNo: 'TASK015',
      productModelNames: 'ModelD',
      blockNames: 'Block3',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 7,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-11-15 15:30:00',
      deviceTotal: 7,
      createUser: '陈七',
      createTime: '2024-11-10 15:30:00',
    },
    {
      taskNo: 'TASK016',
      productModelNames: 'ModelA,ModelB,ModelD',
      blockNames: 'Block4',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-02-14 09:45:00',
      deviceTotal: 0,
      createUser: '张三',
      createTime: '2024-02-14 09:45:00',
    },
    {
      taskNo: 'TASK017',
      productModelNames: 'ModelC',
      blockNames: 'Block5',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 8,
      failureTotal: 2,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-06-01 18:00:00',
      deviceTotal: 10,
      createUser: '李四',
      createTime: '2024-06-01 18:00:00',
    },
    {
      taskNo: 'TASK018',
      productModelNames: 'ModelE',
      blockNames: 'Block1,Block2',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 4,
      failureTotal: 1,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-08-08 08:08:08',
      deviceTotal: 5,
      createUser: '王五',
      createTime: '2024-08-01 08:08:08',
    },
    {
      taskNo: 'TASK019',
      productModelNames: 'ModelA',
      blockNames: 'Block3',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-12-25 20:00:00',
      deviceTotal: 0,
      createUser: '赵六',
      createTime: '2024-12-20 20:00:00',
    },
    {
      taskNo: 'TASK020',
      productModelNames: 'ModelB,ModelE',
      blockNames: 'Block4',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 10,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-10-10 10:10:10',
      deviceTotal: 10,
      createUser: '陈七',
      createTime: '2024-10-10 10:10:10',
    },
    {
      taskNo: 'TASK021',
      productModelNames: 'ModelC,ModelD',
      blockNames: 'Block5',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 6,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-04-18 14:20:00',
      deviceTotal: 6,
      createUser: '张三',
      createTime: '2024-04-10 14:20:00',
    },
  ],
};

// 基于新数据结构生成的mock数据，包含21条记录
const newMockData = {
  pageNum: 1,
  pageSize: 21,
  pages: 1,
  total: 21,
  list: [
    {
      taskNo: 'TASK001',
      detailNo: 'DTL001',
      deviceName: 'Device_001',
      productModelName: 'ModelA-Pro',
      groupLevelName: '华北区/北京/朝阳区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 08:30:00',
      remark: '配置推送成功',
    },
    {
      taskNo: 'TASK002',
      detailNo: 'DTL002',
      deviceName: 'Device_002',
      productModelName: 'ModelB-Standard',
      groupLevelName: '华东区/上海/浦东新区',
      executeStatus: 1,
      executeStatusName: '执行中',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 09:15:00',
      remark: '正在推送配置',
    },
    {
      taskNo: 'TASK003',
      detailNo: 'DTL003',
      deviceName: 'Device_003',
      productModelName: 'ModelC-Lite',
      groupLevelName: '华南区/广州/天河区',
      executeStatus: 3,
      executeStatusName: '失败',
      online: 0,
      onlineName: '离线',
      modifyTime: '2025-01-01 10:20:00',
      remark: '设备离线，推送失败',
    },
    {
      taskNo: 'TASK004',
      detailNo: 'DTL004',
      deviceName: 'Device_004',
      productModelName: 'ModelD-Enterprise',
      groupLevelName: '西南区/成都/高新区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 11:45:00',
      remark: '企业版配置更新完成',
    },
    {
      taskNo: 'TASK005',
      detailNo: 'DTL005',
      deviceName: 'Device_005',
      productModelName: 'ModelE-Basic',
      groupLevelName: '华中区/武汉/江汉区',
      executeStatus: 0,
      executeStatusName: '待执行',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 12:30:00',
      remark: '等待执行配置推送',
    },
    {
      taskNo: 'TASK006',
      detailNo: 'DTL006',
      deviceName: 'Device_006',
      productModelName: 'ModelF-Advanced',
      groupLevelName: '东北区/沈阳/和平区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 13:15:00',
      remark: '高级配置应用成功',
    },
    {
      taskNo: 'TASK007',
      detailNo: 'DTL007',
      deviceName: 'Device_007',
      productModelName: 'ModelG-Premium',
      groupLevelName: '西北区/西安/雁塔区',
      executeStatus: 1,
      executeStatusName: '执行中',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 14:00:00',
      remark: '高端设备配置中',
    },
    {
      taskNo: 'TASK008',
      detailNo: 'DTL008',
      deviceName: 'Device_008',
      productModelName: 'ModelH-Smart',
      groupLevelName: '华北区/天津/滨海新区',
      executeStatus: 3,
      executeStatusName: '失败',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 14:45:00',
      remark: '智能配置验证失败',
    },
    {
      taskNo: 'TASK009',
      detailNo: 'DTL009',
      deviceName: 'Device_009',
      productModelName: 'ModelI-IoT',
      groupLevelName: '华东区/杭州/西湖区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 15:30:00',
      remark: 'IoT设备配置完成',
    },
    {
      taskNo: 'TASK010',
      detailNo: 'DTL010',
      deviceName: 'Device_010',
      productModelName: 'ModelJ-Edge',
      groupLevelName: '华南区/深圳/南山区',
      executeStatus: 0,
      executeStatusName: '待执行',
      online: 0,
      onlineName: '离线',
      modifyTime: '2025-01-01 16:15:00',
      remark: '边缘设备待上线',
    },
    {
      taskNo: 'TASK011',
      detailNo: 'DTL011',
      deviceName: 'Device_011',
      productModelName: 'ModelK-Cloud',
      groupLevelName: '西南区/重庆/渝北区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 17:00:00',
      remark: '云端配置同步成功',
    },
    {
      taskNo: 'TASK012',
      detailNo: 'DTL012',
      deviceName: 'Device_012',
      productModelName: 'ModelL-Mobile',
      groupLevelName: '华中区/长沙/岳麓区',
      executeStatus: 1,
      executeStatusName: '执行中',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 17:45:00',
      remark: '移动设备配置中',
    },
    {
      taskNo: 'TASK013',
      detailNo: 'DTL013',
      deviceName: 'Device_013',
      productModelName: 'ModelM-Secure',
      groupLevelName: '东北区/大连/中山区',
      executeStatus: 3,
      executeStatusName: '失败',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 18:30:00',
      remark: '安全配置校验失败',
    },
    {
      taskNo: 'TASK014',
      detailNo: 'DTL014',
      deviceName: 'Device_014',
      productModelName: 'ModelN-Industrial',
      groupLevelName: '西北区/兰州/城关区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 19:15:00',
      remark: '工业设备配置完成',
    },
    {
      taskNo: 'TASK015',
      detailNo: 'DTL015',
      deviceName: 'Device_015',
      productModelName: 'ModelO-Compact',
      groupLevelName: '华北区/石家庄/长安区',
      executeStatus: 0,
      executeStatusName: '待执行',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 20:00:00',
      remark: '紧凑型设备待配置',
    },
    {
      taskNo: 'TASK016',
      detailNo: 'DTL016',
      deviceName: 'Device_016',
      productModelName: 'ModelP-Wireless',
      groupLevelName: '华东区/南京/鼓楼区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 20:45:00',
      remark: '无线设备配置成功',
    },
    {
      taskNo: 'TASK017',
      detailNo: 'DTL017',
      deviceName: 'Device_017',
      productModelName: 'ModelQ-Hybrid',
      groupLevelName: '华南区/厦门/思明区',
      executeStatus: 1,
      executeStatusName: '执行中',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 21:30:00',
      remark: '混合设备配置进行中',
    },
    {
      taskNo: 'TASK018',
      detailNo: 'DTL018',
      deviceName: 'Device_018',
      productModelName: 'ModelR-Rugged',
      groupLevelName: '西南区/昆明/五华区',
      executeStatus: 3,
      executeStatusName: '失败',
      online: 0,
      onlineName: '离线',
      modifyTime: '2025-01-01 22:15:00',
      remark: '加固设备连接异常',
    },
    {
      taskNo: 'TASK019',
      detailNo: 'DTL019',
      deviceName: 'Device_019',
      productModelName: 'ModelS-Smart',
      groupLevelName: '华中区/郑州/金水区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 23:00:00',
      remark: '智能设备升级完成',
    },
    {
      taskNo: 'TASK020',
      detailNo: 'DTL020',
      deviceName: 'Device_020',
      productModelName: 'ModelT-Thermal',
      groupLevelName: '东北区/哈尔滨/道里区',
      executeStatus: 0,
      executeStatusName: '待执行',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-01 23:45:00',
      remark: '热敏设备待配置',
    },
    {
      taskNo: 'TASK021',
      detailNo: 'DTL021',
      deviceName: 'Device_021',
      productModelName: 'ModelU-Ultra',
      groupLevelName: '西北区/乌鲁木齐/天山区',
      executeStatus: 2,
      executeStatusName: '成功',
      online: 1,
      onlineName: '在线',
      modifyTime: '2025-01-02 00:30:00',
      remark: '超级设备配置完成',
    },
  ],
};
class ConfigTaskManageApi {
  public getDeviceConfigIssueTaskPage = (params: {
    pageNum: number;
    pageSize: number;
    blockNo: string;
    taskStatus: number;
    taskNo: string;
    createTimeStart: string;
    createTimeEnd: string;
    createUser: string;
    productKey: string;
    productModelNo: string;
  }): Promise<any> => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_issue_task/get_device_config_issue_task_page',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: listData,
      message: 'ok',
    });
  };
  public createDeviceConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_issue_task/create_device_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: false,
      message: 'ok',
    });
  };
  public getDeviceConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_device_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: mockDataList,
      message: 'ok',
    });
  };
  public getDeviceConfigDevicePage = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_device_config_device_page',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: newMockData,
      message: 'ok',
    });
  };
  public cancelConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/cancel_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: true,
      message: 'ok',
    });
  };
}

export default ConfigTaskManageApi;
