.structured-config-editor {
  .config-collapse {
    .ant-collapse-header {
      .config-type-header {
        font-weight: 600;
        color: #1677ff;
        font-size: 14px;
      }
    }

    .ant-collapse-content-box {
      padding: 16px 24px;
    }

    .config-item-label {
      font-weight: 500;
      color: #262626;

      .required-mark {
        color: #ff4d4f;
        margin-left: 4px;
      }
    }

    .array-config {
      .array-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .ant-input {
          flex: 1;
        }

        .ant-btn {
          flex-shrink: 0;
        }
      }
    }
  }
}
