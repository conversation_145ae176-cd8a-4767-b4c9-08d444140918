import { FormConfig } from '@/components/CommonForm/formConfig';
import { regex } from '@/utils/utils';

export const SearchFormConfig: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '产品',
      fieldName: 'productKey',
      placeholder: '请选择产品',
      labelInValue: false,
      allowClear: false,
    },
    {
      type: 'select',
      label: '型号',
      fieldName: 'productModelNo',
      placeholder: '请选择型号',
      labelInValue: false,
    },
    {
      type: 'cascader',
      label: '分组',
      fieldName: 'groupNoList',
      placeholder: '请选择分组',
      labelInValue: false,
    },
    {
      type: 'input',
      label: '设备名称',
      fieldName: 'deviceName',
      placeholder: '请输入设备名称',
    },
  ],
};

export const Colunms: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 70,
    render: (text: any, record: any, index: number) => index + 1,
  },
  {
    title: '产品',
    width: 210,
    dataIndex: 'productName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '型号',
    width: 210,
    dataIndex: 'productModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分组',
    width: 210,
    dataIndex: 'groupLevelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备名称',
    width: 210,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备别名',
    width: 210,
    dataIndex: 'remarkName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '注册时间',
    width: 210,
    dataIndex: 'registerTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 150,
    dataIndex: 'operate',
    align: 'center',
    fixed: 'right',
  },
];

export const AddDeviceForm: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '产品',
      validatorRules: [{ required: true, message: '请选择产品' }],
      fieldName: 'productKey',
      placeholder: '请选择产品',
      labelInValue: false,
    },
    {
      type: 'cascader',
      label: '设备分组',
      fieldName: 'groupNo',
      placeholder: '请选择设备分组',
      labelInValue: false,
    },
    {
      type: 'select',
      label: '产品型号',
      validatorRules: [{ required: true, message: '请选择产品型号' }],
      fieldName: 'productModelNo',
      placeholder: '请选择产品型号',
      labelInValue: false,
    },
    {
      type: 'input',
      label: '设备名称',
      fieldName: 'deviceName',
      placeholder: '请输入设备名称',
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const reg = /^[A-Za-z0-9-_]+$/;
            if (!value?.trim()) {
              return Promise.reject(new Error('请输入设备名称'));
            }
            if (reg.test(value?.trim())) {
              return Promise.resolve();
            } else {
              return Promise.reject(new Error('请输入英文，-，_，数字'));
            }
          },
        },
      ],
    },
    {
      type: 'input',
      label: '设备别名',
      fieldName: 'remarkName',
      placeholder: '请输入设备别名',
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            if (!value?.trim()) {
              return Promise.resolve();
            }
            if (regex.test(value?.trim())) {
              return Promise.resolve();
            } else {
              return Promise.reject(
                new Error('请输入中文、英文字母、数字和下划线（_）'),
              );
            }
          },
        },
      ],
    },
  ],
};

export const ConfigChangeTableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 70,
  },
  {
    title: '操作版本号',
    dataIndex: 'operateVersion',
    align: 'center',
  },
  {
    title: '操作类型',
    dataIndex: 'changeTypeName',
    align: 'center',
  },
  {
    title: '引用模板编号',
    dataIndex: 'templateName',
    align: 'center',
  },
  {
    title: '保存时间',
    dataIndex: 'createTime',
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
  },
  {
    title: '是否发版',
    dataIndex: 'issuedName',
    align: 'center',
  },
  {
    title: '发版时间',
    dataIndex: 'issueTime',
    align: 'center',
  },
  {
    title: '发版任务编号',
    dataIndex: 'issueTaskNo',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 250,
    fixed: 'right',
  },
];
