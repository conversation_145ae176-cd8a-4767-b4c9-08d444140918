import React, { useState, useEffect } from 'react';
import { Tabs, Table } from 'antd';
import { useNavigate } from 'react-router-dom';
import { formatLocation } from '@/utils/formatLocation';
import FormTitle from '@/components/FormTitle';
import BreadCrumb from '@/components/BreadCrumb';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { tabMenu, TabType } from '../utils/constants';
import { recordColumns } from '../utils/column';
import CheckRecordModal from '../components/CheckRecordModal';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
import VehicleTypeManageRequest from '@/fetch/bussiness/vehicleTypeManage';
const { TabPane } = Tabs;
const BreadCrumbItemsMap = new Map([
  [
    'vehicleTypeHandleHistory',
    [
      { title: 'OTA管理', route: '' },
      { title: '车型配置管理', route: '' },
      { title: '操作记录', route: '' },
    ],
  ],
]);
const fetchApi = new VehicleTypeManageRequest();
const VehicleTypeHandleHistory = () => {
  const navigator = useNavigate();
  const urlData = formatLocation(window.location.search);
  const [activeTab, setActiveTab] = useState(TabType.CURRENT_RECORD);
  const [tabContent, setTabContent] = useState<any[]>([]);
  const [checkModalShow, setCheckModalShow] = useState<boolean>(false);
  const [conf, setConf] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    fetchVehicleTypeRecord(TabType.CURRENT_RECORD);
  }, [urlData.type, urlData.vehicleTypeId]);

  const fetchVehicleTypeRecord = (isExist: string) => {
    setLoading(true);
    fetchApi
      .getVehicleTypeRecord({
        vehicleTypeId: urlData.vehicleTypeId,
        isExist: isExist,
      })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTabContent(res.data);
          setLoading(false);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const makeColumns = () => {
    return recordColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${index + 1}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    setConf(record);
                    setCheckModalShow(true);
                  }}
                >
                  查看记录
                </a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onTabsChange = (key: any) => {
    setActiveTab(key);
    fetchVehicleTypeRecord(key);
    setTabContent([]);
  };
  return (
    <div className="vehicle-type-handle-history">
      <BreadCrumb items={BreadCrumbItemsMap.get('vehicleTypeHandleHistory')} />
      <div className="history-content">
        <FormTitle title={`${urlData.vehicleTypeName}操作记录`} />
        <div className="card-title">
          <Tabs activeKey={activeTab} onChange={onTabsChange}>
            {tabMenu?.map((item: any) => {
              return <TabPane tab={item.name} key={item.key}></TabPane>;
            })}
          </Tabs>
        </div>
        <div className="card-content">
          <Table
            dataSource={tabContent}
            bordered
            rowKey={(record) => record.version}
            columns={makeColumns()}
            pagination={false}
            loading={loading}
          />
        </div>
        <div className="history-btn">
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            otherCSSProperties={{ marginLeft: '20px' }}
            onSubmitClick={() => navigator('/vehicleTypeManage')}
            title={'返回'}
          />
        </div>
      </div>
      {checkModalShow && (
        <CheckRecordModal
          checkModalShow={checkModalShow}
          onCancel={() => {
            setCheckModalShow(false);
          }}
          conf={conf}
          vehicleName={urlData.vehicleName}
          vehicleTypeId={urlData.vehicleTypeId}
        />
      )}
    </div>
  );
};

export default React.memo(VehicleTypeHandleHistory);
