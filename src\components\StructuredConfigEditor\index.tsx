import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Collapse, Form, Input, InputNumber, Select, DatePicker, Button, message } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './index.scss';

const { Panel } = Collapse;

interface ConfigItem {
  identifier: string;
  name: string;
  dataType: string;
  required: boolean;
  description?: string;
  dataSpecs?: any;
}

interface ConfigType {
  itemTypeNo: string;
  itemTypeName: string;
  itemList: ConfigItem[];
}

interface StructuredConfigEditorProps {
  configTypes: ConfigType[];
  blockNo: string;
  onDataChange?: (blockNo: string, data: Record<string, any>) => void;
}

export interface StructuredConfigEditorRef {
  getConfigData: () => Record<string, any>;
  validateConfig: () => Promise<boolean>;
}

const StructuredConfigEditor = forwardRef<StructuredConfigEditorRef, StructuredConfigEditorProps>(
  ({ configTypes, blockNo, onDataChange }, ref) => {
    const [form] = Form.useForm();
    const [configData, setConfigData] = useState<Record<string, any>>({});
    const [activeKeys, setActiveKeys] = useState<string[]>([]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getConfigData: () => configData,
      validateConfig: async () => {
        try {
          await form.validateFields();
          return true;
        } catch (error) {
          return false;
        }
      },
    }));

    // 初始化展开所有配置类
    useEffect(() => {
      const keys = configTypes.map(type => type.itemTypeNo);
      setActiveKeys(keys);
    }, [configTypes]);

    // 处理配置项值变化
    const handleValueChange = (identifier: string, value: any) => {
      const newData = { ...configData, [identifier]: value };
      setConfigData(newData);
      onDataChange?.(blockNo, newData);
    };

    // 渲染不同类型的配置项
    const renderConfigItem = (item: ConfigItem) => {
      const { identifier, name, dataType, required, description, dataSpecs } = item;

      const commonProps = {
        placeholder: `请输入${name}`,
        value: configData[identifier],
        onChange: (value: any) => handleValueChange(identifier, value),
      };

      switch (dataType) {
        case 'TEXT':
          return (
            <Input
              {...commonProps}
              maxLength={dataSpecs?.maxLength}
              showCount={!!dataSpecs?.maxLength}
            />
          );

        case 'INT':
        case 'LONG':
        case 'DOUBLE':
          return (
            <InputNumber
              {...commonProps}
              style={{ width: '100%' }}
              min={dataSpecs?.min}
              max={dataSpecs?.max}
              step={dataSpecs?.step}
              addonAfter={dataSpecs?.unitName}
              controls={false}
              keyboard={true}
            />
          );

        case 'ENUM':
        case 'BOOL':
        case 'INT_ENUM':
          const options = dataSpecs?.enumList?.map((item: any) => ({
            label: item.name,
            value: item.value,
          })) || [];
          return (
            <Select
              {...commonProps}
              style={{ width: '100%' }}
              options={options}
              placeholder={`请选择${name}`}
            />
          );

        case 'DATE':
          return (
            <DatePicker
              style={{ width: '100%' }}
              placeholder={`请选择${name}`}
              value={configData[identifier] ? dayjs(configData[identifier]) : undefined}
              onChange={(date) => handleValueChange(identifier, date ? date.valueOf() : null)}
            />
          );

        case 'ARRAY':
          // 数组类型的处理比较复杂，这里简化处理
          const arrayValue = configData[identifier] || [];
          return (
            <div className="array-config">
              {arrayValue.map((item: any, index: number) => (
                <div key={index} className="array-item">
                  <Input
                    value={item}
                    onChange={(e) => {
                      const newArray = [...arrayValue];
                      newArray[index] = e.target.value;
                      handleValueChange(identifier, newArray);
                    }}
                    placeholder={`${name} 第${index + 1}项`}
                  />
                  <Button
                    type="link"
                    danger
                    onClick={() => {
                      const newArray = arrayValue.filter((_: any, i: number) => i !== index);
                      handleValueChange(identifier, newArray);
                    }}
                  >
                    删除
                  </Button>
                </div>
              ))}
              <Button
                type="dashed"
                onClick={() => {
                  const newArray = [...arrayValue, ''];
                  handleValueChange(identifier, newArray);
                }}
                style={{ width: '100%', marginTop: 8 }}
              >
                添加项
              </Button>
            </div>
          );

        default:
          return (
            <Input
              {...commonProps}
              placeholder={`请输入${name}`}
            />
          );
      }
    };

    return (
      <div className="structured-config-editor">
        <Form form={form} layout="vertical">
          <Collapse
            activeKey={activeKeys}
            onChange={setActiveKeys}
            expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
            className="config-collapse"
          >
            {configTypes.map((configType) => (
              <Panel
                key={configType.itemTypeNo}
                header={
                  <span className="config-type-header">
                    {configType.itemTypeName}
                  </span>
                }
              >
                {configType.itemList.map((item) => (
                  <Form.Item
                    key={item.identifier}
                    label={
                      <span className="config-item-label">
                        {item.name}
                        {item.required && <span className="required-mark">*</span>}
                      </span>
                    }
                    name={item.identifier}
                    rules={[
                      {
                        required: item.required,
                        message: `请输入${item.name}`,
                      },
                    ]}
                    tooltip={item.description}
                  >
                    {renderConfigItem(item)}
                  </Form.Item>
                ))}
              </Panel>
            ))}
          </Collapse>
        </Form>
      </div>
    );
  }
);

StructuredConfigEditor.displayName = 'StructuredConfigEditor';

export default StructuredConfigEditor;
