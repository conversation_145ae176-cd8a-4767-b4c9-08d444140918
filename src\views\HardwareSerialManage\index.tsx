
import React, { useState, useEffect } from 'react';
import { Form, Modal, Table, Input } from 'antd';
import Searchform from '@/components/Searchform';
import { pageSizeOptions, SearchCondition, TableListType } from '@/utils/constant';
import './index.scss';
import { request } from '@/fetch/core';
import { TableColumns, SearchConfig, detailConfig } from './utils/columns';
import { api } from '@/fetch/core/api';
import { HttpStatusCode } from '@/fetch/core/constant';
import { CustomButton, ButtonType } from '@/components/CustomButton';

const HardwareSerialManage = () => {
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    searchForm: {
      serialNumber: null
    },
    current: 1,
    pageSize: 10
  });
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [formRef] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [form] = Form.useForm();
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  useEffect(() => {
    fetchTableData(searchCondition)
  }, []);
  const fetchTableData = (searchValues: any) => {
    setLoading(true)
    try {
      request({
        method: 'POST',
        path: api.getHardwareSerialNumberPageList,
        body: searchValues.searchForm,
        urlParams: {
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize,
        }
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          })
        }
      })
    } catch (e) {
      console.log(e)
    } finally {
      setLoading(false)
    }
  };
  const formatColumns = () => {
    return TableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${(searchCondition.current - 1) * searchCondition.pageSize + index + 1}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className='operate-btn'>
                <a onClick={() => {
                  fetchDetail(record.id)
                  setModalShow(true)
                }}>查看</a>
              </div>)
          }
          break;
        default:
          col.render = (text: any) => `${text || '-'}`
          break;
      }
      return col
    })
  }
  const onSearchClick = () => {
    const data = {
      searchForm: formRef.getFieldsValue(),
      pageSize: 10,
      current: 1
    }
    setSearchCondition(data)
    fetchTableData(data)
  }
  const onResetClick = () => {
    const search = {
      searchForm: {
        serialNumber: null
      },
      current: 1,
      pageSize: 10
    }
    formRef.setFieldsValue({
      serialNumber: null
    });
    setSearchCondition(search)
    fetchTableData(search)
  }
  const fetchDetail = (id: any) => {
    request({
      method: 'GET',
      path: api.getHardwareSerialNumberDetail,
      urlParams: {
        id: id,
      }
    }).then((res: any) => {
      if (res && res.code === HttpStatusCode.Success) {
        form.setFieldsValue(res.data)
      }
    }).catch((err) => {
      console.log(err)
    })
  }
  return <div className='hardware-serial-manage'>
    <div className='searchform'>
      <Searchform
        configData={SearchConfig}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        initValues={searchCondition.searchForm}
        formRef={formRef}
      />
    </div>
    <div className='table-container'>
      <div style={{ color: '#999999', marginBottom: '10px' }}>{'说明：通过内参标定工具，获取相机序列号。'}</div>
      <Table
        rowKey={(record) => record.id}
        columns={formatColumns()}
        dataSource={tableList.list}
        loading={loading}
        bordered
        scroll={{
          y: 600
        }}
        pagination={{
          position: ["bottomCenter"],
          total: tableList.totalNumber,
          current: searchCondition.current,
          pageSize: searchCondition.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: pageSizeOptions,
          showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`
        }}
        onChange={(paginationData: any, filters: any, sorter: any, extra: any) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            const newSearchValue = {
              ...searchCondition,
              current,
              pageSize
            }
            setSearchCondition(newSearchValue);
            fetchTableData(newSearchValue)
          }
        }}
      />
    </div>
    {
      modalShow && <Modal
        title={'详情'}
        visible={modalShow}
        onCancel={() => {
          setModalShow(false)
        }}
        width={800}
        footer={<div>
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => {
              setModalShow(false)
            }}
            title={'关闭'} />
        </div>}>
        <Form
          {...layout}
          form={form}
        >
          {
            detailConfig?.map((item: any, index: any) => {
              return item.name === 'parameter' ?
                <Form.Item
                  key={index}
                  name={item.name}
                  label={item.label}
                >
                  <Input.TextArea
                    rows={10}
                    disabled />
                </Form.Item> :
                <Form.Item
                  key={index}
                  name={item.name}
                  label={item.label}
                >
                  <Input.TextArea
                    rows={1}
                    disabled />
                </Form.Item>
            })
          }
        </Form>
      </Modal>
    }
  </div>
}

export default React.memo(HardwareSerialManage);