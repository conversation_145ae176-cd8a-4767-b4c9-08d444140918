import React, { useState, useRef } from 'react';
import { Button, message } from 'antd';
import { CommonForm, CommonTable } from '@jd/x-coreui';
import type { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import './index.scss';

const ConfigExample: React.FC = () => {
  // 表单引用
  const formRef = useRef<any>(null);

  // 表格数据状态
  const [tableData, setTableData] = useState<any[]>([
    {
      id: 1,
      identifier: 'test1',
      name: '配置项1',
      module: 'PDA应用',
      creator: 'xuehongwei3',
      createTime: '2024-10-10 16:55:57',
      updateTime: '2024-10-11 16:55:57',
      updater: 'xuehongwei',
      status: '启用'
    },
    {
      id: 2,
      identifier: 'test2',
      name: '配置项2',
      module: 'PDA应用',
      creator: 'qinying22',
      createTime: '2024-10-10 15:55:57',
      updateTime: '2024-10-10 16:55:57',
      updater: 'qinying22',
      status: '禁用'
    }
  ]);

  // 表单加载状态
  const [loading, setLoading] = useState<boolean>(false);

  // 表单配置
  const formConfig: FormConfig = {
    fields: [
      {
        type: 'select',
        label: '产品',
        fieldName: 'product',
        options: [
          { label: 'PDA', value: 'PDA' },
          { label: '手机', value: 'mobile' },
          { label: '平板', value: 'tablet' }
        ]
      },
      {
        type: 'select',
        label: '模块',
        fieldName: 'module',
        placeholder: '请选择',
        options: [
          { label: 'PDA应用', value: 'PDA应用' },
          { label: '系统设置', value: '系统设置' },
          { label: '用户管理', value: '用户管理' }
        ]
      },
      {
        type: 'input',
        label: '配置项标识符',
        fieldName: 'identifier',
        placeholder: '请输入'
      },
      {
        type: 'input',
        label: '配置项名称',
        fieldName: 'name',
        placeholder: '请输入'
      },
      {
        type: 'input',
        label: '创建人',
        fieldName: 'creator',
        placeholder: '请输入创建人账号'
      },
      {
        type: 'select',
        label: '状态',
        fieldName: 'status',
        placeholder: '请选择',
        options: [
          { label: '启用', value: '启用' },
          { label: '禁用', value: '禁用' }
        ]
      },
      {
        type: 'datePicker',
        label: '创建时间',
        fieldName: 'createTimeRange',
        placeholder: '请选择时间'
      }
    ]
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      width: 80,
      align: 'center'
    },
    {
      title: '配置项标识符',
      dataIndex: 'identifier',
      width: 150
    },
    {
      title: '配置项名称',
      dataIndex: 'name',
      width: 150
    },
    {
      title: '模块',
      dataIndex: 'module',
      width: 120
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      width: 120
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 180
    },
    {
      title: '更新人',
      dataIndex: 'updater',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (text: string) => (
        <span style={{ color: text === '启用' ? '#52c41a' : '#ff4d4f' }}>
          {text}
        </span>
      )
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 200,
      render: (_: any, record: any) => (
        <div className="operation-buttons">
          <Button type="link" size="small" onClick={() => handleView(record)}>
            查看
          </Button>
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleToggleStatus(record)}
            style={{ color: record.status === '启用' ? '#ff4d4f' : '#52c41a' }}
          >
            {record.status === '启用' ? '禁用' : '启用'}
          </Button>
          <Button type="link" size="small" danger onClick={() => handleDelete(record)}>
            删除
          </Button>
        </div>
      )
    }
  ];

  // 表格数据对象
  const tableListData = {
    list: tableData,
    totalNumber: tableData.length,
    totalPage: 1
  };

  // 查看配置项
  const handleView = (record: any) => {
    console.log('查看配置项:', record);
    message.info(`查看配置项: ${record.name}`);
  };

  // 编辑配置项
  const handleEdit = (record: any) => {
    console.log('编辑配置项:', record);
    message.info(`编辑配置项: ${record.name}`);
  };

  // 切换状态
  const handleToggleStatus = (record: any) => {
    const newStatus = record.status === '启用' ? '禁用' : '启用';
    console.log(`将配置项 ${record.name} 状态修改为: ${newStatus}`);

    // 更新表格数据
    const newData = tableData.map(item => {
      if (item.id === record.id) {
        return { ...item, status: newStatus };
      }
      return item;
    });

    setTableData(newData);
    message.success(`已${newStatus}配置项: ${record.name}`);
  };

  // 删除配置项
  const handleDelete = (record: any) => {
    console.log('删除配置项:', record);

    // 更新表格数据
    const newData = tableData.filter(item => item.id !== record.id);
    setTableData(newData);

    message.success(`已删除配置项: ${record.name}`);
  };

  // 配置类管理
  const handleConfigTypeManagement = () => {
    console.log('配置类管理');
    message.info('进入配置类管理');
  };

  // 新建结构化配置项
  const handleCreateStructuredConfig = () => {
    console.log('新建结构化配置项');
    message.info('新建结构化配置项');
  };

  return (
    <div className="config-example">
      <div className="search-form">
        <CommonForm
          formConfig={formConfig}
          layout="inline"
          formType="search"
          colon={false}
          onSearchClick={() => {
            const values = formRef.current?.getFieldsValue();
            console.log('查询条件:', values);
            message.success('查询成功');
          }}
          onResetClick={() => {
            formRef.current?.resetFields();
          }}
          getFormInstance={(form: any) => {
            formRef.current = form;
          }}
        />
      </div>

      {/* 中间按钮定义 */}
      <div className="table-actions">
        <CommonTable
          middleBtns={[
            {
              title: '配置类管理',
              btnType: 'primary',
              onClick: handleConfigTypeManagement
            },
            {
              title: '新建结构化配置项',
              btnType: 'primary',
              onClick: handleCreateStructuredConfig
            }
          ]}
        />
      </div>

      <div className="table-container">
        <CommonTable
          columns={columns}
          tableListData={tableListData}
          rowKey="id"
          loading={loading}
        />
      </div>
    </div>
  );
};

export default ConfigExample;
