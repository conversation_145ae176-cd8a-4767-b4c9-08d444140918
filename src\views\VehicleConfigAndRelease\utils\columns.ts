import {
  DropDownType,
  dropDownKey,
  dropDownList<PERSON>ey,
  ClstagKey,
} from '@/utils/searchFormEnum';
import { FormConfig } from '@/components/CommonForm/formConfig';
import { ProductType, ProductTypeName } from '@/utils/constant';

export const TableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 70,
    fixed: 'left',
  },
  {
    title: '车牌号',
    width: 120,
    dataIndex: 'vehicleName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 120,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点',
    width: 180,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆归属方',
    width: 120,
    dataIndex: 'ownerUseCaseName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车型名称',
    width: 150,
    dataIndex: 'vehicleTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属产品',
    width: 150,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属设备',
    width: 150,
    dataIndex: 'vehicleBusinessTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '箱体格口模板',
    width: 150,
    dataIndex: 'boxTemplateName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置发布状态',
    width: 150,
    dataIndex: 'vehicleConfIssueStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 400,
    dataIndex: 'operate',
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const recordColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 25 },
  {
    title: '操作版本号',
    width: 80,
    dataIndex: 'version',
    key: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所在位置',
    width: 40,
    dataIndex: 'position',
    key: 'position',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置文件名称',
    width: 120,
    dataIndex: 'name',
    key: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 60,
    dataIndex: 'modifyUser',
    key: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 90,
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    align: 'center',
    width: 50,
    fixed: 'right',
  },
];

export const SelectVersionTableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 70,
    fixed: 'left',
  },
  {
    title: '系统版本编号',
    width: 230,
    dataIndex: 'versionNumber',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '版本号',
    width: 330,
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '适配车辆类型',
    width: 140,
    dataIndex: 'applyVehicleBusinessTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '描述',
    width: 170,
    dataIndex: 'description',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更新内容',
    width: 170,
    dataIndex: 'updateInfo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分支名',
    width: 140,
    dataIndex: 'branchName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: 'Commit ID',
    width: 120,
    dataIndex: 'commitId',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    width: 160,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  { title: '创建时间', dataIndex: 'modifyTime', align: 'center', width: 180 },
];

export const PublishRecordSearchConfig: any[] = [
  {
    name: 'issueTaskNumber',
    title: '发布计划编号',
    placeHolder: '请输入关键字',
    type: DropDownType.INPUT,
  },
  {
    name: 'appName',
    title: '发布模块',
    placeHolder: '请选择',
    type: DropDownType.SELECT,
  },
  {
    name: 'contemporaryVersionNumber',
    title: '车端版本号',
    placeHolder: '请输入关键字',
    type: DropDownType.INPUT,
  },
  {
    name: 'latestIssueTaskResult',
    title: '车端升级结果',
    placeHolder: '请选择',
    type: DropDownType.SELECT,
    dropDownKey: dropDownKey.issueTaskVehicleStatusList,
    dropDownListKey: dropDownListKey.issueTaskVehicleStatusList,
  },
];

export const PublishRecordTableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '发布计划编号',
    dataIndex: 'issueTaskNumber',
    align: 'center',
    width: 200,
  },
  {
    title: '发版描述',
    dataIndex: 'issueDescription',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '发布模块',
    width: 90,
    dataIndex: 'appAlias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布系统版本编号',
    width: 230,
    dataIndex: 'versionNumber',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布版本号',
    width: 280,
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布分支名',
    width: 180,
    dataIndex: 'branchName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车端系统版本编号',
    width: 250,
    dataIndex: 'contemporaryVersionNumber',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车端版本号',
    width: 200,
    dataIndex: 'contemporaryVersion',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车端升级结果',
    width: 120,
    dataIndex: 'statusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布时间',
    width: 180,
    dataIndex: 'issueTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '升级时间',
    width: 150,
    dataIndex: 'upgradeCompleteTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '下载时间',
    width: 150,
    dataIndex: 'downloadCompleteTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布人',
    width: 130,
    dataIndex: 'issueUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];

export const VehicleConfColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    ellipsis: true,
  },
  {
    title: '操作版本号',
    dataIndex: 'version',
    align: 'center',
    width: 190,
    ellipsis: true,
  },
  {
    title: '所在位置',
    dataIndex: 'position',
    align: 'center',
    width: 95,
    ellipsis: true,
  },
  {
    title: '配置文件名称',
    dataIndex: 'name',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 110,
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 160,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 120,
    fixed: 'right',
    ellipsis: true,
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'stationInfo',
      label: '省市站',
      placeholder: '请选择省市站信息',
      type: 'cascader',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      mapRelation: { label: 'name', value: 'id', children: 'children' },
      specialFetch: 'station',
      showSearch: true,
    },
    {
      fieldName: 'vehicleName',
      label: '车牌号',
      placeholder: '请输入关键字',
      type: 'input',
    },
    {
      fieldName: 'serialNo',
      label: '车架号',
      placeholder: '请输入关键字',
      type: 'input',
    },
    {
      fieldName: 'ownerUseCase',
      label: '车辆归属方',
      placeholder: '请选择车辆归属方',
      type: 'select',
      multiple: true,
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.vehicleOwnerUseCaseList,
      dropDownListKey: dropDownListKey.vehicleOwnerUseCaseList,
    },
    {
      fieldName: 'vehicleConfTemplate',
      label: '车辆配置模板',
      placeholder: '请输入车辆配置模板名称，支持关键字联想全称',
      type: 'select',
    },
    {
      fieldName: 'vehicleConfIssueStatus',
      label: '配置发布状态',
      placeholder: '请选择',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.confInfoStatusList,
      dropDownListKey: dropDownListKey.confInfoStatusList,
    },
    {
      fieldName: 'productType',
      label: '产品类型',
      type: 'select',
      placeholder: '请选择产品类型',
      options: [
        {
          value: ProductType.VEHICLE,
          label: ProductTypeName.VEHICLE,
        },
        {
          value: ProductType.ROBOT,
          label: ProductTypeName.ROBOT,
        },
      ],
    },
    {
      fieldName: 'vehicleBusinessType',
      label: '设备类型',
      type: 'select',
      placeholder: '请选择设备类型',
    },
    {
      fieldName: 'appName',
      label: '车端版本号',
      type: 'select',
      placeholder: '请选择应用',
    },
    {
      fieldName: 'version',
      type: 'select',
      placeholder: '请输入版本号，支持关键字联想全称',
      showSearch: true,
    },
    {
      fieldName: 'hardwareTypeName',
      label: '硬件类型',
      type: 'select',
      placeholder: '请选择硬件类型',
    },
    {
      fieldName: 'hardwareModelId',
      type: 'select',
      placeholder: '请选择硬件型号',
    },
    {
      fieldName: 'vehicleTypeId',
      label: '车型名称',
      placeholder: '请输入车型名称，支持关键字联想全称',
      type: 'select',
      showSearch: true,
    },
    {
      fieldName: 'boxTemplateId',
      label: '箱体格口模板',
      type: 'select',
      placeholder: '请选择箱体格口模板',
    },
  ],
};

export const currentVersionColumns: any[] = [
  {
    title: '应用',
    dataIndex: 'appName',
    align: 'left',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    align: 'left',
    ellipsis: true,
  },
  {
    title: '更新内容',
    dataIndex: 'updateInfo',
    align: 'left',
    ellipsis: true,
  },
  {
    title: '缺陷',
    dataIndex: 'defect',
    align: 'left',
    ellipsis: true,
    width: 120,
  },
  {
    title: '备注信息',
    dataIndex: 'description',
    align: 'left',
    ellipsis: true,
    width: 120,
  },
  {
    title: '扩展信息',
    dataIndex: 'extData',
    align: 'left',
    ellipsis: true,
    width: 120,
  },
];

export const AppInfoColumns: any[] = [
  {
    title: '应用',
    dataIndex: 'appName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
];
