.detail-group-info {
  padding: 12px;
  background-color: #fff;

  .title {
    font-weight: bold;
    margin-bottom: 16px;
    position: relative;
    .goback{
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url(@/assets/images/back.png) no-repeat center;
      background-size: 100%;
      position: absolute;
      right: 20px;
      cursor: pointer;
    }
  }

  .group-info {
    display: flex;
    align-items: center;
    gap: 20%;
    max-width: 100%;
  }

  .group-create-time,
  .group-desc,
  .group-info {
    padding: 10px 4px;
  }

  .group-desc {
    display: flex;
    align-items: center;

    i,
    a {
      font-style: normal;
      word-break: keep-all;

    }

    i {
      margin-right: 10px;
    }

    a {
      color: #1677ff;
      margin-left: 10px;
    }
  }

  .group-tag-list{
    margin: 4px 0;
  }
  .group-tag-item {
    font-style: normal;
    padding: 4px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    margin-left: 6px;
  }
  .tag-item-label{
    font-style: normal;
    margin-left: 2px;
  }
  a{
    color: #1677ff;
    margin-left: 10px;
    cursor: pointer;
  }
}

.device-list {
  padding: 12px;
  background-color: #fff;
  margin-top: 16px;

  .title {
    font-weight: bold;
    margin-bottom: 16px;
  }
}