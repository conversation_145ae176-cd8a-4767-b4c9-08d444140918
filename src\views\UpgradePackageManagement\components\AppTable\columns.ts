import { FormConfig } from '@/components/CommonForm/formConfig';
import { ProductType, ProductTypeName } from '@/utils/constant';

export const AppTableData: any[] = [
  {
    title: '应用ID',
    dataIndex: 'appId',
    align: 'center',
    width: 200,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '应用名称',
    dataIndex: 'appAlias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '英文名',
    dataIndex: 'appName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '负责人',
    dataIndex: 'developer',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 300,
    fixed: 'right',
  },
];

export const AppFormData: FormConfig = {
  fields: [
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请输入关键字',
      type: 'radioGroup',
      specialFetch: 'commonDown',
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请选择所属产品',
        },
      ],
      options: [
        {
          label: ProductTypeName.VEHICLE,
          value: ProductType.VEHICLE,
        },
        {
          label: ProductTypeName.ROBOT,
          value: ProductType.ROBOT,
        },
      ],
    },
    {
      fieldName: 'appAlias',
      label: '应用名称',
      placeholder: '请输入应用名称',
      type: 'input',
      maxLength: 20,
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请填写应用名称',
        },
      ],
    },
    {
      fieldName: 'appName',
      label: '英文名',
      placeholder: '请输入应用英文名',
      type: 'input',
      maxLength: 20,
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请填写应英文名',
        },
      ],
    },
    {
      fieldName: 'developer',
      label: '负责人',
      placeholder: '请输入负责人',
      type: 'input',
      maxLength: 20,
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请填写负责人姓名',
        },
      ],
    },
  ],
};

export const VersionTableData: any[] = [
  {
    title: '版本编号',
    dataIndex: 'versionNumber',
    width: 200,
  },
  {
    title: '应用',
    dataIndex: 'appName',
    ellipsis: true,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    ellipsis: true,
  },
  {
    title: '所属设备',
    dataIndex: 'applyVehicleBusinessTypeName',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    ellipsis: true,
  },
  {
    title: '更新内容',
    dataIndex: 'updateInfo',
    ellipsis: true,
  },
  {
    title: '缺陷',
    dataIndex: 'defect',
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    fixed: 'right',
    ellipsis: true,
  },
  {
    title: '扩展信息',
    dataIndex: 'extData',
    align: 'center',
    fixed: 'right',
    ellipsis: true,
  },
  {
    title: '更新时间',
    dataIndex: 'modifyTime',
    fixed: 'right',
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    fixed: 'right',
    width: 200,
  },
];

export const AppPackageForm: FormConfig = {
  fields: [
    {
      fieldName: 'appName',
      label: '应用',
      type: 'input',
      disabled: true,
    },
    {
      fieldName: 'productTypeName',
      label: '所属产品',
      type: 'input',
      disabled: true,
    },
    {
      fieldName: 'applyVehicleBusinessType',
      label: '所属设备',
      type: 'radioGroup',
    },
    {
      fieldName: 'version',
      label: '版本号',
      type: 'input',
      placeholder: '请填写版本号',
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[a-zA-Z0-9._-]+$/;
            const result = regex.test(value);
            if (result) {
              return Promise.resolve();
            } else {
              return Promise.reject(new Error('请输入英文、数字、.、-、_'));
            }
          },
        },
      ],
    },
    {
      fieldName: 'packageType',
      label: '应用包格式',
      type: 'select',
      placeholder: '请选择应用包格式',
      validatorRules: [
        {
          required: true,
          message: '请选择应用包格式',
        },
      ],
      options: [
        {
          label: 'apk',
          value: 'apk',
        },
        {
          label: 'zip',
          value: 'zip',
        },
      ],
    },
    {
      fieldName: 'file',
      label: '上传应用包',
      placeholder: '上传升级包',
      type: 'ReactNode',
    },
    {
      fieldName: 'updateInfo',
      label: '更新内容',
      type: 'textarea',
      maxLength: 1000,
    },
    {
      fieldName: 'defect',
      label: '缺陷',
      type: 'textarea',
      maxLength: 1000,
    },
    {
      fieldName: 'description',
      label: '备注信息',
      type: 'textarea',
      maxLength: 500,
    },
    {
      fieldName: 'extData',
      label: '扩展信息',
      type: 'textarea',
      maxLength: 1000,
    },
  ],
};
