import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  DeviceSearchForm,
  DeviceTableColumns,
  UpgradeRule,
  UpgradeScopeForm,
} from '../../utils/constant';
import './index.scss';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import CommandControlFetch from '@/fetch/bussiness/commandControl';
import FileUpload from '../FileUpload';
import { flat, isEmpty } from '@/utils/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
} from '@/utils/emit';
const commonApi = new CommandControlFetch();
const UpgradeScope = forwardRef(
  (
    props: {
      productKey: string;
      modelList: any[];
      deviceChoiceType: number;
      updateSelectDevice: (selectKeys: any[], total?: number) => void;
      updateDeviceChoiceType: (value: number) => void;
    },
    ref: any,
  ) => {
    const initSearchCondition = {
      productKey: null,
      productModelNoList: null,
      groupNoList: null,
      appType: null,
      appName: null,
      appVersionNumber: null,
      deviceName: null,
      pageNum: 1,
      pageSize: 10,
      hasSearched: false,
    };
    const createOTATask = useSelector(
      (state: RootState) => state.createOTATask,
    );
    const conditionRef = useRef<any>(null);
    const deviceChoiceTypeRef = useRef<any>(null);
    const [searchCondition, setSearchCondition] =
      useState<any>(initSearchCondition);
    const [uploadResult, setUploadResult] = useState<any>(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const [autoFetch, setAutoFetch] = useState<boolean>(false);
    const { tableData, loading, reloadTable } = useTableData(
      searchCondition,
      commonApi.getDeviceList,
      'upgradeScope',
      autoFetch,
    );
    const onSearchClick = (values: any) => {
      const { groupNoList, ...otherData } = values || {};
      const groupList = new Set(flat(groupNoList) || []);
      setSearchCondition({
        ...searchCondition,
        ...otherData,
        groupNoList: [...groupList],
        hasSearched: true,
        pageNum: 1,
        pageSize: 10,
      });
      setSelectedRowKeys([]);
      if (props.deviceChoiceType === UpgradeRule.CONDITIONAL) {
        props.updateSelectDevice([], tableData?.total);
      } else {
        props.updateSelectDevice([]);
      }
    };
    const onResetClick = () => {
      const resetValue: any = {
        ...initSearchCondition,
        productKey: props.productKey,
        productModelNoList: props.modelList,
      };
      setSearchCondition(resetValue);
      setSelectedRowKeys([]);
      props.updateSelectDevice([]);
      conditionRef.current.setFieldsValue(resetValue);
    };
    const formatColumns = (columns: any[]) => {
      return columns.map((col: any) => {
        switch (col.dataIndex) {
          case 'order':
            col.render = (text: any, record: any, index: number) =>
              `${
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              }`;
            break;
        }

        return col;
      });
    };

    const getUploadResult = () => {
      return uploadResult;
    };

    const getConditionFormValues = () => {
      return {
        ...searchCondition,
      };
    };

    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onSelect: (record: any, selected: boolean) => {
        if (selected) {
          const newSelectedRowKeys = [...selectedRowKeys, record.deviceName];
          setSelectedRowKeys(newSelectedRowKeys);
          props.updateSelectDevice(newSelectedRowKeys);
        } else {
          const newSelectedRowKeys = selectedRowKeys.filter(
            (v: string) => v !== record.deviceName,
          );
          setSelectedRowKeys(newSelectedRowKeys);
          props.updateSelectDevice(newSelectedRowKeys);
        }
      },
      onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
        if (selected) {
          const set1 = new Set(selectedRowKeys);
          changeRows.forEach((v: any) => {
            set1.add(v.deviceName);
          });
          setSelectedRowKeys([...set1]);
          props.updateSelectDevice([...set1]);
        } else {
          const arr2 = selectedRowKeys.filter((v) => {
            return !changeRows.some((i: any) => i.deviceName === v);
          });
          setSelectedRowKeys([...arr2]);
          props.updateSelectDevice([...arr2]);
        }
      },
    };

    useImperativeHandle(ref, () => {
      return {
        getUploadResult,
        getConditionFormValues,
      };
    });
    useEffect(() => {
      if (props.modelList && props.productKey) {
        conditionRef.current.setFieldsValue({
          productKey: props.productKey,
          productModelNoList: props.modelList,
        });

        setSearchCondition({
          ...searchCondition,
          productKey: props.productKey,
          productModelNoList: props.modelList,
        });
        setAutoFetch(true);
      }
    }, [props.productKey, props.modelList?.toString(), props.deviceChoiceType]);

    useEffect(() => {
      deviceChoiceTypeRef.current.setFieldValue(
        'deviceChoiceType',
        props.deviceChoiceType,
      );

      if (props.deviceChoiceType === UpgradeRule.DIRECTIONAL) {
        props.updateSelectDevice(selectedRowKeys);
      }
    }, [props.deviceChoiceType]);

    useEffect(() => {
      if (
        props.deviceChoiceType === UpgradeRule.CONDITIONAL &&
        searchCondition.hasSearched
      ) {
        props.updateSelectDevice([], tableData?.total);
      }
    }, [tableData?.total]);

    useEffect(() => {
      if (createOTATask.info) {
        const { productKey, productModelNoList, deviceNameList } =
          createOTATask.info;
        const searchValue = {
          ...searchCondition,
          productKey,
          productModelNoList,
        };
        conditionRef.current.setFieldsValue({
          productKey,
          productModelNoList,
        });
        setSearchCondition(searchValue);
        if (!isEmpty(deviceNameList)) {
          setSelectedRowKeys(deviceNameList);
          props.updateSelectDevice(deviceNameList);
        }
      }
    }, [createOTATask.info]);

    useEffect(() => {
      const cb = () => {
        setSelectedRowKeys([]);
        props.updateSelectDevice([]);
        props.updateDeviceChoiceType(0);
        const initValue = {
          ...initSearchCondition,
          productKey: props.productKey,
          productModelNoList: props.modelList,
        };
        setSearchCondition(initValue);
        conditionRef.current?.setFieldsValue(initValue);
      };
      addGlobalEventListener('PRODUCT_HAS_CHANGED', cb);
      return () => {
        removeGlobalEventListener('PRODUCT_HAS_CHANGED', cb);
      };
    }, [props.productKey, props.modelList?.toString()]);
    const renderChildCmp = () => {
      switch (props.deviceChoiceType) {
        case 0:
        case 2:
          return (
            <div style={{ position: 'relative' }}>
              <CommonForm
                defaultValue={{
                  productKey: createOTATask.info
                    ? createOTATask.info.productKey
                    : props.productKey,
                  productModelNoList: createOTATask.info
                    ? createOTATask.info.productModelNoList
                    : props.productKey,
                  fetchProductKey: true,
                }}
                formConfig={DeviceSearchForm}
                formType="search"
                layout="inline"
                getFormInstance={(ref: any) => {
                  conditionRef.current = ref;
                }}
                onSearchClick={onSearchClick}
                onResetClick={onResetClick}
              />

              <CommonTable
                searchCondition={searchCondition}
                columns={formatColumns(DeviceTableColumns)}
                tableListData={{
                  list: tableData?.list || [],
                  totalPage: tableData?.pages,
                  totalNumber: tableData?.total,
                }}
                onPageChange={(paginationData: any) => {
                  const val = {
                    ...searchCondition,
                    pageNum: paginationData.pageNum,
                    pageSize: paginationData.pageSize,
                  };
                  setSearchCondition(val);
                }}
                rowKey={'deviceName'}
                rowSelection={props.deviceChoiceType != 2 ? rowSelection : null}
              />
              <span
                className="select-num"
                style={{
                  position: 'absolute',
                  fontSize: '14px',
                  left: '20px',
                  bottom: '36px',
                  fontFamily: 'PingFang SC',
                  fontWeight: 'normal',
                  color: 'rgb(153, 153, 153)',
                }}
              >
                已选择
                {props.deviceChoiceType != 2
                  ? selectedRowKeys.length
                  : searchCondition.hasSearched
                  ? tableData?.total
                  : 0}
                ，共{tableData?.total}
              </span>
            </div>
          );
        case 1:
          return (
            <FileUpload
              productKey={props.productKey}
              modelList={props.modelList}
              uploadChange={setUploadResult}
            />
          );
        default:
          break;
      }
    };
    return (
      <div className="upgrade-scope">
        <CommonForm
          defaultValue={props.deviceChoiceType}
          getFormInstance={(ref: any) => {
            deviceChoiceTypeRef.current = ref;
          }}
          layout="inline"
          formConfig={UpgradeScopeForm}
          onValueChange={(allValues: any, changedFieldName: any) => {
            props.updateDeviceChoiceType(allValues?.deviceChoiceType);
          }}
        />
        {renderChildCmp()}
      </div>
    );
  },
);

export default UpgradeScope;
