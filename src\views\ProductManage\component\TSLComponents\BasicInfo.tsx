import { Form, Input, Radio, FormInstance } from 'antd';
import React, { useEffect } from 'react';
import { rwFlagOptions } from '../../utils/column';
import { TSLFunctionType } from '@/utils/constant';

// 该组件包含物模型基础信息，属性的基础信息有描述、读写属性；服务的基础信息有功能名称、标识符、描述
const BasicInfo = ({
  formRef,
  functionType,
  basicInfo,
  pageType,
}: {
  formRef: FormInstance;
  functionType: TSLFunctionType;
  basicInfo: any;
  pageType: 'add' | 'check' | 'edit';
}) => {
  const disabled = pageType === 'check';

  useEffect(() => {
    formRef.setFieldsValue(basicInfo);
  }, []);

  const renderPropertyBasic = () => {
    return (
      <>
        <Form.Item
          label="读写类型"
          name="rwFlag"
          rules={[{ required: true, message: '标识符不能为空' }]}
        >
          <Radio.Group options={rwFlagOptions} disabled={disabled} />
        </Form.Item>
        <Form.Item label="描述" name="description">
          <Input.TextArea
            placeholder="请输入描述"
            showCount
            maxLength={100}
            disabled={disabled}
            style={{ height: 120, resize: 'none' }}
          />
        </Form.Item>
      </>
    );
  };
  const renderServerBasic = () => {
    return (
      <>
        <Form.Item
          label="功能名称"
          name="name"
          rules={[
            { required: true, message: '该名称不能为空' },
            {
              pattern:
                /^[\u4e00-\u9fa5a-zA-Z0-9][\u4e00-\u9fa5a-zA-Z0-9\-_\/.]*$/g,
              message:
                '支持中文、大小写字母、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头，不超过 30 个字符',
            },
          ]}
        >
          <Input
            maxLength={30}
            placeholder="请输入您的功能名称"
            disabled={disabled}
          />
        </Form.Item>
        <Form.Item
          label="标识符"
          name="identifier"
          rules={[
            { required: true, message: '标识符不能为空' },
            {
              pattern: /^[a-zA-Z0-9_]+$/g,
              message: '支持大小写字母、数字和下划线、不超过 50 个字符。',
            },
          ]}
        >
          <Input
            maxLength={50}
            placeholder="请输入您的标识符"
            disabled={disabled}
          />
        </Form.Item>
        <Form.Item label="描述" name="description">
          <Input.TextArea
            placeholder="请输入描述"
            showCount
            maxLength={100}
            disabled={disabled}
            style={{ height: 120, resize: 'none' }}
          />
        </Form.Item>
      </>
    );
  };

  return (
    <Form labelCol={{ span: 2 }} wrapperCol={{ span: 8 }} form={formRef}>
      {functionType === TSLFunctionType.Properties && renderPropertyBasic()}
      {functionType === TSLFunctionType.Server && renderServerBasic()}
    </Form>
  );
};

export default React.memo(BasicInfo);
