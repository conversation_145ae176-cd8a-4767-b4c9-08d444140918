import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { TableColumns, SearchConfig } from './utils/columns';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';
import { HttpStatusCode } from '@/fetch/core/constant';
import { getTaskList, Device, cancelTask } from '@/fetch/bussiness';
import { RootState } from '@/redux/store';
import CommonConfirmBtn from '@/components/CommonConfirmBtn';
import { CommonTable, useTableData, CommonForm } from '@jd/x-coreui';
import './index.scss';
import { formatDateToSecond } from '@/utils/formatTime';

const ReleasePlanManagement = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const deviceApi = new Device();
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );
  const initSearchCondition = useRef<any>({
    searchForm: {
      productKey: null,
      productModelNo: null,
      appName: null,
      appVersionNumber: null,
      issueTaskNumber: null,
      issueTaskStatus: null,
      createUser: null,
      time: null,
      startTime: null,
      endTime: null,
      appType: null,
    },
    pageNum: 1,
    pageSize: 10,
  });
  const defaultSearchVals = useRef<any>(
    historySearchValue?.searchValues
      ? historySearchValue?.searchValues
      : initSearchCondition.current,
  );
  const [searchCondition, setSearchCondition] = useState<any>(
    defaultSearchVals.current,
  );
  const [formatSearchConfig, setFormatSearchConfig] = useState(SearchConfig);
  const searchFormRef = useRef<any>(null);
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    getTaskList,
    'releasePlan',
    autoFetch,
  );

  useEffect(() => {
    formatSearch();
  }, []);
  const formatSearch = async () => {
    const res = await deviceApi.queryProductList();
    let productOptions: any[] = [];
    if (res.code === HttpStatusCode.Success) {
      productOptions = res.data?.map((v: any) => ({
        label: v.productName,
        value: v.productKey,
      }));
      initSearchCondition.current = {
        ...initSearchCondition.current,
        searchForm: {
          ...initSearchCondition.current.searchForm,
          productKey: productOptions[0].value,
        },
      };
      defaultSearchVals.current = historySearchValue?.searchValues
        ? historySearchValue?.searchValues
        : initSearchCondition.current;
      setSearchCondition(defaultSearchVals.current);
      setAutoFetch(true);
    }
    const val = {
      ...SearchConfig,
      fields: formatSearchConfig.fields.map((v) => {
        if (v.fieldName === 'productKey') {
          v = {
            ...v,
            options: productOptions,
          };
        }
        return v;
      }),
    };
    setFormatSearchConfig(val);
  };

  const formatColumns = () => {
    return TableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    navigator(
                      '/releasePlan/detail?issueNumber=' + record.number,
                    );
                  }}
                >
                  升级详情
                </a>
                {record.issueStatus == 0 && (
                  <CommonConfirmBtn
                    message="确认取消该升级计划么?"
                    btnText="取消"
                    onConfirm={() => handleCancel(record.number)}
                  />
                )}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const handleCancel = async (issueTaskNumber: any) => {
    const res = await cancelTask({ issueTaskNumber });
    if (res.code === HttpStatusCode.Success) {
      message.success('取消成功');
      reloadTable();
    } else {
      message.error(res.message);
    }
  };
  const onSearchClick = (val: any) => {
    const formateTime = formatDateToSecond(val.time);
    delete val.time;
    const newValue = {
      searchForm: {
        ...val,
        startTime: formateTime.startTime,
        endTime: formateTime.endTime,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(newValue);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newValue,
      }),
    );
  };
  const onResetClick = () => {
    defaultSearchVals.current = initSearchCondition.current;
    setSearchCondition(initSearchCondition.current);
    searchFormRef.current?.setFieldsValue(
      initSearchCondition.current.searchForm,
    );
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition.current,
      }),
    );
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建发布计划',
      key: 'add-task',
      onClick: () => navigator('/releasePlan/create'),
    },
  ];

  return (
    <div className="release-plan-page">
      <CommonForm
        name="release-plan-searchForm"
        formConfig={formatSearchConfig}
        layout={'inline'}
        defaultValue={defaultSearchVals.current.searchForm}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        getFormInstance={(ref: any) => {
          searchFormRef.current = ref;
        }}
      />

      <CommonTable
        searchCondition={searchCondition}
        loading={loading}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        middleBtns={middleBtns}
        columns={formatColumns()}
        rowKey={'number'}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      ></CommonTable>
    </div>
  );
};

export default React.memo(ReleasePlanManagement);
