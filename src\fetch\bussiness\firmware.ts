import { request } from '@/fetch/core';

class FirmwareFetch {
  getTableList = (params: {
    pageNum: number;
    pageSize: number;
    searchForm: any;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_page_list',
      body: {
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        productKey: params.searchForm?.productKey?.value,
        type: params.searchForm?.type,
      },
      newGeteway: true,
    };
    return request(requestOptions);
  };

  createApp = (params: {
    productKey: string;
    type: string;
    name: string;
    alias: string;
    developer: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/create_application',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };

  editApp = (params: {
    productKey: string;
    type: string;
    name: string;
    alias: string;
    developer: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/edit_application',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };

  getAppInfo = (params: {
    productKey: string;
    type: string;
    name: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_info',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };

  enableApp = (params: {
    productKey: string;
    type: string;
    name: string;
    enable: 0 | 1;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/change_application_enable',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };

  getVersionList = (params: {
    productKey: string;
    type: string;
    appName: string;
    enable?: 0 | 1 | null;
    productModelNoList?: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_version_list',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };

  getAppList = (params: {
    productKey: string;
    type: string;
    enable: 0 | 1 | null;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_list',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };

  getVersionInfoList = (params: {
    pageNum: number | string;
    pageSize: number | string;
    productKey: any;
    type: string;
    appName: any;
    productModelNo: any;
    appVersionNumber: any;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_version_page_list',
      newGeteway: true,
      body: {
        ...params,
        appName: params.appName?.value,
        productKey: params.productKey?.value,
        productModelNo: params.productModelNo?.value,
        appVersionNumber: params.appVersionNumber?.value,
      },
    };
    return request(requestOptions);
  };

  enableAppVersion = (params: {
    productKey: string;
    type: string;
    appName: string;
    appVersionNumber: string;
    enable: 0 | 1;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/change_application_version_enable',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };
  availableVersion = (params: {
    productKey: string;
    type: string;
    appName: string;
    appVersionNumber: string;
    available: 0 | 1;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/change_application_version_available',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };
  delAppVersion = (params: {
    productKey: string;
    type: string;
    appName: string;
    appVersionNumber: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/delete_application_version',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };
  getAppVersionInfo = (params: {
    productKey: string;
    type: string;
    appName: string;
    appVersionNumber: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_version_info',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };
  getPackageType = (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_package_type_list',
      newGeteway: true,
    };
    return request(requestOptions);
  };

  createAppVersion = (params: any): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/create_application_version',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  editAppVersion = (params: any): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/edit_application_version_info',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  downloadVersionPackage = (params: {
    productKey: string;
    type: string;
    appName: string;
    appVersionNumber: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/app/get_application_package_download_url',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  getPushDetailDevice = (params: {
    pageNum: number;
    pageSize: number;
    productKey: string;
    appType: string;
    appName: string;
    appVersionNumber: string;
    groupNoList: any[];
    online: any;
    issueDeviceStatusList: string[];
    deviceName: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/issue/get_issue_application_version_device_page_list',
      body: {
        ...params,
        issueDeviceStatusList: params.issueDeviceStatusList?.map(
          (v: any) => v.value,
        ),
        online: params.online?.value,
      },
      newGeteway: true,
    };
    return request(requestOptions);
  };
  getDeviceUpgradeStatus = (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/issue/get_issue_device_status_list',
      newGeteway: true,
    };
    return request(requestOptions);
  };
  getDeviceOnlineStatus = (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/device/get_device_online_list',
      newGeteway: true,
    };
    return request(requestOptions);
  };
  countUpgradeStatus = (params: {
    productKey: string;
    appType: string;
    appName: string;
    appVersionNumber: string;
    groupNoList: string[];
    online: string;
    issueDeviceStatusList: string[];
    deviceName: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/issue/get_issue_application_version_device_count',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };
  handleStopDevice = (params: {
    productKey: string;
    appType: string;
    appName: string;
    appVersionNumber: string;
    issueDeviceIdList: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/issue/stop_app_issue_device',
      newGeteway: true,
      body: params,
    };
    return request(requestOptions);
  };
}

export default FirmwareFetch;
