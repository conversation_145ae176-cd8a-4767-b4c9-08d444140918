import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { CommonForm } from '@jd/x-coreui';
import { message } from 'antd';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

interface ModuleOption {
  productModelNo: string;
  productModelName: string;
}

interface ProductModuleSelectorProps {
  selectedData?: any;
  onChange?: (data: any) => void;
  step?: 'first' | 'second'; // 区分第一步和第二步（向后兼容）
  needFormConfig?: boolean; // 是否需要表单配置，默认true
  formConfig?: any; // 表单配置，从父组件传入
  formType?: 'search' | 'edit'; // 表单类型
  title?: string; // 自定义标题
}

export interface ProductModuleSelectorRef {
  getSelectedData: () => Promise<{
    productKey: string;
    selectedModules: string[];
    templateName?: string;
    productModelNoList?: string[];
    remark?: string;
  }>;
}

// 第一步：产品选择表单配置
const FirstStepFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      labelInValue: false,
      validatorRules: [{ required: true, message: '请选择产品' }],
      style: { width: 300 },
      allowClear: true,
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const deviceApi = new Device();
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return (
              res.data?.map((item: any) => ({
                label: item.productName,
                value: item.productKey,
              })) || []
            );
          }
          return [];
        },
      },
    ],
  },
};

// 第二步：模板信息表单配置
const SecondStepFormConfig = {
  fields: [
    {
      fieldName: 'templateName',
      label: '模板名称',
      type: 'input',
      placeholder: '请输入模板名称',
      validatorRules: [
        { required: true, message: '请输入模板名称' },
        { max: 50, message: '模板名称不能超过50个字符' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
          message: '只支持中文、字母、数字、中划线、下划线',
        },
      ],
      maxLength: 50,
      showCount: true,
    },
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      disabled: true,
      labelInValue: false,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      placeholder: '请选择型号',
      multiple: true,
      labelInValue: false,
      validatorRules: [{ required: true, message: '请选择型号' }],
    },
    {
      fieldName: 'remark',
      label: '备注',
      type: 'textArea',
      placeholder: '请输入备注',
      maxLength: 150,
      showCount: true,
      autoSize: { minRows: 3, maxRows: 5 },
      validatorRules: [{ max: 150, message: '备注不能超过150个字符' }],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) return [];
          const deviceApi = new Device();
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          }
          return [];
        },
      },
    ],
  },
};

const ProductModuleSelector = forwardRef<
  ProductModuleSelectorRef,
  ProductModuleSelectorProps
>(
  (
    {
      selectedData,
      onChange,
      step = 'first',
      needFormConfig = true,
      formConfig: propFormConfig,
      formType = 'search',
      title = '选择产品及模块',
    },
    ref,
  ) => {
    const deviceApi = new Device();
    const formRef = React.useRef<any>(null);

    const [moduleOptions, setModuleOptions] = useState<ModuleOption[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<string>();
    const [selectedModules, setSelectedModules] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);

    // 确定使用的表单配置
    const finalFormConfig =
      propFormConfig ||
      (step === 'second' ? SecondStepFormConfig : FirstStepFormConfig);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getSelectedData: async () => {
        if (!selectedProduct) {
          throw new Error('请选择产品');
        }
        if (
          needFormConfig &&
          step === 'first' &&
          selectedModules.length === 0
        ) {
          throw new Error('请选择至少一个模块');
        }

        const baseData = {
          productKey: selectedProduct,
          selectedModules,
        };

        // 如果需要表单配置且有表单引用，获取表单数据
        if (needFormConfig && formRef.current) {
          try {
            const formValues = await formRef.current?.getFieldsValue();
            return {
              ...baseData,
              ...formValues, // 返回所有表单字段
            };
          } catch (error) {
            // 如果表单验证失败，仍然返回基础数据
            return baseData;
          }
        }

        return baseData;
      },
    }));

    // 当选中数据变化时，同步表单
    useEffect(() => {
      if (selectedData?.productKey) {
        setSelectedProduct(selectedData.productKey);
        formRef.current?.setFieldsValue({
          productKey: selectedData.productKey,
        });
        fetchModuleList(selectedData.productKey);
      }
      if (selectedData?.productModelNoList) {
        setSelectedModules(selectedData.productModelNoList);
      }
    }, [selectedData]);

    const fetchModuleList = async (productKey: string) => {
      setLoading(true);
      try {
        const res = await deviceApi.queryModelList(productKey);
        if (res.code === HttpStatusCode.Success) {
          setModuleOptions(res.data || []);
        } else {
          message.error(res.message || '获取模块列表失败');
          setModuleOptions([]);
        }
      } catch (error) {
        message.error('获取模块列表失败');
        setModuleOptions([]);
      } finally {
        setLoading(false);
      }
    };

    const handleFormChange = (values: any) => {
      if (values.productKey !== selectedProduct) {
        setSelectedProduct(values.productKey);
        setSelectedModules([]);
        setModuleOptions([]);
        if (values.productKey) {
          fetchModuleList(values.productKey);
        }

        // 通知父组件数据变化
        if (onChange) {
          onChange({
            productKey: values.productKey,
            selectedModules: [],
            ...values, // 传递所有表单值
          });
        }
      }
    };

    const handleModuleClick = (moduleNo: string) => {
      const newSelectedModules = selectedModules.includes(moduleNo)
        ? selectedModules.filter((item) => item !== moduleNo)
        : [...selectedModules, moduleNo];

      setSelectedModules(newSelectedModules);

      // 通知父组件数据变化
      if (onChange) {
        const baseData = {
          productKey: selectedProduct,
          selectedModules: newSelectedModules,
        };

        // 如果有表单，获取表单数据
        if (needFormConfig && formRef.current) {
          try {
            const formValues = formRef.current?.getFieldsValue();
            onChange({
              ...baseData,
              ...formValues,
            });
          } catch (error) {
            onChange(baseData);
          }
        } else {
          onChange(baseData);
        }
      }
    };

    const renderModuleTabs = () => {
      if (moduleOptions.length === 0) {
        return <div className="no-modules">请先选择产品</div>;
      }

      return (
        <div className="module-tabs">
          <div className="module-tabs-header">
            <span className="label">模块选择：</span>
            <span className="tip">（点击选择模块，支持多选）</span>
          </div>
          <div className="module-tabs-content">
            {moduleOptions.map((module) => (
              <div
                key={module.productModelNo}
                className={`module-tab ${
                  selectedModules.includes(module.productModelNo)
                    ? 'selected'
                    : ''
                }`}
                onClick={() => handleModuleClick(module.productModelNo)}
              >
                {module.productModelName}
              </div>
            ))}
          </div>
          {selectedModules.length > 0 && (
            <div className="selected-info">
              已选择 {selectedModules.length} 个模块
            </div>
          )}
        </div>
      );
    };

    return (
      <div className="product-module-selector">
        <div className="section-title">{title}</div>
        <div className="section-content">
          {needFormConfig && (
            <CommonForm
              formConfig={finalFormConfig}
              onRef={(ref: any) => (formRef.current = ref)}
              formType={formType}
              layout="vertical"
              defaultValue={{
                fetchProductKey: true,
                productKey: selectedData?.productKey,
                templateName: selectedData?.templateName,
                productModelNoList: selectedData?.productModelNoList,
                remark: selectedData?.remark,
                ...selectedData, // 支持更多字段
              }}
              onValuesChange={handleFormChange}
            />
          )}

          {renderModuleTabs()}
        </div>
      </div>
    );
  },
);

ProductModuleSelector.displayName = 'ProductModuleSelector';

export default ProductModuleSelector;
