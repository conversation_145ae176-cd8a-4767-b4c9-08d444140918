import { <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import React, { useState, useEffect } from 'react';
import './index.scss';
import { useNavigate, useLocation } from 'react-router-dom';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import { HttpStatusCode } from '@/fetch/core/constant';
import ExportTSL from '../component/ExportTSL';
import BlockPart from '../component/BlockPart';
import TSLPart from '../component/TSLPart';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { formatLocation } from '@/utils/formatLocation';

const TSLModel = () => {
  const fetchApi = new TSLModelFetch();
  const navigator = useNavigate();
  const { productKey } = formatLocation(window.location.search);
  const [detail, setDetail] = useState<any>({});
  const [blockList, setBlockList] = useState<any>([]);
  const [selectBlock, setSelectBlock] = useState<any>({});

  useEffect(() => {
    getThingModelBlocks();
    getThingModelDetails();
  }, []);

  const getThingModelDetails = async () => {
    const res = await fetchApi.getThingModelDetails({ productKey });
    if (res.code === HttpStatusCode.Success) {
      setDetail(res.data);
    }
  };

  const getThingModelBlocks = async () => {
    const res = await fetchApi.getThingModelBlocks({ productKey });
    if (res.code === HttpStatusCode.Success) {
      setBlockList(
        res.data.map((v: any) => ({
          label: v.blockName,
          value: v.blockNo,
        })),
      );
      setSelectBlock({
        label: res.data[0]?.blockName,
        value: res.data[0]?.blockNo,
      });
    }
  };

  const handleSelectBlock = (value: any) => {
    setSelectBlock(value);
  };

  const goBack = () => {
    navigator('/product');
  };

  return (
    <div className="TSL-model">
      <div className="cur-model-info">
        <div className="back" onClick={goBack}>
          <ArrowLeftOutlined />
          <span>物模型</span>
        </div>
        <Row>
          <Col span={8}>产品名称：{detail?.productName}</Col>
          <Col span={8}>产品标识：{detail?.productKey}</Col>
          <Col span={8}>创建时间：{detail?.productCreateTime}</Col>
        </Row>
        <Row>
          <Col span={8}>当前版本号：{detail?.version}</Col>
          <Col span={8}>当前版本创建时间：{detail?.releaseTime}</Col>
        </Row>
        <Row>
          <Col span={24}>描述：{detail?.description}</Col>
        </Row>
      </div>
      <div className="cur-tsl">
        <div className="top-btns">
          <Button
            type="primary"
            onClick={() =>
              navigator(`/product/TSLDraft?productKey=${productKey}`)
            }
          >
            草稿箱
          </Button>
          <ExportTSL blockList={blockList} productKey={productKey} version="" />
        </div>
        <div className="body">
          <BlockPart
            type="check"
            blockList={blockList}
            selectBlock={selectBlock}
            productKey={productKey}
            handleSelectBlock={handleSelectBlock}
          />
          <TSLPart
            type="check"
            selectBlock={selectBlock}
            productKey={productKey}
            version=""
            backUrl="/product/TSLModel"
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(TSLModel);
