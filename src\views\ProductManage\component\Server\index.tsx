import { Col, Form, Input, Radio, Row, Select, Flex, message } from 'antd';
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
} from 'react';
import BasicInfo from '../TSLComponents/BasicInfo';
import FunctionInfo from '../TSLComponents/FunctionInfo';
import { TSLFunctionType } from '@/utils/constant';
import TSLContext from '../../utils/TSLContext';
import './index.scss';
import { DataType } from '../../utils/constant';
import { formatStruct, formatThingModel } from '../../utils';
import AceEditor from 'react-ace';
const beautify = require('js-beautify').js;

import 'ace-builds/src-noconflict/mode-javascript';

const Server = forwardRef(
  (
    {
      pageType,
      basicInfo,
      outputTslInfo,
      inputTslInfo,
      debugModel,
    }: {
      debugModel: 0 | 1;
      pageType: 'add' | 'check' | 'edit';
      basicInfo: any;
      outputTslInfo: any;
      inputTslInfo: any;
    },
    ref,
  ) => {
    const [basicInfoRef] = Form.useForm();
    const IDEInputVal = useRef(JSON.stringify([]));
    const IDEOutputVal = useRef(JSON.stringify([]));
    const inputRef = useRef(new TSLContext());
    const outputRef = useRef(new TSLContext());
    const [inputCurKey, setInputCurKey] = useState<any>(null);
    const [outputCurKey, setOutputCurKey] = useState<any>(null);

    const inputInstance: any[] = [];
    for (let i = 0; i < 3; i++) {
      const [instance] = Form.useForm();
      inputInstance[i] = instance;
    }
    const outputInstance: any[] = [];
    for (let i = 0; i < 3; i++) {
      const [instance] = Form.useForm();
      outputInstance[i] = instance;
    }
    const formatCode = (val: any) => {
      return beautify(val, {
        indent_size: 2,
        space_in_empty_paren: true,
        brace_style: 'expand', // 大括号换行
      });
    };

    useEffect(() => {
      const inputInitTsl =
        inputTslInfo?.length > 0
          ? formatThingModel({
              dataType: DataType.STRUCT,
              name: 'inputParams',
              identifier: 'inputParams',
              dataSpecsList: inputTslInfo.map((v: any) => {
                const val = {
                  ...v,
                  dataType: DataType.STRUCT,
                  childDataType: v.dataType,
                };
                delete val.paraOrder;
                delete val.direction;
                return val;
              }),
            })
          : [
              {
                [`input${String(Date.now())}`]: {
                  dataType: DataType.STRUCT,
                  name: 'inputParams',
                  identifier: 'inputParams',
                  dataSpecsList: [],
                },
              },
            ];

      inputRef.current.saveTSLInfo({
        formInstanceList: inputInstance,
        pageType: pageType,
        tslInfo: inputInitTsl,
        maxLevel: 3,
        curEditLevel: 0,
        functionType: TSLFunctionType.Server,
      });
      setInputCurKey(Object.keys(inputRef.current.getTSLInfo('tslInfo')[0])[0]);
    }, [inputTslInfo]);

    useEffect(() => {
      const outputInitTsl = outputTslInfo
        ? formatThingModel({
            dataType: DataType.STRUCT,
            name: 'outputParams',
            identifier: 'outputParams',
            dataSpecsList: outputTslInfo.map((v: any) => {
              const val = {
                ...v,
                dataType: DataType.STRUCT,
                childDataType: v.dataType,
              };
              delete val.paraOrder;
              delete val.direction;
              return val;
            }),
          })
        : [
            {
              [`output${String(Date.now())}`]: {
                dataType: DataType.STRUCT,
                name: 'outputParams',
                identifier: 'outputParams',
                dataSpecsList: [],
              },
            },
          ];
      outputRef.current.saveTSLInfo({
        formInstanceList: outputInstance,
        pageType: pageType,
        tslInfo: outputInitTsl,
        maxLevel: 3,
        curEditLevel: 0,
        functionType: TSLFunctionType.Server,
      });
      setOutputCurKey(
        Object.keys(outputRef.current.getTSLInfo('tslInfo')[0])[0],
      );
    }, [outputTslInfo]);

    useImperativeHandle(
      ref,
      () => {
        return {
          submitCheck,
        };
      },
      [inputCurKey, outputCurKey],
    );

    useEffect(() => {
      basicInfoRef.setFieldsValue(basicInfo);
    }, [basicInfo]);

    const submitCheck = async () => {
      const basicInfo = await basicInfoRef.validateFields();
      let inputParams;
      let outputParams;
      if (debugModel === 0 || debugModel == undefined) {
        let inputData = inputRef.current.getTSLInfo('tslInfo');
        let outputData = outputRef.current.getTSLInfo('tslInfo');
        inputData[0][inputCurKey].dataSpecsList = inputData[0][
          inputCurKey
        ].dataSpecsList.filter((v: any) => {
          if (typeof v === 'object') {
            return v.type !== 'del';
          } else {
            return true;
          }
        });
        outputData[0][outputCurKey].dataSpecsList = outputData[0][
          outputCurKey
        ].dataSpecsList.filter((v: any) => {
          if (typeof v === 'object') {
            return v.type !== 'del';
          } else {
            return true;
          }
        });
        inputParams =
          inputData[0][inputCurKey]?.dataSpecsList?.length > 0
            ? formatStruct(inputData)[0].dataSpecsList.map(
                (v: any, i: number) => {
                  const val = {
                    ...v,
                    dataType: v.childDataType,
                    direction: 'PARAM_INPUT',
                    paraOrder: i,
                  };
                  delete val.childDataType;
                  return val;
                },
              )
            : [];
        outputParams =
          outputData[0][outputCurKey]?.dataSpecsList?.length > 0
            ? formatStruct(outputData)[0].dataSpecsList.map(
                (v: any, i: number) => {
                  const val = {
                    ...v,
                    dataType: v.childDataType,
                    direction: 'PARAM_OUTPUT',
                    paraOrder: i,
                  };
                  delete val.childDataType;
                  return val;
                },
              )
            : [];
      }
      return debugModel === 0 || debugModel == undefined
        ? { ...basicInfo, inputParams, outputParams }
        : {
            ...basicInfo,
            inputParams: JSON.parse(IDEInputVal.current),
            outputParams: JSON.parse(IDEOutputVal.current),
          };
    };

    return (
      <div className="server-edit-add">
        <BasicInfo
          formRef={basicInfoRef}
          pageType={pageType}
          basicInfo={basicInfo}
          functionType={TSLFunctionType.Server}
        />
        <span>输入参数</span>
        {debugModel === 0 || debugModel == undefined ? (
          inputCurKey ? (
            <FunctionInfo
              tslRef={inputRef.current}
              level={0}
              curKey={inputCurKey}
              key="input1"
            />
          ) : (
            ''
          )
        ) : (
          <AceEditor
            key={`input${new Date().getTime()}`}
            mode="javascript"
            value={formatCode(JSON.stringify(inputTslInfo))}
            maxLines={28}
            minLines={28}
            setOptions={{
              useWorker: false,
              readOnly: pageType === 'check' ? true : false,
              wrap: true,
            }}
            editorProps={{ $blockScrolling: true }}
            style={{
              width: '600px',
              marginTop: '15px',
              marginBottom: '15px',
              border: '1px solid rgba(0, 0, 0, 0.06)',
            }}
            onChange={(val: any) => {
              IDEInputVal.current = val;
            }}
          />
        )}

        <span>输出参数</span>
        {debugModel === 0 || debugModel == undefined ? (
          outputCurKey ? (
            <FunctionInfo
              tslRef={outputRef.current}
              level={0}
              curKey={outputCurKey}
              key="output1"
            />
          ) : (
            ''
          )
        ) : (
          <AceEditor
            key={`output${new Date().getTime()}`}
            mode="javascript"
            value={formatCode(JSON.stringify(outputTslInfo))}
            maxLines={28}
            minLines={28}
            setOptions={{
              useWorker: false,
              readOnly: pageType === 'check' ? true : false,
              wrap: true,
            }}
            editorProps={{ $blockScrolling: true }}
            style={{
              width: '600px',
              marginTop: '15px',
              marginBottom: '15px',
              border: '1px solid rgba(0, 0, 0, 0.06)',
            }}
            onChange={(val: any) => {
              IDEOutputVal.current = val;
            }}
          />
        )}
      </div>
    );
  },
);

export default React.memo(Server);
