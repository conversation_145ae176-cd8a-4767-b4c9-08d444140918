import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Radio, message } from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import { api } from '@/fetch/core/api';
const { TextArea } = Input;
interface CheckConfProps {
  show: boolean | undefined; // 是否显示modal
  onCancel: Function; // 退出、取消
  content: any; // 当前要查看的文件内容
  type: string | undefined; // 当前modal类型：编辑、查看
  onSubmit?: Function; // 保存按钮
  title: string;
}
const CheckConf = (props: CheckConfProps) => {
  const { show, onCancel, content, type, onSubmit, title } = props;
  const [form] = Form.useForm();
  const layout = {
    labelCol: { span: 2 },
    wrapperCol: { span: 21 },
  };
  useEffect(() => {
    form.setFieldsValue(content);
  }, [type, JSON.stringify(content)]);
  const onSubmitClick = async () => {
    try {
      const data = await form.validateFields();
      if (data && onSubmit) {
        onSubmit(data);
      }
    } catch (e) {
      message.error('请完善信息！');
    }
  };
  return (
    <>
      <Modal
        title={title}
        visible={show}
        onCancel={() => {
          onCancel();
        }}
        width={1000}
        footer={
          <div>
            {type === 'edit' && (
              <div>
                <CustomButton onSubmitClick={onSubmitClick} title={'保存'} />
                <CustomButton
                  buttonType={ButtonType.DefaultButton}
                  onSubmitClick={() => {
                    onCancel();
                  }}
                  title={'取消'}
                />
              </div>
            )}
            {type === 'check' && (
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  onCancel();
                }}
                title={'退出'}
              />
            )}
          </div>
        }
      >
        <Form {...layout} form={form}>
          <Form.Item name="name" label="配置名称">
            <Input.TextArea rows={1} disabled />
          </Form.Item>
          <Form.Item name="description" label="用途说明">
            <Input.TextArea rows={1} disabled />
          </Form.Item>
          <Form.Item name="position" label="所在位置">
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="content"
            label="配置内容"
            rules={
              type === 'edit'
                ? [{ required: true, message: '请填写配置内容' }]
                : []
            }
          >
            <TextArea rows={20} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(CheckConf);
