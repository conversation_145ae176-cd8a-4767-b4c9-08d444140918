import React, { useEffect, useRef, useState } from 'react';
import { message, Popconfirm } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { CommonForm, CommonTable, useTableData } from '@jd/x-coreui';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { SearchConfig, TableConfig } from './utils/columns';
import { formatDateToSecond } from '@/utils/formatTime';
import { RootState } from '@/redux/store';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';
import './index.scss';
import { useNavigate } from 'react-router-dom';
import { EnableMap } from './utils/columns';

const configTemplateApi = new ConfigTemplateApi();

const ConfigTemplateManage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchform,
  );

  const initSearchCondition = {
    searchForm: {
      productKey: undefined,
      productModelNo: undefined,
      templateNo: undefined,
      templateName: undefined,
      createUser: undefined,
      createTime: undefined,
      createTimeStart: undefined,
      createTimeEnd: undefined,
      enable: undefined,
    },
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] = useState(() => {
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const [tableKey, setTableKey] = useState('');

  const { tableData, loading } = useTableData(
    {
      ...searchCondition,
      searchForm: {
        ...searchCondition.searchForm,
      },
    },
    configTemplateApi.getConfigTemplatePage,
    tableKey,
  );

  const onSearchClick = (values: any) => {
    const newSearchCondition = {
      searchForm: values,

      pageNum: 1,
      pageSize: 10,
    };
    setTableKey(Date.now().toString());
    setSearchCondition(newSearchCondition);

    // 保存搜索条件到 store
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newSearchCondition,
      }),
    );
  };

  const onResetClick = () => {
    setTableKey(Date.now().toString());
    setSearchCondition(initSearchCondition);

    // 清除 store 中的搜索条件
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
  };

  const handleView = (record: any) => {
    const { templateNo, templateName, productKey, productModelList } = record;
    const productModelNoList = productModelList
      .map((item: any) => item.productModelNo)
      .join('_');
    navigate(
      `/configTemplate/initDeviceConfig?templateNo=${templateNo}&templateName=${templateName}&productKey=${productKey}&productModelNoList=${productModelNoList}`,
    );
  };

  const handleConfigView = (record: any) => {
    navigate(`/configTemplate/initRecord?templateNo=${record?.templateNo}`);
  };

  // 编辑配置模板
  const handleEdit = (record: any) => {
    navigate(`/configTemplate/edit/${record.templateNo}`);
  };
  // 启用/禁用配置模板
  const handleEnable = async (record: any) => {
    try {
      const res = await configTemplateApi.enableConfigTemplate({
        templateNo: record?.templateNo,
        enable: record?.enable === 1 ? 0 : 1,
      });
      if (res.code === HttpStatusCode.Success && res?.data) {
        message.success('操作成功');
        setTableKey(Date.now().toString());
      } else {
        message.error(res.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleCopy = (record: any) => {
    navigate(`/configTemplate/copy/${record.templateNo}`);
  };

  const handleDelete = async (record: any) => {
    try {
      const res = await configTemplateApi.deleteConfigTemplate({
        templateNo: record?.templateNo,
      });

      if (res.code === HttpStatusCode.Success && res?.data) {
        message.success('删除成功');
        setTableKey(Date.now().toString());
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const formatColumns = () => {
    return TableConfig.map((col) => {
      switch (col.dataIndex) {
        case 'index':
          return {
            ...col,
            render: (_: any, __: any, index: number) => {
              return (
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              );
            },
          };
        case 'enable':
          return {
            ...col,
            render: (enable: number) => (enable === 1 ? '启用' : '禁用'),
          };
        case 'productModelList':
          return {
            ...col,
            render: (productModelList: any[]) => {
              return productModelList
                .map((item: any) => item.productModelName)
                .join(',');
            },
          };
        case 'operation':
          return {
            ...col,
            render: (_: any, record: any) => {
              return (
                <div className="operation-btns">
                  {record?.enable === EnableMap.ENABLE ? (
                    <a onClick={() => handleView(record)}>初始化设备配置</a>
                  ) : null}
                  <a onClick={() => handleConfigView(record)}>初始化记录查询</a>
                  <a onClick={() => handleEdit(record)}>编辑</a>
                  <Popconfirm
                    title={`确认${
                      record?.enable === EnableMap.ENABLE ? '禁用' : '启用'
                    }该配置模板？`}
                    onConfirm={() => handleEnable(record)}
                  >
                    <a>
                      {record?.enable === EnableMap.ENABLE ? '禁用' : '启用'}
                    </a>
                  </Popconfirm>
                  <a onClick={() => handleCopy(record)}>复制</a>
                  <Popconfirm
                    title="确认删除该配置模板？"
                    onConfirm={() => handleDelete(record)}
                  >
                    <a>删除</a>
                  </Popconfirm>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  const handleAdd = () => {
    navigate(`/configTemplate/add?type=add`);
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '创建配置模板',
      key: 'add',
      onClick: () => handleAdd(),
    },
  ];
  return (
    <div className="config-template-manage">
      <div className="search-form">
        <CommonForm
          formConfig={SearchConfig}
          layout="inline"
          formType="search"
          colon={false}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          defaultValue={{
            ...searchCondition.searchForm,
            productKey: 'pda',
            fetchProductKey: true,
          }}
        />
      </div>
      <div className="table-container">
        <CommonTable
          loading={loading}
          columns={formatColumns()}
          middleBtns={middleBtns}
          searchCondition={searchCondition}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          rowKey="templateNo"
          onPageChange={(pagination: any) => {
            const newSearchCondition = {
              ...searchCondition,
              pageNum: pagination.pageNum,
              pageSize: pagination.pageSize,
            };
            setSearchCondition(newSearchCondition);
            dispatch(
              saveSearchValues({
                routeName: location.pathname,
                searchValues: newSearchCondition,
              }),
            );
          }}
        />
      </div>
    </div>
  );
};

export default ConfigTemplateManage;
