import { request } from '@/fetch/core';

export class CommonApi {
  getStationDepartment(params: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_cascade_station_address_list',
      body: params,
    };
    return request(options);
  }

  getCityDepartment(params: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_state_city_address_list',
      body: params,
    };
    return request(options);
  }

  getCommonDropDown({ keyList }: { keyList: Array<string> }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/ota/web/common_get_down_list',
      body: { keyList },
    };
    return request(options);
  }

  fetchERP() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_erp_list',
    };
    return request(options);
  }

  upload(files: any[], requestType?: string) {
    const initReq: RequestInit = {};
    initReq.method = 'POST';
    initReq.mode = 'cors';
    initReq.credentials = 'include';
    const formData = new FormData();
    files.forEach((file: any) => {
      formData.append('file', file);
    });
    formData.append('requestType', requestType ?? 'COMMON_FILE_UPLOAD');
    formData.append('eventType', requestType ?? 'COMMON_FILE_UPLOAD');
    formData.append('pathParameter', '');
    formData.append('requestBody', '');
    formData.append('requestId', `${Date.now()}`);
    // initReq.body = formData;
    // let uploadUrl = api.uploadCommonFile;
    // if (requestType == 'VEHICLE_BATCH_ADD') {
    //   uploadUrl = api.batchUploadCommonVehicle;
    // } else if (requestType == 'VEHICLE_CARD_NO_BATCH_ADD') {
    //   uploadUrl = api.batchUploadCommonCardNo;
    // }

    // const response = await window.fetch(
    //   `//${process.env.JDX_APP_FETCH_DATA_DOMAIN}${uploadUrl}`,
    //   initReq,
    // );
    // const respBody = await parseResponseBody(response, 'application/json');
    // if (respBody) {
    //   return respBody;
    // } else {
    //   return {
    //     code: '-200',
    //     message: '上传失败',
    //   };
    // }
  }
}
