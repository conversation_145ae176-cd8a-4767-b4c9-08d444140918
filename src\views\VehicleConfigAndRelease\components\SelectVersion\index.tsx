import React, { useEffect, useState } from 'react';
import { Col, Form, Select, Table, Row, Modal, message } from 'antd';
import {
  pageSizeOptions,
  SearchCondition,
  TableListType,
} from '@/utils/constant';
import { SelectVersionTableColumns } from '../../utils/columns';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { api } from '@/fetch/core/api';
import './index.scss';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
interface Props {
  modalShow: boolean;
  closeModal: Function;
  onSubmit: Function;
  currentRow: any;
}
const SelectVersion = (props: Props) => {
  const { modalShow, closeModal, onSubmit, currentRow } = props;
  const [form] = Form.useForm();
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    searchForm: {
      versionNumber: null,
    },
    current: 1,
    pageSize: 10,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [versionTable, setVersionTable] = useState<TableListType>({
    list: [],
    totalNumber: 0,
    totalPage: 0,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [versionNameOptions, setVersionNameOptions] = useState<any[]>([]);
  useEffect(() => {
    fetchVersionData(searchCondition);
    fetchOptions();
  }, []);
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };

  /**
   * table每一列要展示的内容
   */
  const formatColumns = () => {
    return SelectVersionTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  /**
   * 获取当前模块的版本信息列表
   * @param {Object} searchValues 搜索条件
   */
  const fetchVersionData = (searchValues: any) => {
    setLoading(true);
    try {
      request({
        method: 'POST',
        path: api.getApplicationVersionInfoList,
        body: {
          appName: currentRow.appName.value,
          versionNumber: searchValues.searchForm.versionNumber,
          enable: 1,
        },
        urlParams: {
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVersionTable({
            list: res.data.list,
            totalNumber: res.data.total,
            totalPage: res.data.pages,
          });
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取版本号下拉框列表
   */
  const fetchOptions = () => {
    request({
      method: 'POST',
      path: api.getApplicationVersionList,
      body: {
        appName: currentRow.appName.value,
        enable: 1,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVersionNameOptions(
            res.data
              ?.filter((i: any) => i.enable === 1)
              ?.map((item: any) => {
                return {
                  label: item.version,
                  value: item.versionNumber,
                };
              }),
          );
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  /**
   * 点击确定按钮
   */
  const onSubmitVersion = () => {
    if (selectedRows.length === 1) {
      onSubmit(selectedRows, currentRow);
      closeModal();
    } else {
      message.error('未选中一条数据，操作失败！');
    }
  };
  return (
    <Modal
      title={`设置${currentRow.appName.label}版本`}
      visible={modalShow}
      width={'1200px'}
      onCancel={() => closeModal()}
      footer={[
        <CustomButton title="确定" onSubmitClick={() => onSubmitVersion()} />,
        <CustomButton
          title="取消"
          buttonType={ButtonType.DefaultButton}
          otherCSSProperties={{ marginLeft: '20px' }}
          onSubmitClick={() => closeModal()}
        />,
      ]}
    >
      <div className="select-version">
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          form={form}
        >
          <Form.Item name={'versionNumber'} label={'版本号'}>
            <Select
              style={{ width: '300px' }}
              placeholder={'请输入关键字，支持关键字联想全称'}
              options={versionNameOptions}
              showSearch
              allowClear
              filterOption={(input, option) => {
                const label: any = option?.label || '';
                return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
              onChange={(value: any) => {
                setSearchCondition({
                  searchForm: { versionNumber: value },
                  current: searchCondition.current,
                  pageSize: searchCondition.pageSize,
                });
              }}
            />
          </Form.Item>
        </Form>
        <CustomButton
          otherCSSProperties={{ marginLeft: '20px', height: '34px' }}
          title="查询"
          onSubmitClick={() =>
            fetchVersionData({
              searchForm: form.getFieldsValue(),
              current: 1,
              pageSize: 10,
            })
          }
        />
      </div>
      <Table
        rowKey={(record) => record.versionNumber}
        loading={loading}
        rowSelection={{
          type: 'radio',
          ...rowSelection,
        }}
        bordered
        dataSource={versionTable.list}
        columns={formatColumns()}
        scroll={{
          y: 250,
        }}
        pagination={{
          position: ['bottomCenter'],
          total: versionTable.totalNumber,
          current: searchCondition.current,
          pageSize: searchCondition.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: pageSizeOptions,
          showTotal: (total) =>
            `共 ${versionTable.totalPage}页,${total} 条记录`,
        }}
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            const newSearchValue = {
              ...searchCondition,
              current,
              pageSize,
            };
            setSearchCondition(newSearchValue);
            fetchVersionData(newSearchValue);
          }
        }}
      />
    </Modal>
  );
};

export default React.memo(SelectVersion);
