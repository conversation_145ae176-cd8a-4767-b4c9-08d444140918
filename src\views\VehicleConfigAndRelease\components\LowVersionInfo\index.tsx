import { Button, Form, Modal, message, Select, Table, Row, Col } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { useDispatch, useSelector } from 'react-redux';
import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  selectedVehicleSelector,
  removeSelectedVehicle,
} from '@/redux/reducers/selectedVehicle';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isEmpty } from '@/utils/utils';
const EditableContext = React.createContext<FormInstance<any> | null>(null);
interface Item {
  key: string;
  appName: string;
  version: string; // 版本名
  number: string; // 版本号
}
interface EditableRowProps {
  index: number;
}
const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  children: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  moduleOptions: any;
  versionOptions: any;
  saveSelectModule: Function;
  saveSelectVersion: Function;
  onClickSelectVersion: Function;
  selectedVehicle: any;
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  children,
  dataIndex,
  record,
  saveSelectModule,
  onClickSelectVersion,
  moduleOptions,
  saveSelectVersion,
  versionOptions,
  selectedVehicle,
  ...restProps
}) => {
  let childNode = children;
  const form: any = useContext(EditableContext);
  const dispatch = useDispatch();
  useEffect(() => {
    form?.setFieldsValue({
      appName: record.appName,
      version: record.version,
    });
  }, [record]);

  /**
   * 选中版本号
   * @param {Object} value 选中的版本信息
   * @param {Object} record 当前行
   */
  const selectVersion = (value: any, record: any) => {
    const vehicleNameList: string[] = [];
    selectedVehicle.forEach((value: any) => {
      vehicleNameList.push(value.vehicleName);
    });
    // 先校验当前模块 版本号是否所有的车都满足
    request({
      method: 'POST',
      path: api.getNotApplicableVehicleNameList,
      body: {
        appName: record.appName.value,
        versionNumber: value,
        vehicleNameList: vehicleNameList,
      },
    })
      .then((res: any) => {
        if (
          res &&
          res.code === HttpStatusCode.Success &&
          res.data.nonConformanceVehicleNameList.length > 0
        ) {
          Modal.confirm({
            title: '提示',
            content: (
              <p style={{ wordBreak: 'break-all' }}>
                {res.data.nonConformanceVehicleNameList.length}
                辆不满足最低版本的车牌号：
                {res.data.nonConformanceVehicleNameList.toString()}
                ；是否要去掉这些车？
              </p>
            ),
            okText: '确认去掉',
            cancelText: '关闭',
            onCancel: () => {
              saveSelectVersion(undefined, record);
              form.resetFields(['version']);
            },
            onOk: () => {
              saveSelectVersion(value, record);
              if (!isEmpty(res.data.nonConformanceVehicleNameList)) {
                for (let item of res.data.nonConformanceVehicleNameList) {
                  dispatch(removeSelectedVehicle(item));
                }
              }
            },
          });
        } else if (
          res &&
          res.code === HttpStatusCode.Success &&
          res.data.nonConformanceVehicleNameList.length <= 0
        ) {
          saveSelectVersion(value, record);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  switch (dataIndex) {
    case 'appName':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'appName'}>
          <Select
            labelInValue
            placeholder={`请选择模块`}
            options={moduleOptions}
            onChange={(value: any) => {
              saveSelectModule(value, record.key);
            }}
          />
        </Form.Item>
      );
      break;
    case 'version':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'version'}>
          <Select
            showSearch
            allowClear
            filterOption={(input, option) => {
              const label: any = option?.label || '';
              return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            placeholder={`请输入最低版本号，支持关键字联想全称`}
            options={versionOptions}
            onFocus={() => onClickSelectVersion(record.key)}
            onChange={(value: any) => {
              selectVersion(value, record);
            }}
          />
        </Form.Item>
      );
      break;
    case 'number':
      childNode = <div>{record.number}</div>;
      break;
  }
  return <td {...restProps}>{childNode}</td>;
};

type EditableTableProps = Parameters<typeof Table>[0];
type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;
const LowVersionInfo = (props: any) => {
  const selectedVehicle = useSelector(selectedVehicleSelector).selectedVehicle;
  const [moduleOptions, setModuleOptions] = useState<any[]>();
  const [versionOptions, setVersionOptions] = useState<any[]>();
  const defaultColumns = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: '10%',
      ellipsis: true,
      render: (text: any, record: number, index: number) => index + 1,
    },
    {
      title: (
        <div>
          <span style={{ color: 'red' }}>*</span>&nbsp;模块
        </div>
      ),
      dataIndex: 'appName',
      align: 'center',
      width: '20%',
      ellipsis: true,
    },
    {
      title: (
        <div>
          <span style={{ color: 'red' }}>*</span>&nbsp;版本号
        </div>
      ),
      dataIndex: 'version',
      align: 'center',
      width: '35%',
      ellipsis: true,
    },
    {
      title: '系统版本编号',
      dataIndex: 'versionNumber',
      align: 'center',
      width: '35%',
      ellipsis: true,
    },
  ];
  const columns = defaultColumns?.map((col) => {
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        dataIndex: col.dataIndex,
        title: col.title,
        moduleOptions: moduleOptions,
        versionOptions: versionOptions,
        saveSelectModule: saveSelectModule,
        saveSelectVersion: saveSelectVersion,
        onClickSelectVersion: onClickSelectVersion,
        selectedVehicle: selectedVehicle,
      }),
    };
  });
  const [count, setCount] = useState<number>(2);
  const [dataSource, setDataSource] = useState<any[]>([
    {
      key: 1,
      appName: null,
      version: null,
      number: '系统带入',
      repeat: false,
    },
  ]);
  useEffect(() => {
    fetchModuleOptions();
  }, []);

  /**
   * 获取模块下拉框列表信息
   */
  const fetchModuleOptions = () => {
    request({
      method: 'POST',
      path: api.getApplicationList,
      body: {
        productType: props.productType,
        enable: 1,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setModuleOptions(
            res.data?.map((item: any) => {
              return {
                value: item.appName,
                label: item.appAlias,
              };
            }),
          );
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  /**
   * 保存选中的模块
   * @param {Object} value 选中的模块信息
   * @param {number} key 当前行的key
   */
  const saveSelectModule = (value: any, key: number) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => key === item.key);
    const item = newData[index];
    let isRepeat: boolean = false;
    dataSource?.forEach((item: any) => {
      if (item.appName?.value === value.value) {
        isRepeat = true;
        return;
      }
    });
    if (isRepeat) {
      newData.splice(index, 1, {
        key: item.key,
        appName: value,
        version: undefined,
        number: '系统带入',
        repeat: isRepeat,
      });
      setDataSource(newData);
      message.error(`${value.label}模块已存在，不允许重复选择！`);
    } else {
      newData.splice(index, 1, {
        key: item.key,
        appName: value,
        version: undefined,
        number: '系统带入',
        repeat: isRepeat,
      });
      setDataSource(newData);
    }
  };

  /**
   * 点击选择版本号时获取版本号下拉框列表内容
   * @param {number} key 当前行的key
   */
  // 点击选择版本名的时候拿到当前行,并获取版本号下拉框内容
  const onClickSelectVersion = (key: any) => {
    const data = dataSource.find((item: any) => item.key === key);
    console.log(data);
    if (data?.appName?.value && !data.repeat) {
      request({
        method: 'POST',
        path: api.getApplicationVersionList,
        body: {
          appName: data.appName.value,
          enable: props.enable,
        },
      })
        .then((res: any) => {
          if (res && res.code === HttpStatusCode.Success) {
            setVersionOptions(
              res.data?.map((item: any) => {
                return {
                  label: item.version,
                  value: item.versionNumber,
                };
              }),
            );
          }
        })
        .catch((err) => {
          console.log(err);
        });
    } else if (data?.appName?.value && data.repeat) {
      setVersionOptions([]);
      message.error(`请先修改重复模块`);
    } else if (!data?.appName) {
      setVersionOptions([]);
      message.error('请先选择模块');
    }
  };

  /**
   * 保存选中的版本号信息
   * @param {string} value 选中的版本号option的value
   * @param {Object} record 当前行的信息
   */
  const saveSelectVersion = (value: string, record: any) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => record.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      key: item.key,
      appName: item.appName,
      version: value,
      number: value ? value : '系统带入',
      repeat: item.repeat,
    });
    setDataSource(newData);
  };

  /**
   * 增加删除表的数据源
   */
  const makeEditDatasource = () => {
    const editList = dataSource?.map((item, index) => {
      return {
        name: index === 0 ? '+' : '-',
        key: item.key,
      };
    });
    return editList;
  };

  /**
   * 增加一行
   */
  const onAddClick = () => {
    const filteredList = dataSource.filter(
      (item: any) =>
        item.appName === undefined ||
        item.version === undefined ||
        item.appName === undefined ||
        item.version === null,
    );
    if (filteredList.length > 0) {
      message.error('请先完善当前信息');
      return;
    }
    setDataSource([
      ...dataSource,
      {
        key: count,
        appName: null,
        version: null,
        number: '系统带入',
        repeat: false,
      },
    ]);
    setCount(count + 1);
  };

  /**
   * 删除一行
   * @param {number} index 要删除行的index
   */
  const onDeleteClick = (index: number) => {
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  useEffect(() => {
    props.form.setFieldsValue({
      lowestAppVersionInfoList: dataSource?.map((item: any) => {
        return {
          appName: item.appName?.value,
          versionNumber: item.number,
        };
      }),
    });
  }, [JSON.stringify(dataSource)]);
  return (
    <>
      <Row>
        <Col span={23}>
          <Table
            pagination={false}
            components={components}
            rowClassName={() => 'editable-row'}
            bordered
            dataSource={dataSource}
            columns={columns as ColumnTypes}
          />
        </Col>
        <Col span={1}>
          <Table
            columns={[
              {
                title: 'has',
                dataIndex: 'name',
                align: 'left',
                onHeaderCell: (data, index) => {
                  return {
                    style: {
                      backgroundColor: 'rgba(0, 0, 0, 0)',
                      color: 'white',
                      borderWidth: 0,
                    },
                  };
                },
                onCell: (data, index) => {
                  return {
                    style: {
                      backgroundColor: 'white',
                      borderWidth: 0,
                      padding: 0,
                      height: 65,
                    },
                  };
                },
                render: (name, _: any, index: number) => {
                  const bgColor = name === '+' ? '#31C2A6' : '#D9001B';
                  return name === '-' ? (
                    <Button
                      onClick={() => {
                        onDeleteClick && onDeleteClick(index);
                      }}
                      style={{
                        marginLeft: 15,
                        color: bgColor,
                        width: 55,
                        borderColor: bgColor,
                        borderRadius: 4,
                      }}
                    >
                      {' '}
                      {name}
                    </Button>
                  ) : (
                    <Button
                      onClick={() => {
                        onAddClick && onAddClick();
                      }}
                      style={{
                        marginLeft: 15,
                        color: bgColor,
                        width: 55,
                        borderColor: bgColor,
                        borderRadius: 4,
                      }}
                    >
                      {' '}
                      {name}
                    </Button>
                  );
                },
              },
            ]}
            dataSource={makeEditDatasource()}
            pagination={false}
          />
        </Col>
      </Row>
    </>
  );
};
export default React.memo(LowVersionInfo);
