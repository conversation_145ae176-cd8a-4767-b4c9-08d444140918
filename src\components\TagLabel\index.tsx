import React, { useRef, useState } from 'react';
import './index.scss';
import { isEmpty } from '@/utils/utils';
import showModal from '@/components/commonModal';
import CreateTag, { CreateTagProps } from './CreateTag';

export interface TagProps {
  modalTitle?: string;
  placeholder?: string;
  createTagProps?: CreateTagProps;
  onChange: AnyFunc;
}

export function validateTagList(tagList: { name: string; value: string }[]) {
  const nameArr: any = new Set();
  let flag = true;
  for (const item of tagList) {
    if (nameArr.has(item.name)) {
      flag = false;
      break;
    } else {
      nameArr.add(item.name);
      continue;
    }
  }
  return flag;
}
export const TagItem = (props: {
  value: string | number;
  text: string;
  handleDelete: (value: string | number) => void;
}) => {
  return (
    <div className="tag-item">
      <span className="tag-text">{props.text}</span>
      <i
        className="delete-btn"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          props.handleDelete(props.value);
        }}
      ></i>
    </div>
  );
};

const TagLabel = (props: TagProps) => {
  const { modalTitle, placeholder, createTagProps } = props;
  const tagModalRef = useRef<any>(null);
  const [tagList, setTagList] = useState<any[]>([]);
  const handleDelete = (value: any) => {
    const newTagList = tagList.filter((item) => item.name != value) || [];
    setTagList([...newTagList]);
    props.onChange(newTagList);
  };

  const showAddTagModal = () => {
    showModal({
      title: modalTitle ?? '添加分组标签',
      width: '600px',
      content: (
        <CreateTag {...createTagProps} tagList={tagList} ref={tagModalRef} />
      ),
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: (cb: any) => {
            const tagList = tagModalRef.current.getTagList();
            const flag = validateTagList(tagList);
            if (!tagList || !flag) {
              return;
            }
            setTagList([...tagList]);
            props.onChange(tagList);
            cb();
          },
        },
      ],
    });
  };
  return (
    <div className="tag-label" onClick={showAddTagModal}>
      <div className="tag-list-wrapper">
        {isEmpty(tagList) && (
          <div className="placeholder">{placeholder ?? '请选择分组标签'}</div>
        )}
        {tagList.map((item) => (
          <TagItem
            key={`${item.name}/${item.value}`}
            value={item.name}
            text={`${item.name}/${item.value}`}
            handleDelete={handleDelete}
          />
        ))}
      </div>
    </div>
  );
};

export default TagLabel;
