.structured-config-selector {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .section-content {
    .ant-tabs {
      margin-bottom: 16px;
    }

    .config-tree-container {
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fafafa;

      .tree-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: white;
        border-bottom: 1px solid #f0f0f0;
        border-radius: 6px 6px 0 0;

        > span {
          font-weight: 500;
          color: #262626;
        }

        .header-actions {
          .ant-btn {
            padding: 0;
            height: auto;
            font-size: 12px;
          }
        }
      }

      .ant-tree {
        padding: 16px;
        background: white;
        border-radius: 0 0 6px 6px;
        max-height: 400px;
        overflow-y: auto;

        .ant-tree-treenode {
          padding: 4px 0;

          .ant-tree-node-content-wrapper {
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s;

            &:hover {
              background: #f5f5f5;
            }
          }

          .ant-tree-title {
            font-size: 14px;
          }

          // 配置类样式（不可选）
          &.ant-tree-treenode-disabled {
            .ant-tree-title {
              font-weight: 500;
              color: #262626;
            }
          }

          // 配置项样式（可选）
          .ant-tree-checkbox {
            margin-right: 8px;
          }
        }

        // 展开/收起图标
        .ant-tree-switcher {
          width: 20px;
          height: 20px;
          line-height: 20px;
          margin-right: 4px;
        }
      }

      .selected-info {
        padding: 8px 16px;
        background: #f6ffed;
        border-top: 1px solid #f0f0f0;
        border-radius: 0 0 6px 6px;
        font-size: 12px;
        color: #52c41a;
      }
    }
  }

  .no-product,
  .no-data {
    color: #8c8c8c;
    font-style: italic;
    text-align: center;
    padding: 40px 0;
    background: #fafafa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
  }
}
