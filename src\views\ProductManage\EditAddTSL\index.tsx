import { But<PERSON>, Col, Flex, Row, Table, message } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import './index.scss';
import { useNavigate, useLocation } from 'react-router-dom';
import { formatStruct, formatTSLData, formatThingModel } from '../utils';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import { HttpStatusCode } from '@/fetch/core/constant';
import Properties from '../component/Properties';
import { formatLocation } from '@/utils/formatLocation';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { DataType } from '../utils/constant';
import { addGlobalEventListener } from '@/utils/emit';
import { TSLFunctionType } from '@/utils/constant';
import Server from '../component/Server';

const EditAddTSL = () => {
  const navigator = useNavigate();
  const propertiesRef = useRef<any>();
  const serverRef = useRef<any>();
  const checkDataRes = useRef<any>(true);
  const {
    type,
    productKey,
    blockNo,
    blockName,
    identifier,
    backUrl,
    version,
    funcType,
  } = formatLocation(window.location.search);
  const fetchApi = new TSLModelFetch();
  const titleMap = new Map([
    ['add', '添加功能'],
    ['edit', '编辑功能'],
    ['check', '查看功能'],
  ]);
  const [TSLData, setTSLData] = useState<any>({});
  const [functionType, setFunctionType] = useState<TSLFunctionType>(
    TSLFunctionType.Properties,
  );

  useEffect(() => {
    addGlobalEventListener('checkRes', (res) => {
      checkDataRes.current = res;
    });
    return () => {
      removeEventListener('checkRes', (res) => {
        checkDataRes.current = res;
      });
    };
  }, []);

  useEffect(() => {
    if (type !== 'add' && identifier) {
      getFunctionInfo();
      setFunctionType(funcType);
    }
  }, [type, productKey, blockNo, identifier]);

  const goBack = () => {
    navigator(
      `${backUrl}?productKey=${productKey}&blockNo=${blockNo}&blockName=${blockName}`,
    );
  };

  const getFunctionInfo = async () => {
    const res = await fetchApi.getFunctionInfo({
      productKey,
      blockNo,
      identifier,
      version: type === 'edit' ? 'beta' : version,
    });
    if (res.code === HttpStatusCode.Success) {
      const data = JSON.parse(res?.data?.content || '{}');
      if (funcType == TSLFunctionType.Properties) {
        setTSLData({
          TSLInfo: formatThingModel(data),
          basicInfo: {
            rwFlag: data.rwFlag,
            description: data.description,
          },
        });
      } else if (funcType == TSLFunctionType.Server) {
        setTSLData({
          inputTslInfo: data.inputParams,
          outputTslInfo: data.outputParams,
          debugModel: res.data.debugModel,
          basicInfo: {
            identifier: data.identifier,
            name: data.name,
            description: data.description,
          },
        });
      }
    }
  };

  const changeType = (val: TSLFunctionType) => {
    if (type !== 'add') {
      return;
    }
    setTSLData({});
    setFunctionType(val);
  };

  const handlePropertySubmit = async () => {
    const { basicInfo, propertiesData } =
      await propertiesRef.current.submitCheck();
    if (!basicInfo || !propertiesData) {
      return;
    }
    const tabularData = [
      DataType.ENUM,
      DataType.INT_ENUM,
      DataType.BOOL,
      DataType.STRUCT,
    ];
    let properties;
    const val: any = Object.values(propertiesData[0]);
    if (
      propertiesData.length === 1 ||
      (propertiesData.length > 1 && !val[0].dataSpecsList)
    ) {
      if (val[0].dataType === DataType.STRUCT && !val[0].dataSpecsList) {
        message.error('参数不能为空');
        return;
      }
      properties = val.map((v: any, index: number) => {
        if (tabularData.includes(v.dataType)) {
          return {
            ...basicInfo,
            productKey,
            identifier: v.identifier,
            name: v.name,
            dataType: v.dataType,
            dataSpecsList: formatTSLData(v),
          };
        } else {
          return {
            ...basicInfo,
            productKey,
            identifier: v.identifier,
            name: v.name,
            dataType: v.dataType,
            dataSpecs: formatTSLData(v),
          };
        }
      });
    } else {
      properties = [
        {
          ...formatStruct(propertiesData)[0],
          ...basicInfo,
          productKey,
        },
      ];
    }
    const res = await fetchApi.createTSLInfo({
      productKey,
      blockNo,
      identifier: identifier ?? null,
      content: JSON.stringify({ blockNo, blockName, productKey, properties }),
    });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      propertiesRef.current?.clearData();
      navigator(
        `${backUrl}?productKey=${productKey}&blockNo=${blockNo}&blockName=${blockName}`,
      );
    } else {
      message.error(res.message);
    }
  };

  const handleServerSubmit = async () => {
    const data = await serverRef.current.submitCheck();
    if (!data) {
      return;
    }
    const res = await fetchApi.createTSLInfo({
      productKey,
      blockNo,
      identifier: identifier ?? null,
      content: JSON.stringify({
        productKey: productKey,
        services: [{ ...data, productKey }],
      }),
    });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      navigator(
        `${backUrl}?productKey=${productKey}&blockNo=${blockNo}&blockName=${blockName}`,
      );
    } else {
      message.error(res.message);
    }
  };

  const handleSubmit = async () => {
    if (type === 'check') {
      return;
    }
    if (!checkDataRes.current) {
      return;
    }
    if (functionType == TSLFunctionType.Properties) {
      handlePropertySubmit();
    } else if (functionType == TSLFunctionType.Server) {
      handleServerSubmit();
    }
  };

  return (
    <div className="TSL-add-edit">
      <div className="title">
        <ArrowLeftOutlined onClick={goBack} />
        <span onClick={goBack}>{titleMap.get(type!)}</span>
      </div>
      <Row className="function-type" align="middle">
        <Col className="field-name" span={2}>
          <span>*</span>
          <span>功能类型：</span>
        </Col>
        <Col className="type" span={22}>
          <Flex>
            <span
              className={
                functionType == TSLFunctionType.Properties ? 'selected' : ''
              }
              onClick={() => changeType(TSLFunctionType.Properties)}
            >
              属性
            </span>
            <span
              className={
                functionType == TSLFunctionType.Server ? 'selected' : ''
              }
              onClick={() => changeType(TSLFunctionType.Server)}
            >
              服务
            </span>
            <span
              className={
                functionType == TSLFunctionType.Event ? 'selected' : ''
              }
              onClick={() => changeType(TSLFunctionType.Event)}
            >
              事件
            </span>
          </Flex>
        </Col>
      </Row>

      {functionType == TSLFunctionType.Properties && (
        <Properties
          type={type}
          ref={propertiesRef}
          ThingModel={TSLData.TSLInfo ?? [{}]}
          basicInfo={TSLData.basicInfo}
        />
      )}

      {functionType == TSLFunctionType.Server && (
        <Server
          ref={serverRef}
          pageType={type}
          debugModel={TSLData.debugModel}
          basicInfo={TSLData.basicInfo}
          inputTslInfo={TSLData.inputTslInfo}
          outputTslInfo={TSLData.outputTslInfo}
        />
      )}

      <div className="bottom-btns">
        <Button type="primary" onClick={handleSubmit}>
          确定
        </Button>
        <Button onClick={goBack}>取消</Button>
      </div>
    </div>
  );
};

export default React.memo(EditAddTSL);
