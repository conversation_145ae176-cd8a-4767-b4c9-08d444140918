import React, { useState, useEffect, useRef } from 'react';
import CommonForm from '@/components/CommonForm';
import { GroupColumns, GroupForm, SearchForm } from './utils/columns';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import TagLabel from '@/components/TagLabel';
import { Button, Modal, Table, Tree, message } from 'antd';
import './index.scss';
import showModal from '@/components/commonModal';
import { cloneDeep, set } from 'lodash';
import CreateTag from '@/components/TagLabel/CreateTag';
import Detail from './Detail';
import Device from '@/fetch/bussiness/device';
import { HttpStatusCode } from '@/fetch/core/constant';
import CommonTable from '@/components/CommonTable';
import { useTableData } from '@/components/CommonTable/useTableData';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { formatTreeData, isEmpty } from '@/utils/utils';
import { sendGlobalEvent } from '@/utils/emit';
const fetchApi = new Device();
/**
 * 将嵌套数组扁平化
 * @param arr - 要处理的数组
 * @returns 扁平化后的数组
 */
function flattenArray(arr: any[]) {
  let result: any[] = [];
  for (let i = 0; i < arr.length; i++) {
    if (Array.isArray(arr[i]?.children)) {
      result = result.concat(flattenArray(arr[i]?.children));
    } else {
      result.push(arr[i]);
    }
  }
  return result;
}

const GroupManage = () => {
  const formRef = useRef<any>(null);
  const [searchConf, setSearchConf] = useState<FormConfig>(SearchForm);
  const [selectGroup, setSelectGroup] = useState<string[]>([]);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [groupNoList, setGroupNoList] = useState<any[]>([]);
  const [productList, setProductList] = useState<any[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );

  const initSearchCondition = {
    productKey: null,
    groupName: null,
    tagList: null,
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValue.searchValues
      ? historySearchValue.searchValues
      : initSearchCondition;
  });
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    fetchApi.getGroupInfoPageList,
    'init',
    autoFetch,
  );
  const tagRef = useRef<any>(null);
  // 获取所有分组并组装成树结构和级联下拉数据结构
  const getTreeData = (productKey?: string) => {
    fetchApi.getAllGroupList({ productKey }).then((res: any) => {
      if (res?.code === HttpStatusCode.Success) {
        setGroupNoList(res?.data?.groupNoList || []);
        setTreeData(
          formatTreeData({
            origin: res?.data?.groupNoList || [],
            type: 'Tree',
            level: 0,
            productKey,
          }),
        );
      }
    });
  };
  useEffect(() => {
    // 获取所有产品列表，搜索条件默认选择产品列表第一项，不允许删除
    fetchApi.queryProductList().then((res: any) => {
      if (res.code === HttpStatusCode.Success) {
        const firstProductKey = res?.data && res?.data[0]?.productKey;
        const productField = searchConf.fields.find(
          (field: FieldItem) => field.fieldName === 'productKey',
        );
        const productList =
          res?.data?.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
        productField!.options = productList;
        setProductList(productList);
        formRef.current?.setFieldValue('productKey', firstProductKey);
        setSearchCondition({
          ...searchCondition,
          productKey: firstProductKey,
        });
        setSearchConf({ ...searchConf });
        setAutoFetch(true);
        getTreeData(firstProductKey);
      }
    });
  }, []);
  useEffect(() => {
    const custormField = searchConf.fields.find(
      (item: FieldItem) => item.fieldName === 'tagList',
    );
    custormField!.renderFunc = () => (
      <TagLabel
        createTagProps={{
          maxSize: 10,
        }}
        onChange={(tagList: string[] | null) => {
          formRef.current?.setFieldValue('tagList', tagList);
        }}
      />
    );
    setSearchConf({
      ...searchConf,
    });
  }, []);

  const addGroup = (values: any, cb: AnyFunc) => {
    fetchApi
      .addGroup({
        ...values,
        parentNo: !isEmpty(values?.parentNo)
          ? values?.parentNo[values?.parentNo?.length - 1]
          : null,
        productKey: values?.productKey,
        tagList: values?.tagList?.map((item: any) => ({
          tagKey: item.name,
          tagValue: item.value,
        })),
      })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          message.success('新增分组成功');
          getTreeData(searchCondition.productKey);
          reloadTable();
        } else {
          res?.message && message.error(res?.message);
        }
      })
      .finally(() => {
        cb();
      });
  };
  const createGroup = () => {
    let formInstance: any = null;
    const _formConfig = cloneDeep(GroupForm);
    // 直接将搜索表单中的数据放到新建分组表单中来
    const productField = searchConf.fields.find(
      (field: FieldItem) => field.fieldName === 'productKey',
    );
    _formConfig.fields?.forEach((item: FieldItem) => {
      if (item.fieldName === 'tagList') {
        item.renderFunc = () => <CreateTag ref={tagRef} />;
      } else if (item.fieldName === 'productKey') {
        item.options = productField?.options;
      } else if (item.fieldName === 'parentNo') {
        item.options = formatTreeData({
          origin: groupNoList.filter((i: any) => i.groupNo !== 'NULL'),
          type: 'Cascader',
          level: 0,
          disabledLevel: 2,
        });
        item.disabled = true;
      }
    });
    showModal({
      title: '创建分组',
      width: 700,
      content: (
        <CommonForm
          layout="vertical"
          name="addGroup"
          formConfig={_formConfig}
          getFormInstance={(formRef: any) => {
            formInstance = formRef;
          }}
          onValueChange={(formValues: any, changedFieldName: string) => {
            if (changedFieldName === 'productKey') {
              formRef.current?.setFieldValue('parentNo', null);
              const parentNo = _formConfig.fields.find(
                (item: FieldItem) => item.fieldName === 'parentNo',
              );
              const options = formatTreeData({
                origin: groupNoList.filter((i: any) => i.groupNo !== 'NULL'),
                type: 'Cascader',
                level: 0,
                productKey: formValues?.productKey,
                disabledLevel: 2,
              });
              parentNo!.options = options;
              parentNo!.disabled = !formValues?.productKey;
              sendGlobalEvent('FORCE_UPDATE_CONFIG', {
                name: 'addGroup',
                config: _formConfig,
              });
            }
          }}
        />
      ),
      footer: [
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: AnyFunc) => {
            try {
              const values = await formInstance?.validateFields();
              const tagList = tagRef.current?.getTagList();
              if (!tagList) {
                return;
              }
              addGroup({ ...values, tagList }, cb);
            } catch (e) {}
          },
        },
        {
          text: '取消',
          type: 'cancelBtn',
        },
      ],
    });
  };
  const formatColumns = () => {
    return GroupColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (text: string, record: any) => {
            return (
              <>
                <a
                  onClick={() => {
                    setSelectGroup([record.groupNo]);
                    const data = flattenArray(treeData);
                    const expandedKeys = data?.find(
                      (i) => i.key === record.groupNo,
                    )?.parentKey;
                    setExpandedKeys(expandedKeys || []);
                  }}
                >
                  查看详情
                </a>
                <a
                  style={{ marginLeft: '4px' }}
                  onClick={() => {
                    Modal.confirm({
                      content: `是否确定删除该分组[${record.groupNo}][${record.levelName}]？`,
                      onOk: () => {
                        fetchApi
                          .deleteGroup(record.groupNo)
                          .then((res: any) => {
                            if (res?.code === HttpStatusCode.Success) {
                              message.success('删除成功');
                              reloadTable();
                              getTreeData(searchCondition.productKey);
                            }
                          });
                      },
                    });
                  }}
                >
                  删除
                </a>
              </>
            );
          };
          break;
        default:
          break;
      }
      return col;
    });
  };
  useEffect(() => {
    if (searchCondition.productKey) {
      getTreeData(searchCondition.productKey);
    }
  }, [searchCondition.productKey]);
  return (
    <div className="group-management-container">
      <CommonForm
        className="group-searchform"
        formConfig={searchConf}
        formType="search"
        layout={'inline'}
        onResetClick={() => {
          formRef.current?.resetFields();
          setSearchCondition({
            ...initSearchCondition,
            productKey: productList && productList[0]?.value,
          });
          formRef.current?.setFieldValue(
            'productKey',
            productList && productList[0]?.value,
          );
          setSelectGroup([]);
        }}
        onSearchClick={() => {
          const values = formRef.current?.getFieldsValue();
          setSearchCondition({
            ...searchCondition,
            ...values,
            productKey: values?.productKey,
            tagList: values?.tagList?.map((i: any) => `${i.name}/${i.value}`),
          });
          const options = formatTreeData({
            origin: groupNoList.filter((i: any) => i.groupNo !== 'NULL'),
            type: 'Tree',
            level: 0,
            productKey: values?.productKey,
          });
          setSelectGroup([]);
          setTreeData(options);
        }}
        getFormInstance={(formInstance: any) => {
          formRef.current = formInstance;
        }}
      />
      <div className="group-main-content">
        <div className="left-menu">
          <Button
            style={{
              margin: '4px',
            }}
            type="primary"
            onClick={createGroup}
          >
            创建分组
          </Button>
          <div
            style={{
              fontSize: '14px',
              padding: '4px',
              cursor: 'pointer',
            }}
            onClick={() => {
              setSelectGroup(['NULL']);
            }}
          >
            未分组
          </div>
          <Tree
            treeData={treeData}
            selectedKeys={selectGroup}
            expandedKeys={expandedKeys}
            onSelect={(value: any, e: any) => {
              setSelectGroup(value || []);
            }}
            onExpand={(newExpandedKeys: any[]) => {
              setExpandedKeys(newExpandedKeys);
            }}
          />
        </div>
        <div className="right-region">
          {selectGroup?.length > 0 ? (
            <Detail
              groupNo={selectGroup}
              key={selectGroup?.toString() + Date.now()}
              goBack={setSelectGroup}
              prodKey={searchCondition?.productKey}
            />
          ) : (
            <CommonTable
              searchCondition={searchCondition}
              loading={false}
              tableListData={{
                list: tableData?.list || [],
                totalPage: tableData?.pages,
                totalNumber: tableData?.total,
              }}
              columns={formatColumns()}
              rowKey={'groupNo'}
              onPageChange={(paginationData: any) => {
                setSearchCondition({
                  ...searchCondition,
                  pageNum: paginationData.pageNum,
                  pageSize: paginationData.pageSize,
                });
              }}
            ></CommonTable>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(GroupManage);
