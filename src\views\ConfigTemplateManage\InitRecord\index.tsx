import React, { useState, useMemo } from 'react';
import { CommonTable, useTableData } from '@jd/x-coreui';
import { formatLocation } from '@/utils/formatLocation';
import { InitRecordColumns } from '../utils/columns';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import InitRecordDetail from '../components/InitRecordDetail';
import showModal from '@/components/commonModal';
import BreadCrumb from '@/components/BreadCrumb';

const configManageApi = new ConfigTemplateApi();

const InitRecord = () => {
  const { templateNo = '' } = formatLocation(window.location.search);
  const initSearchCondition = {
    templateNo: templateNo,
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState(initSearchCondition);
  const { tableData } = useTableData(
    searchCondition,
    configManageApi.getConfigTemplateInitRecordPage,
  );

  const handleCheckDeviceRange = (record: any) => {
    showModal({
      title: '查看设备范围',
      width: '1200px',
      content: <InitRecordDetail recordId={record?.recordId} />,
      footer: [
        {
          text: '返回',
          type: 'cancelBtn',
          onClick: (cb: () => void) => {
            cb();
          },
        },
      ],
    });
  };

  const formatColumns = useMemo(() => {
    return InitRecordColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any, index: number) => (
            <div className="operate-btn">
              <a onClick={(record: any) => handleCheckDeviceRange(record)}>
                查看设备范围
              </a>
            </div>
          );
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  }, [searchCondition.pageNum, searchCondition.pageSize]);

  const breadCrumbList = [
    { title: '通用设备管理', route: '' },
    { title: '配置模板管理', route: '/configTemplate' },
    { title: '初始化记录查询', route: '' },
  ];

  return (
    <div>
      <BreadCrumb items={breadCrumbList} />
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formatColumns}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </div>
  );
};

export default InitRecord;
