.vehicle_config_contain {

  .vehicle-type-tip {
    display: flex;
    justify-content: center;
  }

  .content {
    margin-top: 10px;
    padding-top: 30px;
    background-color: white;
    display: flex;
    flex-direction: column;

    .select-config {
      // width: 100%;
      text-align: center;

      .selectable {
        border: 1px solid rgb(49, 194, 166);

        .selectable-title {
          background-color: rgb(49, 194, 166);
          height: 60px;
          color: white;
          text-align: center;
          line-height: 60px;
        }

        .selectable-content {
          padding: 10px 15px 20px;

          .operate-btn {
            text-align: center;
            display: flex;
            justify-content: center;

            a {
              display: block;
              margin: 0 10px;
            }

            .active {
              color: #1ABC9C;
            }
          }
        }
      }

      .selected {
        border: 1px solid rgb(49, 194, 166);

        .selected-title {
          background-color: rgb(49, 194, 166);
          height: 60px;
          color: white;
          text-align: center;
          line-height: 60px;
        }

        .selected-content {
          padding: 10px 15px 20px;

          .operate-btn {
            text-align: center;
            display: flex;
            justify-content: center;

            a {
              display: block;
              margin: 0 10px;
            }

            .active {
              color: #1ABC9C;
            }
          }
        }
      }
    }

    .submit_btn {
      font-size: 13px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin: 30px 0;
    }
  }
}