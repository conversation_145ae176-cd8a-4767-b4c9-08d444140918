import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Form, Select, Tabs, message } from 'antd';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

interface ProductOption {
  label: string;
  value: string;
}

interface ModuleOption {
  productModelNo: string;
  productModelName: string;
}

interface ProductModuleSelectorProps {
  selectedData?: any;
}

export interface ProductModuleSelectorRef {
  getSelectedData: () => Promise<{
    productKey: string;
    selectedModules: string[];
  }>;
}

const ProductModuleSelector = forwardRef<ProductModuleSelectorRef, ProductModuleSelectorProps>(
  ({ selectedData }, ref) => {
    const [form] = Form.useForm();
    const deviceApi = new Device();
    
    const [productOptions, setProductOptions] = useState<ProductOption[]>([]);
    const [moduleOptions, setModuleOptions] = useState<ModuleOption[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<string>();
    const [selectedModules, setSelectedModules] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getSelectedData: async () => {
        if (!selectedProduct) {
          throw new Error('请选择产品');
        }
        if (selectedModules.length === 0) {
          throw new Error('请选择至少一个模块');
        }
        return {
          productKey: selectedProduct,
          selectedModules,
        };
      },
    }));

    // 获取产品列表
    useEffect(() => {
      fetchProductList();
    }, []);

    // 当选中数据变化时，同步表单
    useEffect(() => {
      if (selectedData?.productKey) {
        setSelectedProduct(selectedData.productKey);
        form.setFieldsValue({ productKey: selectedData.productKey });
        fetchModuleList(selectedData.productKey);
      }
      if (selectedData?.productModelNoList) {
        setSelectedModules(selectedData.productModelNoList);
      }
    }, [selectedData]);

    const fetchProductList = async () => {
      try {
        const res = await deviceApi.queryProductList();
        if (res.code === HttpStatusCode.Success) {
          const options = res.data?.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
          setProductOptions(options);
        } else {
          message.error(res.message || '获取产品列表失败');
        }
      } catch (error) {
        message.error('获取产品列表失败');
      }
    };

    const fetchModuleList = async (productKey: string) => {
      setLoading(true);
      try {
        const res = await deviceApi.queryModelList(productKey);
        if (res.code === HttpStatusCode.Success) {
          setModuleOptions(res.data || []);
        } else {
          message.error(res.message || '获取模块列表失败');
          setModuleOptions([]);
        }
      } catch (error) {
        message.error('获取模块列表失败');
        setModuleOptions([]);
      } finally {
        setLoading(false);
      }
    };

    const handleProductChange = (value: string) => {
      setSelectedProduct(value);
      setSelectedModules([]);
      setModuleOptions([]);
      if (value) {
        fetchModuleList(value);
      }
    };

    const handleModuleClick = (moduleNo: string) => {
      setSelectedModules(prev => {
        if (prev.includes(moduleNo)) {
          return prev.filter(item => item !== moduleNo);
        } else {
          return [...prev, moduleNo];
        }
      });
    };

    const renderModuleTabs = () => {
      if (moduleOptions.length === 0) {
        return <div className="no-modules">请先选择产品</div>;
      }

      return (
        <div className="module-tabs">
          <div className="module-tabs-header">
            <span className="label">模块选择：</span>
            <span className="tip">（点击选择模块，支持多选）</span>
          </div>
          <div className="module-tabs-content">
            {moduleOptions.map((module) => (
              <div
                key={module.productModelNo}
                className={`module-tab ${
                  selectedModules.includes(module.productModelNo) ? 'selected' : ''
                }`}
                onClick={() => handleModuleClick(module.productModelNo)}
              >
                {module.productModelName}
              </div>
            ))}
          </div>
          {selectedModules.length > 0 && (
            <div className="selected-info">
              已选择 {selectedModules.length} 个模块
            </div>
          )}
        </div>
      );
    };

    return (
      <div className="product-module-selector">
        <div className="section-title">选择产品及模块</div>
        <div className="section-content">
          <Form form={form} layout="vertical">
            <Form.Item
              label="产品"
              name="productKey"
              rules={[{ required: true, message: '请选择产品' }]}
            >
              <Select
                placeholder="请选择产品"
                options={productOptions}
                onChange={handleProductChange}
                style={{ width: 300 }}
                allowClear
              />
            </Form.Item>
          </Form>
          
          {renderModuleTabs()}
        </div>
      </div>
    );
  }
);

ProductModuleSelector.displayName = 'ProductModuleSelector';

export default ProductModuleSelector;
