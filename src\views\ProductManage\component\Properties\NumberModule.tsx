import React from 'react';
import { Form, Input, Select } from 'antd';
import { unitOptions } from '../../utils/column';
import { DataType } from '../../utils/constant';

const NumberModule = React.memo(
  ({
    disabled,
    dataType,
    formInstance,
  }: {
    disabled: boolean;
    dataType: string;
    formInstance: any;
  }) => {
    return (
      <>
        <Form.Item label="取值范围">
          <Form.Item
            label=""
            name="min"
            rules={[
              dataType === DataType.DOUBLE
                ? {
                    pattern: /^-?\d+(\.\d+)?$/,
                    message: '请输入格式正确的数字',
                  }
                : {
                    pattern: /^-?\d+$/,
                    message: '请输入格式正确的数字',
                  },
            ]}
          >
            <Input placeholder="最小值" allowClear disabled={disabled} />
          </Form.Item>
          <Form.Item
            label=""
            name="max"
            rules={[
              dataType === DataType.DOUBLE
                ? {
                    pattern: /^-?\d+(\.\d+)?$/,
                    message: '请输入格式正确的数字',
                  }
                : {
                    pattern: /^-?\d+$/,
                    message: '请输入格式正确的数字',
                  },
            ]}
          >
            <Input placeholder="最大值" allowClear disabled={disabled} />
          </Form.Item>
        </Form.Item>
        <Form.Item
          label="步长"
          name="step"
          rules={[
            dataType === DataType.DOUBLE
              ? {
                  pattern: /^-?\d+(\.\d+)?$/,
                  message: '请输入格式正确的数字',
                }
              : {
                  pattern: /^-?\d+$/,
                  message: '请输入格式正确的数字',
                },
          ]}
        >
          <Input placeholder="请输入步长" allowClear disabled={disabled} />
        </Form.Item>
        <Form.Item label="单位" name="unit">
          <Select
            placeholder="请选择单位"
            disabled={disabled}
            options={unitOptions}
            onChange={(value: any, option: any) => {
              formInstance.setFieldsValue({ unitName: option.label });
            }}
          />
        </Form.Item>
        <Form.Item name="unitName"></Form.Item>
      </>
    );
  },
);

export default NumberModule;
