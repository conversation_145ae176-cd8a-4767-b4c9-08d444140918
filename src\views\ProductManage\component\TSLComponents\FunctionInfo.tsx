import { Col, Form, Input, Radio, Row, Select, Flex, message } from 'antd';
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
} from 'react';
import { DataType } from '../../utils/constant';
import { DataTypeOptions, rwFlagOptions } from '../../utils/column';
import NumberModule from '../Properties/NumberModule';
import EnumModule from '../Properties/EnumModule';
import BoolModule from '../Properties/BoolModule';
import { cloneDeep } from 'lodash';
import ArrayModule from '../Properties/ArrayModule';
import StructModule from './StructModule';
import { TSLFunctionType } from '@/utils/constant';

// 该组件包含物模型某个功能或功能中参数的信息
const FunctionInfo = ({
  level,
  curKey,
  tslRef,
}: {
  level: number;
  curKey: string;
  tslRef: any;
}) => {
  const pageType = tslRef.getTSLInfo('pageType');
  const tslInfo = tslRef.getTSLInfo('tslInfo');
  const curEditLevel = tslRef.getTSLInfo('curEditLevel');
  const maxLevel = tslRef.getTSLInfo('maxLevel');
  const functionType = tslRef.getTSLInfo('functionType');
  const formRef = useRef<any>(tslRef.getTSLInfo('formInstanceList')[level]);
  const dataType = Form.useWatch('dataType', formRef.current);
  const [editable, setEditable] = useState<boolean>(curEditLevel === level); // 编辑层级不是当前组件对应层级时，当前组件的内容是不可以编辑的
  const disabled = pageType === 'check' ? true : !editable;
  let initData = cloneDeep(tslInfo[level][curKey]);
  const [childDataType, setChildDataType] = useState(initData.childDataType);

  let dataTypeOptions = [];
  if (functionType == TSLFunctionType.Properties) {
    dataTypeOptions =
      level < maxLevel - 1
        ? DataTypeOptions
        : DataTypeOptions.filter(
            (v: any) => ![DataType.ARRAY, DataType.STRUCT].includes(v.value),
          );
  } else if (functionType == TSLFunctionType.Server) {
    dataTypeOptions =
      level < maxLevel - 1
        ? DataTypeOptions
        : DataTypeOptions.filter(
            (v: any) => ![DataType.ARRAY, DataType.STRUCT].includes(v.value),
          );
  }

  useEffect(() => {
    formRef.current.setFieldsValue(initData);
  }, [curKey]);

  const changeParentStatus = (v: boolean) => {
    setEditable(v);
  };

  const renderDataType = () => {
    switch (dataType) {
      case DataType.INT:
        return (
          <NumberModule
            disabled={disabled}
            dataType={dataType}
            formInstance={formRef.current}
          />
        );
      case DataType.DOUBLE:
        return (
          <NumberModule
            disabled={disabled}
            dataType={dataType}
            formInstance={formRef.current}
          />
        );
      case DataType.LONG:
        return (
          <NumberModule
            disabled={disabled}
            dataType={dataType}
            formInstance={formRef.current}
          />
        );
      case DataType.DATE:
        return (
          <Form.Item label="时间格式">
            <Input value="String类型的UTC时间戳（毫秒）" disabled />
          </Form.Item>
        );
      case DataType.INT_ENUM:
        return (
          <>
            <EnumModule
              key={Date.now()}
              formInstance={formRef.current}
              initEnumData={initData}
              disabled={disabled}
              type="number"
            />
          </>
        );
      case DataType.ENUM:
        return (
          <EnumModule
            key={Date.now()}
            initEnumData={initData}
            formInstance={formRef.current}
            disabled={disabled}
            type="string"
          />
        );
      case DataType.BOOL:
        return <BoolModule disabled={disabled} />;
      case DataType.TEXT:
        return (
          <Form.Item
            label="数据长度"
            name="length"
            rules={[
              { required: true, message: '数据长度不能为空' },
              {
                pattern: /^(?:10240|[1-9]\d{0,3}|[1-9])$/g,
                message: '字符串长度应为1-10240',
              },
            ]}
          >
            <Input
              placeholder="请输入数据长度"
              allowClear
              suffix="字节"
              disabled={disabled}
            />
          </Form.Item>
        );
      case DataType.ARRAY:
        const initArrStructChildKeyList =
          initData.dataType === DataType.ARRAY ? initData.dataSpecsList : [];
        return (
          <>
            <ArrayModule
              disabled={disabled}
              initInfo={initData}
              formInstance={formRef.current}
              changeChildDataType={(v: DataType) => setChildDataType(v)}
            />
            {childDataType === DataType.STRUCT && (
              <StructModule
                tslRef={tslRef}
                level={level + 1}
                disabled={disabled}
                parentKey={curKey}
                initChildKeyList={initArrStructChildKeyList}
                changeParentStatus={changeParentStatus}
              />
            )}
          </>
        );
      case DataType.STRUCT:
        if (initData.dataType === DataType.ARRAY) {
          delete initData.childDataType;
        }
        const initStructChildKeyList =
          initData.dataType === DataType.STRUCT ? initData.dataSpecsList : [];
        if (level + 1 < maxLevel) {
          return (
            <StructModule
              tslRef={tslRef}
              level={level + 1}
              disabled={disabled}
              parentKey={curKey}
              initChildKeyList={initStructChildKeyList}
              changeParentStatus={changeParentStatus}
            />
          );
        } else {
          return (
            <Form.Item
              label="JSON对象"
              name="JSONObject"
              rules={[
                { required: true, message: '请输入文本' },
                {
                  validator: (_, value) => {
                    if (value[0] !== '[' || value[value.length - 1] !== ']') {
                      return Promise.reject(new Error('JSON文本格式不对'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input.TextArea
                placeholder="请输入文本"
                showCount
                disabled={disabled}
                style={{ height: 120, resize: 'none' }}
              />
            </Form.Item>
          );
        }
    }
  };

  return (
    <div>
      <Form
        key={level}
        form={formRef.current}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 8 }}
      >
        <Form.Item
          label={level > 0 ? '参数名称' : '功能名称'}
          name="name"
          style={{
            display:
              functionType === TSLFunctionType.Server && level === 0
                ? 'none'
                : 'block',
          }}
          rules={[
            { required: true, message: '该名称不能为空' },
            {
              pattern:
                /^[\u4e00-\u9fa5a-zA-Z0-9][\u4e00-\u9fa5a-zA-Z0-9\-_\/.]*$/g,
              message:
                '支持中文、大小写字母、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头，不超过 30 个字符',
            },
          ]}
        >
          <Input
            disabled={disabled}
            maxLength={30}
            placeholder={'请输入您的参数名称'}
          />
        </Form.Item>
        <Form.Item
          label="标识符"
          name="identifier"
          rules={[
            { required: true, message: '标识符不能为空' },
            {
              pattern: /^[a-zA-Z0-9_]+$/g,
              message: '支持大小写字母、数字和下划线、不超过 50 个字符。',
            },
          ]}
          style={{
            display:
              functionType === TSLFunctionType.Server && level === 0
                ? 'none'
                : 'block',
          }}
        >
          <Input
            disabled={disabled}
            maxLength={50}
            placeholder="请输入您的标识符"
          />
        </Form.Item>
        <Form.Item
          label="数据类型"
          name="dataType"
          rules={[{ required: true, message: '数据类型不能为空' }]}
          style={{
            display:
              functionType === TSLFunctionType.Server && level === 0
                ? 'none'
                : 'block',
          }}
        >
          <Select
            disabled={disabled ? disabled : !editable}
            options={dataTypeOptions}
            placeholder="请选择数据类型"
          />
        </Form.Item>
        {dataType && renderDataType()}
      </Form>
    </div>
  );
};

export default React.memo(FunctionInfo);
