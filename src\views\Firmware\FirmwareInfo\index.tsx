import React, { useEffect, useRef, useState } from 'react';
import {
  FirmwareInfoForm,
  AppInfoForm,
  FirmwareInfoTableConfig,
  AppInfoTableConfig,
} from '../utils/column';
import { FirmwareFetch, Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { formatLocation } from '@/utils/formatLocation';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import BreadCrumb from '@/components/BreadCrumb';
import CommonConfirmBtn from '@/components/CommonConfirmBtn';
import { message } from 'antd';
import './index.scss';

const FirmwareInfo = () => {
  const fetchApi = new FirmwareFetch();
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const { type, productKey, productName, appName, appAlias, appEnable } =
    formatLocation(window.location.search);

  const initSearchCondition = useRef<any>({
    searchForm: {
      productKey: { label: productName, value: productKey },
      appName: { label: appAlias, value: appName },
      productModelNo: null,
      appVersionNumber: null,
    },
    pageNum: 1,
    pageSize: 10,
  });
  const [searchCondition, setSearchCondition] = useState<any>(
    initSearchCondition.current,
  );
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition
      ? {
          ...searchCondition?.searchForm,
          type,
          pageNum: searchCondition?.pageNum,
          pageSize: searchCondition?.pageSize,
        }
      : null,
    fetchApi.getVersionInfoList,
    'firmwareInfoPage',
    true,
  );

  const formatColumns = () => {
    const config =
      type === 'app' ? AppInfoTableConfig : FirmwareInfoTableConfig;

    return config?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    const path = type === 'firmware' ? '/firmware' : '/app';
                    navigator(
                      path +
                        '/pushDetail?type=' +
                        type +
                        '&productKey=' +
                        searchCondition.searchForm.productKey.value +
                        '&productName=' +
                        searchCondition.searchForm.productKey.label +
                        '&appName=' +
                        appName +
                        '&appAlias=' +
                        appAlias +
                        '&appVersionNumber=' +
                        record.appVersionNumber +
                        '&appEnable=' +
                        appEnable +
                        '&versionEnable=' +
                        record.enable,
                    );
                  }}
                >
                  推送详情
                </a>
                {appEnable == 1 && record.enable === 1 && (
                  <a
                    onClick={() => {
                      handleAdd('edit', record.appVersionNumber);
                    }}
                  >
                    编辑
                  </a>
                )}
                {record.enable === 1 && appEnable == 1 && (
                  <CommonConfirmBtn
                    message="确认禁用此版本吗，禁用后此版本不能创建升级任务，也不能组成产品包；已生成的产品包不受影响"
                    btnText="禁用"
                    onConfirm={() =>
                      handleEnableAppVersion(record.appVersionNumber, 0)
                    }
                  />
                )}
                {appEnable == 1 && record.enable === 0 && (
                  <CommonConfirmBtn
                    message="确认启用此版本吗?"
                    btnText="启用"
                    onConfirm={() =>
                      handleEnableAppVersion(record.appVersionNumber, 1)
                    }
                  />
                )}
                <CommonConfirmBtn
                  message="确认删除此版本吗?"
                  btnText="删除"
                  onConfirm={() => handleDelAppVersion(record.appVersionNumber)}
                />
                {type === 'app' &&
                  appEnable == 1 &&
                  record.enable === 1 &&
                  record.available === 0 && (
                    <CommonConfirmBtn
                      message="确认上架此版本吗?"
                      btnText="上架"
                      onConfirm={() =>
                        handleChangeListingStatus(record.appVersionNumber, 1)
                      }
                    />
                  )}
                {type === 'app' &&
                  appEnable == 1 &&
                  record.enable === 1 &&
                  record.available === 1 && (
                    <CommonConfirmBtn
                      message="确认下架此版本吗?"
                      btnText="下架"
                      onConfirm={() =>
                        handleChangeListingStatus(record.appVersionNumber, 0)
                      }
                    />
                  )}
                <a
                  onClick={() => {
                    handleDownload(record);
                  }}
                >
                  下载
                </a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const downloadFile = (src: string) => {
    const a = document.createElement('a');
    a.setAttribute('download', '111');
    a.href = src;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  const handleDownload = async (record: any) => {
    const res = await fetchApi.downloadVersionPackage({
      productKey,
      type,
      appName,
      appVersionNumber: record.appVersionNumber,
    });
    if (res.code === HttpStatusCode.Success && res.data?.length > 0) {
      res.data.forEach((url: string, index: number) => {
        setTimeout(() => {
          downloadFile(url);
        }, 500 * index);
      });
    }
  };
  const handleChangeListingStatus = async (
    appVersionNumber: string,
    available: 0 | 1,
  ) => {
    const res = await fetchApi.availableVersion({
      productKey,
      type,
      available,
      appName,
      appVersionNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success('成功');
      reloadTable();
    } else {
      message.error(res.message || '失败');
    }
  };
  const handleEnableAppVersion = async (
    appVersionNumber: string,
    enable: 0 | 1,
  ) => {
    const res = await fetchApi.enableAppVersion({
      productKey,
      type,
      enable,
      appName,
      appVersionNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success('成功');
      reloadTable();
    } else {
      message.error(res.message || '失败');
    }
  };
  const handleDelAppVersion = async (appVersionNumber: string) => {
    const res = await fetchApi.delAppVersion({
      productKey,
      type,
      appName,
      appVersionNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success('成功');
      reloadTable();
    } else {
      message.error(res.message || '失败');
    }
  };

  const onSearchClick = (val: any) => {
    const newValue = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(newValue);
  };
  const onResetClick = () => {
    setSearchCondition(initSearchCondition.current);
  };
  const handleAdd = (
    operateType: 'add' | 'edit',
    appVersionNumber?: string,
  ) => {
    const path = type === 'firmware' ? '/firmware' : '/app';
    navigator(
      path +
        '/addPackage?type=' +
        type +
        '&operateType=' +
        operateType +
        '&appVersionNumber=' +
        appVersionNumber +
        '&productKey=' +
        productKey +
        '&productName=' +
        productName +
        '&appName=' +
        appName +
        '&appAlias=' +
        appAlias +
        '&appEnable=' +
        appEnable +
        '&appVersionNumber=' +
        appVersionNumber,
    );
  };
  const middleBtns: any[] =
    appEnable == 1
      ? [
          {
            show: true,
            title: '添加升级包',
            key: 'addPackage',
            onClick: () => handleAdd('add'),
          },
        ]
      : [];

  return (
    <div className="firmware-info-page">
      <BreadCrumb
        items={
          type === 'firmware'
            ? [
                { title: '通用设备管理', route: '/firmware' },
                { title: '升级包管理', route: '/firmware' },
                {
                  title: type === 'firmware' ? '固件' : '应用',
                  route: '/firmware',
                },
                {
                  title: type === 'firmware' ? '固件信息' : '应用信息',
                  route: '',
                },
              ]
            : [
                { title: '通用设备管理', route: '/app' },
                { title: '升级包管理', route: '/app' },
                { title: '应用', route: '/app' },
                { title: '应用信息', route: '' },
              ]
        }
      />
      <CommonForm
        name="firmware-info-searchForm"
        formConfig={type === 'firmware' ? FirmwareInfoForm : AppInfoForm}
        layout={'inline'}
        defaultValue={initSearchCondition.current.searchForm}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        getFormInstance={(ref: any) => {
          ref?.setFieldsValue(searchCondition.searchForm);
        }}
      />
      <CommonTable
        searchCondition={searchCondition}
        loading={loading}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        middleBtns={middleBtns}
        columns={formatColumns()}
        rowKey={'appVersionNumber'}
        onPageChange={(paginationData: any) => {
          const val = {
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          };
          setSearchCondition(val);
        }}
      ></CommonTable>
    </div>
  );
};

export default React.memo(FirmwareInfo);
