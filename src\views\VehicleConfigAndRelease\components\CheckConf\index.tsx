import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Radio } from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import CompareModal from '@/components/CompareModal';
const { TextArea } = Input;
interface CheckConfProps {
  show: boolean | undefined; // 是否显示
  onCancel: Function; // 取消
  record: any; // 当前要编辑的文件内容
  onSave: Function; // 点击保存
  modalTitle: string | undefined; // 标题
  valueList: any[]; // 所有的文件内容
}
const layout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 21 },
};
const CheckConf = (props: CheckConfProps) => {
  const { show, onCancel, record, onSave, modalTitle, valueList } = props;
  const [form] = Form.useForm();
  const [formRef] = Form.useForm();
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [compareModalShow, setCompareModalShow] = useState<boolean>(false);
  useEffect(() => {
    form.setFieldsValue(record);
  }, []);

  // 点击保存展示内容对比
  const handleCompare = () => {
    if (record?.content !== form.getFieldsValue().content) {
      setCompareModalShow(true);
      return;
    }
    onSubmit();
  };

  // 确定保存修改内容
  const onSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        // 判断是否存在同名字、不同位置的文件
        const list = valueList.filter((item: any) => {
          if (item.name === values.name) return item;
        });
        if (list.length > 1) {
          setModalShow(true);
        } else {
          onSave(
            {
              position: values.position,
              name: values.name,
              content: values.content,
              confTemplateNumber: record.confTemplateNumber,
            },
            0,
          );
          onCancel();
        }
      })
      .catch((errorInfo) => {
        console.log(errorInfo);
      });
  };

  // 确定同时修改
  const onConfirmModify = () => {
    const bothModify = formRef.getFieldsValue().bothModify;
    const data = {
      position: form.getFieldsValue().position,
      name: form.getFieldsValue().name,
      content: form.getFieldsValue().content,
      confTemplateNumber: record.confTemplateNumber,
    };
    if (bothModify === 1 || bothModify === 0) {
      onSave(data, bothModify);
      setModalShow(false);
      onCancel();
      formRef.resetFields();
    }
  };

  return (
    <div>
      <Modal
        title={modalTitle}
        visible={show}
        onCancel={() => {
          onCancel();
        }}
        width={1000}
        footer={
          <div>
            <CustomButton onSubmitClick={handleCompare} title={'保存'} />
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              onSubmitClick={() => {
                onCancel();
              }}
              title={'取消'}
            />
          </div>
        }
      >
        <Form {...layout} form={form}>
          <Form.Item name="name" label="配置名称">
            <Input.TextArea rows={1} disabled />
          </Form.Item>
          <Form.Item name="description" label="用途说明">
            <Input.TextArea rows={1} disabled />
          </Form.Item>
          <Form.Item name="position" label="所在位置">
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="content"
            rules={[{ required: true, message: '请输入配置内容' }]}
            label="配置内容"
          >
            <TextArea rows={20} />
          </Form.Item>
        </Form>
      </Modal>

      {compareModalShow && (
        <CompareModal
          footer={
            <>
              <CustomButton onSubmitClick={onSubmit} title={'确定保存'} />
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  setCompareModalShow(false);
                }}
                title={'返回修改'}
              />
            </>
          }
          onCancel={() => {
            setCompareModalShow(false);
          }}
          compareModalShow={compareModalShow}
          content={{
            rightValue: form.getFieldsValue().content,
            rightTitle: '修改后',
            leftValue: record?.content,
            leftTitle: '修改前',
          }}
        />
      )}

      {modalShow && (
        <Modal
          bodyStyle={{ textAlign: 'center' }}
          title={'提示'}
          okText={'确定'}
          cancelText={'取消'}
          visible={modalShow}
          onCancel={() => {
            setModalShow(false);
          }}
          footer={
            <div>
              <CustomButton
                onSubmitClick={() => onConfirmModify()}
                title={'确定'}
              />
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  setModalShow(false);
                  formRef.resetFields();
                }}
                title={'取消'}
              />
            </div>
          }
        >
          <p>{`control和compute存在一样的文件${record.name}，请确认是否同时修改？`}</p>
          <Form form={formRef}>
            <Form.Item name="bothModify">
              <Radio.Group name="bothModify">
                <Radio value={0}>不需要同时修改</Radio>
                <Radio value={1}>需要同时修改</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );
};

export default React.memo(CheckConf);
