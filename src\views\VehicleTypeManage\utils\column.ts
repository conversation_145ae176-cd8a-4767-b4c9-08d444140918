import { FormConfig } from '@/components/CommonForm/formConfig';
import { dropDownKey, dropDownListKey } from '@/utils/searchFormEnum';

export const VehicleTypeTableData: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '车型id',
    width: 80,
    dataIndex: 'vehicleTypeId',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属产品',
    width: 150,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
    render: (text: any, record: any) => record.productTypeName,
  },
  {
    title: '车型名称',
    width: 390,
    dataIndex: 'vehicleTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置文件数量',
    width: 120,
    dataIndex: 'configurationFileCount',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车型配置状态',
    width: 120,
    dataIndex: 'isConfigName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 120,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 190,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 300,
    fixed: 'right',
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'vehicleType',
      label: '车型名称',
      showSearch: true,
      placeholder: '请输入车型，支持关键字联想全称',
      type: 'select',
      xxl: 12,
      xl: 14,
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请输入关键字',
      type: 'select',
      dropDownKey: dropDownKey.productTypeList,
      dropDownListKey: dropDownListKey.productTypeList,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'isConfig',
      label: '车型配置状态',
      placeholder: '请选择',
      type: 'select',
      dropDownKey: dropDownKey.vehicleTypeConfInfoStateList,
      dropDownListKey: dropDownListKey.vehicleTypeConfInfoStateList,
      specialFetch: 'commonDown',
    },
  ],
};

export const recordColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 40 },
  {
    title: '操作版本号',
    width: 120,
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所在位置',
    width: 60,
    dataIndex: 'position',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置文件名称',
    width: 200,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 100,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 110,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  { title: '操作', dataIndex: 'operate', align: 'center', width: 60 },
];

export const CompareRecordColumns: any[] = [
  {
    title: '操作版本号',
    width: 100,
    dataIndex: 'version',
    key: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作来源',
    width: 65,
    dataIndex: 'operationTypeName',
    key: 'operationTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人',
    width: 90,
    dataIndex: 'modifyUser',
    key: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    width: 90,
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    align: 'center',
    width: 140,
    editable: true,
    fixed: 'right',
  },
];

export const SelectableColumn: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 50 },
  {
    title: '模板编号',
    width: 80,
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所在位置',
    width: 70,
    dataIndex: 'position',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置文件名称',
    width: 150,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 50,
    fixed: 'right',
  },
];

export const SelectedVehicleTypeColumn: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '操作版本号',
    width: 90,
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所在位置',
    width: 90,
    dataIndex: 'position',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置文件名称',
    width: 150,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 120,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 150,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 100,
    fixed: 'right',
  },
];
