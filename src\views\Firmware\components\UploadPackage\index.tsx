import { request } from '@/fetch/core';
import { Button, message, Progress, Upload, UploadProps } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
const SparkMD5 = require('spark-md5');

const UploadPackage = ({
  uploadFileType,
  prefix,
  info,
  onFileChange,
  onStartUpload,
}: {
  uploadFileType: string;
  prefix: string;
  info: any;
  onFileChange: AnyFunc;
  onStartUpload?: AnyFunc;
}) => {
  const calculatePercentRef = useRef<any>(null);
  const [progressPercent, setProgressPercent] = useState<number>(0);
  const [btnDisable, setBtnDisable] = useState(false);
  const [progressInfo, setProgressInfo] = useState<{
    status: 'active' | 'exception' | 'success';
    show: boolean;
  }>({
    status: 'active',
    show: false,
  });
  const [fileInfo, setFileInfo] = useState<any>({
    fileName: info?.S3Key,
  });

  useEffect(() => {
    setFileInfo({
      fileName: info?.S3Key,
    });
  }, [info?.S3Key]);

  const createFileChunk = (file: any, size = 2 * 1024 * 1024) => {
    const fileChunkList = [];
    let cur = 0;
    while (cur < file.size) {
      fileChunkList.push({ file: file.slice(cur, cur + size) });
      cur += size;
    }
    return fileChunkList;
  };

  const getFileMd5Async = (file: any) => {
    let fileHash = null;
    // 这里需要使用异步执行，保证获取到hash后执行下一步
    return new Promise((resolve) => {
      const fileChunks = createFileChunk(file);
      const spark = new SparkMD5.ArrayBuffer();
      let hadReadChunksNum = 0;
      const readFile = (chunkIndex: any) => {
        const fileReader = new FileReader();
        fileReader.readAsArrayBuffer(fileChunks[chunkIndex]?.file);
        fileReader.onload = (e: any) => {
          hadReadChunksNum++;
          spark.append(e.target.result);
          if (hadReadChunksNum === fileChunks.length) {
            fileHash = spark.end();
            fileReader.onload = null;
            resolve(fileHash);
          } else {
            readFile(hadReadChunksNum);
          }
        };
      };
      readFile(0);
    });
  };

  const getS3InfoAsync = (fileName: string) => {
    return request({
      path: '/k2/oss/upload',
      method: 'POST',
      body: {
        fileKey: `${prefix}${fileName}`,
        bucketName: 'rover-operation',
      },
      newGeteway: true,
    });
  };

  const calculatePercent = (size: number) => {
    if (calculatePercentRef.current) {
      clearInterval(calculatePercentRef.current);
    }
    let uploadedSize = 0;
    setProgressPercent(() => 0);
    const speed = (1.8 * 1024 * 1024 * 1024) / 540; // 字节/s
    calculatePercentRef.current = setInterval(() => {
      uploadedSize = uploadedSize + speed * 1;
      const uploaded = parseFloat(((uploadedSize / size) * 100).toFixed(2));
      if (uploaded < 100) {
        setProgressPercent(() => uploaded);
      } else {
        clearInterval(calculatePercentRef.current);
      }
    }, 1000);
  };

  const beforeUploadRequest = (file: File) => {
    setBtnDisable(true); // 按钮置为不可用
    setProgressInfo({
      status: 'active',
      show: true,
    }); // 进度条状态更改
    calculatePercent(file.size); // 创建定时器计算进度条数值
    setFileInfo({
      fileName: file.name,
    });
    onStartUpload && onStartUpload(); // 调用开始上传函数(主要目的是将升级包格式设置为不可用)
  };

  const afterUploadRequest = (status: 'success' | 'fail') => {
    if (status === 'fail') {
      setBtnDisable(false);
      setProgressInfo({
        status: 'exception',
        show: true,
      });
      clearInterval(calculatePercentRef.current);
    } else if (status === 'success') {
      setBtnDisable(false);
      setProgressPercent(100);
      setProgressInfo({
        status: 'success',
        show: true,
      });
      clearInterval(calculatePercentRef.current);
    }
  };

  const getFileExtension = (filename: any) => {
    const parts = filename.split('.');
    if (parts.length > 1) {
      const lastPart = parts.pop();
      const secondLastPart = parts.pop();
      if (secondLastPart === 'tar' && lastPart === 'gz') {
        return '.tar.gz';
      }
      return `.${lastPart}`;
    }
    return '';
  };
  const uploadProps: UploadProps = {
    maxCount: 1,
    accept: uploadFileType,
    openFileDialogOnClick: uploadFileType ? true : false,
    showUploadList: false,
    beforeUpload: (file: any) => {
      if (getFileExtension(file.name) !== uploadFileType) {
        message.warning('文件格式不对');
        return false;
      }
      const fileSize = file.size / 1024 / 1024 / 1024 < 3;
      if (!fileSize) {
        message.warning('文件大小不能超过3G');
        return false;
      }
      return fileSize || Upload.LIST_IGNORE;
    },
    customRequest: async (option: any) => {
      const { file, onSuccess } = option;
      beforeUploadRequest(file);
      Promise.all([getFileMd5Async(file), getS3InfoAsync(file.name)])
        .then((res) => {
          const [md5Info, s3Info]: any = res;
          if (!md5Info) {
            message.error('文件读取失败，请重新上传！');
            return;
          }
          const { uploadUrl, bucketName, fileKey } = s3Info.data;
          request({
            absoluteURL: uploadUrl,
            contentType: 'multipart/form-data',
            method: 'PUT',
            body: file,
            timeout: 900000,
            newGeteway: true,
          })
            .then((res) => {
              afterUploadRequest('success');
              onSuccess(res, file);
              onFileChange({
                s3bucketName: bucketName,
                S3Key: fileKey,
                md5: md5Info,
              });
            })
            .catch((e) => {
              console.log(e);
              afterUploadRequest('fail');
            });
        })
        .catch((e) => {
          console.log(e);
          message.error(e.message);
          afterUploadRequest('fail');
        });
    },
  };
  return (
    <div className="package-upload">
      {info?.type === 'add' && (
        <Upload {...uploadProps}>
          <Button
            type="primary"
            disabled={btnDisable}
            onClick={() => {
              if (!uploadFileType) {
                message.warning('请先选择升级包格式');
              }
            }}
          >
            点击上传
          </Button>
        </Upload>
      )}

      <div className="progress">
        {fileInfo.fileName && (
          <div className="file-name">{fileInfo.fileName}</div>
        )}
        {progressInfo.show && (
          <Progress
            percent={progressPercent}
            percentPosition={{ align: 'center', type: 'outer' }}
            size="small"
            status={progressInfo.status}
          />
        )}
      </div>
    </div>
  );
};

export default React.memo(UploadPackage);
