import React, { useEffect, useRef, useState } from 'react';
import CommonForm from '../CommonForm';
import { FieldItem, FormConfig } from '../CommonForm/formConfig';
import dayjs from 'dayjs';
import CommonTable from '../CommonTable';
import { useTableData } from '../CommonTable/useTableData';
import Device from '@/fetch/bussiness/device';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';
const searchConf: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '产品',
      fieldName: 'productKey',
      placeholder: '请选择产品',
      labelInValue: false,
    },
    {
      type: 'rangeTime',
      label: '操作时间',
      fieldName: 'operateTime',
    },
  ],
};

const column: any[] = [
  {
    title: '产品',
    dataIndex: 'productName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结果',
    dataIndex: 'resultDescription',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'createUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 300,
    ellipsis: true,
  },
];

const deviceApi = new Device();

const RecordTable = () => {
  const searchFormRef = useRef<any>(null);
  const [formConfig, setFormConfig] = useState<FormConfig>(searchConf);
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const startOfDay = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
  const endOfDay = dayjs()
    .endOf('day')
    .subtract(1, 'millisecond')
    .format('YYYY-MM-DD HH:mm:ss');
  const initSearchCondition = {
    productKey: null,
    createTime: [startOfDay, endOfDay],
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] =
    useState<any>(initSearchCondition);
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    deviceApi.queryBatchAddLog,
    'batchAddLog',
    autoFetch,
  );
  const getProductList = () => {
    deviceApi.queryProductList().then((res: any) => {
      if (res.code === HttpStatusCode.Success) {
        const productField = formConfig.fields.find(
          (field: FieldItem) => field.fieldName === 'productKey',
        );
        const productList =
          res?.data?.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
        productField!.options = productList;
        searchFormRef.current?.setFieldValue('productKey', {
          label: res?.data && res?.data[0]?.productName,
          value: res?.data && res?.data[0]?.productKey,
        });
        searchCondition.productKey = res?.data && res?.data[0]?.productKey;
        setSearchCondition({ ...searchCondition });
        setFormConfig({ ...formConfig });
        setAutoFetch(true);
      }
    });
  };

  const formatColumns = () => {
    return column?.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (text: string, record: any) => {
            return (
              <>
                <button
                  onClick={() => {
                    window.open(record.successRecordDownloadUrl, '_self');
                  }}
                  disabled={!record.successRecordDownloadUrl}
                >
                  下载成功列表
                </button>
                <button
                  onClick={() => {
                    window.open(record.failureRecordDownloadUrl, '_self');
                  }}
                  disabled={!record.failureRecordDownloadUrl}
                >
                  下载错误列表
                </button>
              </>
            );
          };
          break;
        default:
          break;
      }
      return col;
    });
  };

  useEffect(() => {
    getProductList();
  }, []);
  return (
    <div className="record-table">
      <CommonForm
        defaultValue={searchCondition.searchForm}
        formConfig={formConfig}
        onSearchClick={() => {
          const values = searchFormRef.current?.getFieldsValue();
          setSearchCondition({
            ...searchCondition,
            ...values,
            productKey: values?.productKey,
            startTime:
              values?.operateTime &&
              values?.operateTime[0]?.format('YYYY-MM-DD HH:mm:ss'),
            endTime:
              values?.operateTime &&
              values?.operateTime[1]?.format('YYYY-MM-DD HH:mm:ss'),
          });
        }}
        onResetClick={() => {
          searchFormRef.current?.resetFields();
          setSearchCondition(initSearchCondition);
        }}
        formType="search"
        getFormInstance={(form: any) => {
          searchFormRef.current = form;
        }}
      />
      <CommonTable
        searchCondition={searchCondition}
        loading={false}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        columns={formatColumns()}
        rowKey={'id'}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      ></CommonTable>
    </div>
  );
};

export default RecordTable;
