import { request } from '@/fetch/core';
class ConfigManagementRequest {
  /**
   * 获取配置模板分页列表
   * @param {string} param.name 配置模板名称
   * @param {string} param.position 配置模板位置
   * @param {string} param.enable 配置模板状态
   * @param {string} param.productType 所属产品
   * @param {number} pageInfo.pageNum 当前页码
   * @param {number} pageInfo.pageSize 每页数量
   * @return {Promise}
   */
  public getConfTemplatePageList = async (
    params: {
      name: string;
      position: string;
      enable: number;
      productType: string;
    },
    pageInfo: {
      pageNum: number;
      pageSize: number;
    },
  ): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/conf_template_page_list`,
      urlParams: {
        pageNum: pageInfo.pageNum,
        pageSize: pageInfo.pageSize,
      },
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 新建配置模板
   * @param {string} params.name 模板名称
   * @param {string} params.description 用途描述
   * @param {string[]} params.positionList 模板位置列表
   * @param {string} params.content 模板内容
   * @param {number} params.enable 模板状态
   * @param {string} params.productType 所属产品
   * @return {Promise}
   */
  public addConfTemplate = (params: {
    name: string;
    description: string;
    positionList: string[];
    content: string;
    enable: number;
    productType: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/add_conf_template`,
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 获取配置模板详情
   * @param {string} param.number 模板编号
   * @return {Promise}
   */
  public getConfTemplateDetail = (params: { number: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: `/ota/web/conf_template_detail`,
      urlParams: {
        number: params.number,
      },
    };
    return request(requestOptions);
  };

  /**
   * 改变配置模板状态
   * @param {string} param.number 模板编号
   * @param {number} param.enable 模板状态 0 无效 1 有效
   * @return {Promise}
   */
  public changeConfTemplateState = (param: {
    number: string;
    enable: number;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'PUT',
      path: `/ota/web/change_conf_template_state`,
      body: param,
    };
    return request(requestOptions);
  };

  /**
   * 删除配置模板
   * @param {string} param.number 模板编号
   * @return {Promise}
   */
  public deleteConfTemplate = (param: { number: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'PUT',
      path: `/ota/web/delete_conf_template`,
      urlParams: {
        number: param.number,
      },
    };
    return request(requestOptions);
  };
}

export default ConfigManagementRequest;
