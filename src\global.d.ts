declare module 'autodll-webpack-plugin';
declare module '@jd/x-coreui';
declare let $: any;
declare module '*.png' {
  const image: string;
  export default image;
}
declare module '*.svg' {
  const image: string;
  export default image;
}

declare type AnyFunc = (...arg: any[]) => any | void;
declare type AnyObj = {
  [key in string | number]: any;
};

// 消掉ts对自定义属性的检查
declare global {
  declare module '@types/react' {
    interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
      clstag?: string;
    }
  }
}

declare module 'file-saver' {
  export function saveAs(data: Blob, filename: string, options?: any): void;
}
