import React from 'react';
import successIcon from '@/assets/images/success_icon.png';
import { useNavigate } from 'react-router-dom';
const UpgradeResult = (props: { issueNumber: string }) => {
  const navigate = useNavigate();
  return (
    <div
      className="upgrade-result"
      style={{
        textAlign: 'center',
        fontSize: '14px',
        lineHeight: '30px',
      }}
    >
      <img src={successIcon} width={40} height={40} />
      <div>成功创建推送计划</div>
      <a
        style={{ color: '#1677ff' }}
        onClick={() => {
          navigate('/releasePlan/detail?issueNumber=' + props.issueNumber);
        }}
      >
        查看升级详情
      </a>
    </div>
  );
};

export default UpgradeResult;
