import { request } from '@/fetch/core';
export class ConfigTemplateApi {
  // 4.1 获取产品下全部启用配置列表
  getAllEnableConfigBaseInfoList = (params: {
    productKey: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_all_enable_config_base_info_list',
      body: { productKey: params.productKey },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: 'ok',
      data: [
        {
          blockNo: 'system_config',
          blockName: '系统配置',
          itemsTypeList: [
            {
              itemTypeNo: 'basic_settings',
              itemTypeName: '基础设置',
              itemList: [
                { itemNo: 'network', itemName: '网络配置' },
                { itemNo: 'storage', itemName: '存储设置' },
                { itemNo: 'display', itemName: '显示设置' },
              ],
            },
            {
              itemTypeNo: 'security',
              itemTypeName: '安全配置',
              itemList: [
                { itemNo: 'firewall', itemName: '防火墙' },
                { itemNo: 'encryption', itemName: '加密设置' },
                { itemNo: 'access_control', itemName: '访问控制' },
              ],
            },
          ],
          fileList: [
            {
              fileNo: 'NET001',
              fileName: '网络配置模板',
              blockNo: 'system_config',
              blockName: '系统配置',
            },
            {
              fileNo: 'SEC001',
              fileName: '安全策略配置',
              blockNo: 'system_config',
              blockName: '系统配置',
            },
            {
              fileNo: 'STO001',
              fileName: '存储策略模板',
              blockNo: 'system_config',
              blockName: '系统配置',
            },
          ],
        },
        {
          blockNo: 'hardware_config',
          blockName: '硬件配置',
          itemsTypeList: [
            {
              itemTypeNo: 'sensor',
              itemTypeName: '传感器配置',
              itemList: [
                { itemNo: 'temp_sensor', itemName: '温度传感器' },
                { itemNo: 'humidity_sensor', itemName: '湿度传感器' },
                { itemNo: 'pressure_sensor', itemName: '压力传感器' },
              ],
            },
            {
              itemTypeNo: 'motor',
              itemTypeName: '电机配置',
              itemList: [
                { itemNo: 'servo_motor', itemName: '伺服电机' },
                { itemNo: 'stepper_motor', itemName: '步进电机' },
              ],
            },
          ],
          fileList: [
            {
              fileNo: 'SEN001',
              fileName: '传感器基础配置',
              blockNo: 'hardware_config',
              blockName: '硬件配置',
              description: '各类传感器参数设置',
              updateTime: '2024-01-15 11:20:00',
            },
            {
              fileNo: 'MOT001',
              fileName: '电机控制参数',
              blockNo: 'hardware_config',
              blockName: '硬件配置',
              description: '电机运动控制参数配置',
              updateTime: '2024-01-14 16:30:00',
            },
          ],
        },
        {
          blockNo: 'application_config',
          blockName: '应用配置',
          itemsTypeList: [
            {
              itemTypeNo: 'ui_config',
              itemTypeName: '界面配置',
              itemList: [
                { itemNo: 'theme', itemName: '主题设置' },
                { itemNo: 'layout', itemName: '布局配置' },
              ],
            },
            {
              itemTypeNo: 'function_config',
              itemTypeName: '功能配置',
              itemList: [
                { itemNo: 'module_a', itemName: '模块A配置' },
                { itemNo: 'module_b', itemName: '模块B配置' },
                { itemNo: 'module_c', itemName: '模块C配置' },
              ],
            },
          ],
          fileList: [
            {
              fileNo: 'UI001',
              fileName: '界面主题配置',
              blockNo: 'application_config',
              blockName: '应用配置',
              description: '系统UI主题相关配置',
              updateTime: '2024-01-15 14:20:00',
            },
            {
              fileNo: 'UI002',
              fileName: '布局模板配置',
              blockNo: 'application_config',
              blockName: '应用配置',
              description: '系统布局相关配置',
              updateTime: '2024-01-14 17:15:00',
            },
            {
              fileNo: 'FUNC001',
              fileName: '功能模块配置',
              blockNo: 'application_config',
              blockName: '应用配置',
              description: '系统功能模块配置参数',
              updateTime: '2024-01-13 10:30:00',
            },
          ],
        },
        {
          blockNo: 'maintenance_config',
          blockName: '维护配置',
          itemsTypeList: [
            {
              itemTypeNo: 'diagnostic',
              itemTypeName: '诊断配置',
              itemList: [
                { itemNo: 'error_code', itemName: '错误码配置' },
                { itemNo: 'log_level', itemName: '日志级别' },
              ],
            },
            {
              itemTypeNo: 'backup',
              itemTypeName: '备份配置',
              itemList: [
                { itemNo: 'backup_schedule', itemName: '备份计划' },
                { itemNo: 'backup_storage', itemName: '备份存储' },
              ],
            },
          ],
          fileList: [
            {
              fileNo: 'DIAG001',
              fileName: '系统诊断配置',
              blockNo: 'maintenance_config',
              blockName: '维护配置',
              description: '系统诊断相关参数设置',
              updateTime: '2024-01-15 16:40:00',
            },
            {
              fileNo: 'BAK001',
              fileName: '备份策略配置',
              blockNo: 'maintenance_config',
              blockName: '维护配置',
              description: '系统备份相关配置',
              updateTime: '2024-01-14 13:20:00',
            },
          ],
        },
      ],
    });
    return request(requestOptions);
  };

  // 4.2 获取产品下指定配置列表明细
  getEnableConfigDetailList = (params: {
    productKey: string;
    blockConfigList: Array<{
      blockNo: string;
      itemNoList: string[];
      fileNoList: string[];
    }>;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_item/get_enable_config_detail_list',
      body: {
        productKey: params.productKey,
        blockConfigList: params.blockConfigList,
      },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: [
        {
          blockNo: 'device_config',
          blockName: '设备配置',
          itemsTypeList: [
            {
              itemTypeNo: 'sensor_config',
              itemTypeName: '传感器配置',
              itemList: [
                {
                  itemNo: 'basic_config',
                  itemName: '基础配置',
                  content: [
                    {
                      identifier: 'sensorEnabled',
                      name: '传感器开关',
                      dataType: 'BOOL',
                      paraOrder: 0,
                      dataSpecsList: [
                        {
                          dataType: 'BOOL',
                          name: '关闭',
                          value: 0,
                        },
                        {
                          dataType: 'BOOL',
                          name: '开启',
                          value: 1,
                        },
                      ],
                      description: '传感器启用状态',
                    },
                    {
                      identifier: 'sensorIds',
                      name: '传感器ID列表',
                      dataType: 'ARRAY',
                      paraOrder: 1,
                      dataSpecs: {
                        dataType: 'ARRAY',
                        childDataType: 'TEXT',
                        size: 10,
                      },
                      description: '支持的传感器ID列表',
                    },
                    {
                      identifier: 'sensorCount',
                      name: '传感器数量',
                      dataType: 'INT',
                      paraOrder: 2,
                      dataSpecs: {
                        dataType: 'INT',
                        max: '100',
                        min: '0',
                        step: '1',
                        unit: '个',
                        unitName: '个数',
                        defaultValue: 1,
                      },
                      description: '传感器数量限制',
                    },
                    {
                      identifier: 'dataRetentionDays',
                      name: '数据保留天数',
                      dataType: 'LONG',
                      paraOrder: 3,
                      dataSpecs: {
                        dataType: 'LONG',
                        max: '365',
                        min: '1',
                        step: '1',
                        unit: '天',
                        unitName: '天数',
                        defaultValue: 30,
                      },
                      description: '传感器数据保留时间',
                    },
                    {
                      identifier: 'sampleRate',
                      name: '采样率',
                      dataType: 'DOUBLE',
                      paraOrder: 4,
                      dataSpecs: {
                        dataType: 'DOUBLE',
                        max: '1000',
                        min: '0.1',
                        step: '0.1',
                        unit: 'Hz',
                        unitName: '赫兹',
                        defaultValue: 1.0,
                      },
                      description: '传感器采样频率',
                    },
                    {
                      identifier: 'sensorName',
                      name: '传感器名称',
                      dataType: 'TEXT',
                      paraOrder: 5,
                      dataSpecs: {
                        dataType: 'TEXT',
                        length: 50,
                      },
                      description: '传感器名称标识',
                    },
                    {
                      identifier: 'lastMaintenanceDate',
                      name: '最后维护时间',
                      dataType: 'DATE',
                      paraOrder: 6,
                      dataSpecs: {
                        dataType: 'DATE',
                        length: 23,
                      },
                      description: '上次维护日期',
                    },
                    {
                      identifier: 'sensorType',
                      name: '传感器类型',
                      dataType: 'ENUM',
                      paraOrder: 7,
                      dataSpecsList: [
                        {
                          dataType: 'ENUM',
                          name: '温度传感器',
                          value: 'temperature',
                        },
                        {
                          dataType: 'ENUM',
                          name: '湿度传感器',
                          value: 'humidity',
                        },
                        {
                          dataType: 'ENUM',
                          name: '压力传感器',
                          value: 'pressure',
                        },
                      ],
                      description: '传感器类型选择',
                    },
                    {
                      identifier: 'priority',
                      name: '优先级',
                      dataType: 'INT_ENUM',
                      paraOrder: 8,
                      dataSpecsList: [
                        {
                          dataType: 'INT_ENUM',
                          name: '低',
                          value: 0,
                        },
                        {
                          dataType: 'INT_ENUM',
                          name: '中',
                          value: 1,
                        },
                        {
                          dataType: 'INT_ENUM',
                          name: '高',
                          value: 2,
                        },
                      ],
                      description: '传感器优先级设置',
                    },
                  ],
                },
                {
                  itemNo: 'password_policy',
                  itemName: '密码策略',
                  content: [
                    {
                      identifier: 'passwordLength',
                      name: '密码长度',
                      dataType: 'DOUBLE',
                      paraOrder: 0,
                      dataSpecs: {
                        dataType: 'DOUBLE',
                        max: '32',
                        min: '8',
                        step: '1',
                        unit: '位',
                        unitName: '字符位数',
                        defaultValue: 12,
                      },
                      description: '密码最小长度要求',
                    },
                    {
                      identifier: 'passwordComplexity',
                      name: '密码复杂度',
                      dataType: 'ARRAY',
                      paraOrder: 1,
                      dataSpecs: {
                        dataType: 'ARRAY',
                        childDataType: 'BOOL',
                        size: 4,
                      },
                      description:
                        '密码复杂度要求[大写字母,小写字母,数字,特殊字符]',
                    },
                  ],
                },
                {
                  itemNo: 'data_encryption',
                  itemName: '数据加密',
                  content: [
                    {
                      identifier: 'encryptionEnabled',
                      name: '加密开关',
                      dataType: 'BOOL',
                      paraOrder: 0,
                      dataSpecsList: [
                        {
                          dataType: 'BOOL',
                          name: '关闭',
                          value: 0,
                        },
                        {
                          dataType: 'BOOL',
                          name: '开启',
                          value: 1,
                        },
                      ],
                      description: '数据加密功能启用状态',
                    },
                  ],
                },
              ],
            },
          ],
          fileList: [
            {
              fileNo: 'SENSOR_001',
              fileName: '传感器配置模板',
              content: '{"version": "1.0.0", "type": "sensor_config"}',
              blockNo: 'device_config',
              blockName: '设备配置',
            },
          ],
        },
        {
          blockNo: 'storage_config',
          blockName: '存储配置',
          itemsTypeList: [
            {
              itemTypeNo: 'disk_management',
              itemTypeName: '磁盘管理',
              itemList: [
                {
                  itemNo: 'disk_partition',
                  itemName: '磁盘分区',
                  content: [
                    {
                      identifier: 'partitionSize',
                      name: '分区大小',
                      dataType: 'DOUBLE',
                      paraOrder: 0,
                      dataSpecs: {
                        dataType: 'DOUBLE',
                        max: '1024',
                        min: '1',
                        step: '1',
                        unit: 'GB',
                        unitName: '千兆字节',
                        defaultValue: 100,
                      },
                      description: '磁盘分区大小设置',
                    },
                    {
                      identifier: 'fileSystem',
                      name: '文件系统',
                      dataType: 'ENUM',
                      paraOrder: 1,
                      dataSpecsList: [
                        { dataType: 'ENUM', name: 'EXT4', value: 'ext4' },
                        {
                          dataType: 'ENUM',
                          name: 'NTFS',
                          value: 'ntfs',
                        },
                        {
                          dataType: 'ENUM',
                          name: 'FAT32',
                          value: 'fat32',
                        },
                      ],
                      description: '文件系统类型选择',
                    },
                  ],
                },
              ],
            },
          ],
          fileList: [
            {
              fileNo: 'STORAGE_001',
              fileName: '存储配置模板',
              content: '{"version": "1.0.0", "type": "storage_config"}',
              blockNo: 'storage_config',
              blockName: '存储配置',
            },
          ],
        },
        {
          blockNo: 'security_config',
          blockName: '安全配置',
          itemsTypeList: [],
          fileList: [
            {
              fileNo: 'SEC_001',
              fileName: '安全策略模板',
              content: '{"version": "1.0.0", "type": "security_config"}',
              blockNo: 'security_config',
              blockName: '安全配置',
            },
          ],
        },
      ],
      traceId: '4438529.66111.17388197119381835',
    });
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: [
        {
          blockNo: 'pda',
          blockName: 'pda',
          itemsTypeList: [
            {
              itemTypeNo: '123',
              itemTypeName: '123',
              itemList: [
                {
                  itemNo: '123',
                  itemName: '123',
                  content: [
                    {
                      identifier: 'wifiOn',
                      name: '开关',
                      dataType: 'BOOL',
                      paraOrder: 0,
                      dataSpecsList: [
                        {
                          dataType: 'BOOL',
                          name: '关',
                          value: 0,
                        },
                        {
                          dataType: 'BOOL',
                          name: '开',
                          value: 1,
                        },
                      ],
                      description: '啊啊啊啊啊啊啊啊啊啊啊啊',
                    },
                    {
                      identifier: 'wifiSsid',
                      name: 'SSID',
                      dataType: 'TEXT',
                      paraOrder: 1,
                      dataSpecs: {
                        dataType: 'TEXT',
                        length: 200,
                      },
                      description: '啊啊啊啊啊啊',
                    },
                    {
                      identifier: 'wifiPwd',
                      name: '密码',
                      dataType: 'DATE',
                      paraOrder: 2,
                      dataSpecs: {
                        dataType: 'DATE',
                        length: 23,
                      },
                      description: '啊啊啊啊啊啊',
                    },
                    {
                      identifier: 'number',
                      name: '数字类型',
                      dataType: 'DOUBLE',
                      direction: 'DOUBLEDOUBLEDOUBLE',
                      paraOrder: 0,
                      dataSpecs: {
                        dataType: 'DOUBLE',
                        max: '100',
                        min: '0',
                        step: '0.01',
                        unit: '%',
                        unitName: '百分比',
                        defaultValue: 0,
                      },
                    },
                    {
                      identifier: 'ENUM',
                      name: '枚举类型',
                      dataType: 'ENUM',
                      direction: 'ENUMENUMENUMENUM',
                      paraOrder: 0,
                      dataSpecsList: [
                        {
                          dataType: 'ENUM',
                          name: 'WIFI',
                          value: 'wifi',
                        },
                        {
                          dataType: 'ENUM',
                          name: '移动4g',
                          value: '4g',
                        },
                        {
                          dataType: 'ENUM',
                          name: '移动5g',
                          value: '5g',
                        },
                      ],
                    },
                    {
                      identifier: 'DOUBLE',
                      name: '布尔类型',
                      dataType: 'DOUBLE',
                      direction: 'DOUBLEDOUBLEDOUBLEDOUBLE',
                      paraOrder: 0,
                      dataSpecsList: [
                        {
                          dataType: 'BOOL',
                          name: '关',
                          value: 0,
                        },
                        {
                          dataType: 'BOOL',
                          name: '开',
                          value: 1,
                        },
                      ],
                    },
                    {
                      identifier: 'ARRAY',
                      name: '数组类型',
                      dataType: 'ARRAY',
                      direction: 'ARRAYARRAYARRAY',
                      paraOrder: 0,
                      dataSpecs: {
                        dataType: 'ARRAY',
                        childDataType: 'TEXT',
                        size: 10,
                      },
                    },
                  ],
                },
                {
                  itemNo: '456',
                  itemName: '456',
                  content: '[]',
                },
              ],
            },
          ],
          fileList: [
            {
              fileNo: '123',
              fileName: '123',
              content: 'XXXX',
              blockNo: 'pda',
              blockName: 'pda',
            },
            {
              fileNo: '123',
              fileName: '123',
              content: 'XXXX',
              blockNo: 'pda',
              blockName: 'pda',
            },
          ],
        },
      ],
      traceId: '4438529.66111.17388197119381835',
    });

    return request(requestOptions);
  };
  // 4.3 查看展示配置文件
  getConfigFile = (params: {
    fileNo: string;
    productKey: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_file/get_config_file',
      body: params,
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: {
        productKey: '1',
        productName: '2',
        fileNo: '3',
        fileName: '4',
        filePath: '5',
        blockNo: '6',
        blockName: '7',
        description: '77',
        content: '7788',
      },
      traceId: '4438529.66111.17388197119381835',
    });
    return request(requestOptions);
  };

  // 4.4 新建配置模板
  addConfigTemplate = (params: {
    productKey: string;
    productModelNoList: string[];
    templateNo: string;
    templateName: string;
    remark?: string;
    blockConfigList: Array<{
      blockNo: string;
      itemList: Array<{ itemNo: string; content: string }>;
      fileList: Array<{ fileNo: string; content: string }>;
    }>;
  }): Promise<any> => {
    // 备注：content是配置项内容JSON字符串，根据配置项参数标识符（key）填写的配置项内容（value）,使用JSON来存储

    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/add_config_template',
      body: params,
      newGeteway: true,
    };
    return Promise.resolve({ code: '0000', message: '成功', data: true });
    return request(requestOptions);
  };

  // 4.5 查看配置模板
  getConfigTemplate = (params: { templateNo: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_config_template',
      body: params,
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: {
        productKey: 'pda',
        productName: 'pda',
        productModelList: [{ productModelNo: '123', productModelName: 'X2P' }],
        templateNo: '11111',
        templateName: '模板一',
        remark: '1312',
        blockConfigList: [
          {
            blockNo: 'pda',
            blockName: 'pda',
            itemTypeList: [
              {
                itemTypeNo: '123',
                itemTypeName: '123',
                itemList: [
                  {
                    itemNo: '123',
                    itemName: '123',
                    content: JSON.stringify([
                      {
                        identifier: 'wifiOn', //参数标识符
                        name: '开关', //参数名称
                        dataType: 'BOOL', //参数类型
                        direction: 'PARAM_INPUT', //参数方向，固定PARAM_INPUT
                        paraOrder: 0, //参数排序
                        dataSpecsList: [
                          //参数类型描述
                          {
                            dataType: 'BOOL',
                            name: '关',
                            value: 0,
                          },
                          {
                            dataType: 'BOOL',
                            name: '开',
                            value: 1,
                          },
                        ],
                        description: '啊啊啊啊啊啊啊啊啊啊啊啊', //参数提示或描述，非必填（此字段为新增字段）
                        value: 1, //参数的值
                      },
                      {
                        identifier: 'wifiSsid',
                        name: 'SSID',
                        dataType: 'TEXT',
                        direction: 'PARAM_INPUT',
                        paraOrder: 0,
                        dataSpecs: {
                          //参数类型描述
                          dataType: 'TEXT',
                          length: 200,
                        },
                        description: '啊啊啊啊啊啊',
                        value: '1234567890', //参数的值
                      },
                      {
                        identifier: 'wifiPwd',
                        name: '密码',
                        dataType: 'TEXT',
                        direction: 'PARAM_INPUT',
                        paraOrder: 1,
                        dataSpecs: {
                          dataType: 'TEXT',
                          length: 200,
                        },
                        description: '啊啊啊啊啊啊',
                        value: '1234567890', //参数的值
                      },
                    ]),
                  },
                  { itemNo: '456', itemName: '123', content: '{}' },
                ],
              },
            ],
            fileList: [
              {
                fileNo: '123',
                fileName: '123',
                content: '{}',
                blockNo: 'pda',
                blockName: 'pda',
              },
            ],
          },
        ],
      },
    });
    return request(requestOptions);
  };

  // 4.7 查看配置文件
  getConfigFileTemplate = (params: {
    templateNo: string;
    fileNo: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_config_file_template',
      body: params,
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: {
        productKey: '',
        productName: '',
        fileNo: '',
        fileName: '',
        filePath: '',
        blockNo: '',
        blockName: '',
        description: '',
        content: '',
      },
    });
    return request(requestOptions);
  };

  // 4.8 编辑配置模板
  editConfigTemplate = (params: {
    templateNo: string;
    remark?: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/edit_config_template',
      body: { templateNo: params.templateNo, remark: params.remark },
      newGeteway: true,
    };
    return Promise.resolve({ code: '0000', message: '成功', data: true });
    return request(requestOptions);
  };

  // 4.9 启用禁用配置模板
  enableConfigTemplate = (params: {
    templateNo: string;
    enable: number;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/enable_config_template',
      body: { templateNo: params.templateNo, enable: params.enable },
      newGeteway: true,
    };
    return Promise.resolve({ code: '0000', message: '成功', data: true });
    return request(requestOptions);
  };

  // 4.11 删除配置模板
  deleteConfigTemplate = (params: { templateNo: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/delete_config_template',
      body: { templateNo: params.templateNo },
      newGeteway: true,
    };
    return Promise.resolve({ code: '0000', message: '成功', data: true });
    return request(requestOptions);
  };

  // 4.13 文件上传校验设备文件内容
  checkInitConfigTemplateDeviceFile = (params: {
    templateNo: string;
    productKey: string;
    productModelNoList: string[];
    fileS3BucketName: string;
    fileS3Key: string;
    fileS3Md5: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/check_init_config_template_device_file',
      body: {
        templateNo: params.templateNo,
        productKey: params.productKey,
        productModelNoList: params.productModelNoList,
        fileS3BucketName: params.fileS3BucketName,
        fileS3Key: params.fileS3Key,
        fileS3Md5: params.fileS3Md5,
      },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: {
        url: 'http://aadad',
        result: true,
        total: 100,
      },
    });
    return request(requestOptions);
  };

  // 4.14 初始化设备配置
  initConfigTemplateToDevice = (params: {
    templateNo: string;
    deviceChoiceInfo: {
      deviceChoiceType: number;
      deviceNameList?: string[];
      deviceNameFileS3Key?: string;
      deviceNameFileS3BucketName?: string;
      productKey?: string;
      productModelNoList?: string[];
      groupNoList?: string[];
      appType?: string;
      appName?: string;
      appVersionNumber?: string;
      deviceName?: string;
    };
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/init_config_template_to_device',
      body: {
        templateNo: params.templateNo,
        deviceChoiceInfo: params.deviceChoiceInfo,
      },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: '成功',
      data: { successTotal: 1, failTotal: 1 },
    });
    return request(requestOptions);
  };

  // 4.15 分页查询设备配置初始化记录
  getConfigTemplateInitRecordPage = (params: {
    pageNum: number;
    pageSize: number;
    templateNo: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_config_template_init_record_page',
      body: {
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        templateNo: params.templateNo,
      },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: 'ok',
      data: {
        pageNum: 1,
        pageSize: 10,
        pages: 1,
        total: 2,
        list: [
          {
            recordId: 123,
            templateNo: 'ABC',
            templateName: 'ABC',
            createTime: '2025-01-01 00:00:00',
            createUser: 'ABC',
          },
        ],
      },
    });
    return request(requestOptions);
  };

  // 4.16 分页查询设备配置初始化记录设备明细
  getConfigTemplateInitDeviceDetailPage = (params: {
    recordId: string;
    pageNum: number;
    pageSize: number;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_config_template_init_device_detail_page',
      body: {
        recordId: params.recordId,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: 'ok',
      data: {
        pageNum: 1,
        pageSize: 10,
        pages: 1,
        total: 2,
        list: [
          {
            productKey: 'ABC',
            productName: 'ABC',
            productModelNo: 'ABC',
            productModelName: 'ABC',
            deviceName: 'ABC',
            groupFullName: 'ABC',
            groupNo: 'ABC',
            result: 'ABC',
            resultName: 'ABC',
          },
          {
            productKey: 'ABC',
            productName: 'ABC',
            productModelNo: 'ABC',
            productModelName: 'ABC',
            deviceName: 'ABC',
            groupFullName: 'ABC',
            groupNo: 'ABC',
            result: 'ABC',
            resultName: 'ABC',
          },
          {
            productKey: 'ABC',
            productName: 'ABC',
            productModelNo: 'ABC',
            productModelName: 'ABC',
            deviceName: 'ABC',
            groupFullName: 'ABC',
            groupNo: 'ABC',
            result: 'ABC',
            resultName: 'ABC',
          },
        ],
      },
    });
    return request(requestOptions);
  };

  // 4.17 分页查询配置模板
  getConfigTemplatePage = (params: {
    pageNum: number;
    pageSize: number;
    productKey: string;
    productModelNo?: string;
    templateNo?: string;
    templateName?: string;
    createUser?: string;
    createTimeStart?: string;
    createTimeEnd?: string;
    enable?: number;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_config_template_page',
      body: {
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        productKey: params.productKey,
        productModelNo: params.productModelNo,
        templateNo: params.templateNo,
        templateName: params.templateName,
        createUser: params.createUser,
        createTimeStart: params.createTimeStart,
        createTimeEnd: params.createTimeEnd,
        enable: params.enable,
      },
      newGeteway: true,
    };
    return Promise.resolve({
      code: '0000',
      message: 'ok',
      data: {
        pageNum: 1,
        pageSize: 10,
        pages: 1,
        total: 2,
        list: [
          {
            productKey: 'pda',
            productName: 'ABC',
            templateNo: 'ABC',
            templateName: 'ABC',
            blockNo: 'ABC',
            blockName: 'ABC',
            enable: 123,
            enableName: 'ABC',
            createTime: '2025-01-01 00:00:00',
            modifyUser: 'ABC',
            modifyTime: '2025-01-01 00:00:00',
            remark: 'ABC',
            productModelList: [
              {
                productModelNo: 'DM0066711247128579',
                productModelName: 'X2P',
              },
              {
                productModelNo: 'm00002',
                productModelName: 'W1',
              },
            ],
          },
        ],
      },
    });
    return request(requestOptions);
  };
}
