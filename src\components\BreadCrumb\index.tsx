import React from 'react'
import { Breadcrumb } from 'antd';
import { useNavigate } from 'react-router';

export interface BreadCrumbItem {
  title: string,
  route: string
}

const BreadCrumb = ({ items }: { items: BreadCrumbItem[] | undefined }) => {
  const navigator = useNavigate();
  return (
    <div>
      <Breadcrumb>
        {items?.map((item, index) => {
          return (
            <Breadcrumb.Item
              key={item.title}
            >
              <a
                onClick={() => {
                  if (item.route) {
                    navigator(item.route);
                  } else {
                    if (index < items.length - 1) {
                      window.history.go(-(Math.max(index - 1, 1)))
                    }
                  }
                }}>
                {item.title}
              </a>
            </Breadcrumb.Item>
          )
        })}
      </Breadcrumb>
    </div>
  )
}
export default BreadCrumb;