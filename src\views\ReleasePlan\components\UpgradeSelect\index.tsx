import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import './index.scss';
import { UpgradeForm } from '../../utils/constant';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { CommonTable, useTableData, CommonForm } from '@jd/x-coreui';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import * as releaseApi from '@/fetch/bussiness/releasePlanning';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
const deviceApi = new Device();
const UpgradeSelect = forwardRef((props: any, ref: any) => {
  const createOTATask = useSelector((state: RootState) => state.createOTATask);
  const formRef = useRef<any>(null);
  const [defaultValue, setDefaultValue] = useState<any>(null);
  const [formConfig, setFormConfig] = useState<FormConfig>(UpgradeForm);
  const getFieldByName = (fieldName: string) => {
    return formConfig.fields.find(
      (field: FieldItem) => field.fieldName === fieldName,
    );
  };
  const getProductList = () => {
    deviceApi.queryProductList().then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        const productKey = getFieldByName('productKey');
        productKey!.options = res.data.map((item: any) => ({
          label: item.productName,
          value: item.productKey,
        }));

        setFormConfig({
          ...formConfig,
        });
        setDefaultValue({
          productKey: createOTATask.info?.productKey
            ? createOTATask.info?.productKey
            : res.data && res.data[0]?.productKey,
          appType: createOTATask.info?.appType,
          appName: createOTATask.info?.appName,
        });
      }
    });
  };

  const getAppTypeList = () => {
    releaseApi.getAppTypeList().then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        const appType = getFieldByName('appType');
        appType!.options = res.data.map((item: any) => ({
          label: item.name,
          value: item.value,
        }));

        setFormConfig({
          ...formConfig,
        });
      }
    });
  };

  const getFormValues = () => {
    return formRef.current.getFieldsValue();
  };
  const validateFormValues = () => {
    return formRef.current.validateFields();
  };
  useImperativeHandle(ref, () => {
    return {
      getFormValues,
      validateFormValues,
    };
  });
  useEffect(() => {
    getProductList();
    getAppTypeList();
  }, []);

  useEffect(() => {
    if (createOTATask.info) {
      formRef.current.setFieldsValue(createOTATask.info);
    }
  }, [createOTATask.info]);

  return (
    <div className="upgrade-select-container">
      <CommonForm
        defaultValue={defaultValue}
        getFormInstance={(ref: any) => {
          formRef.current = ref;
        }}
        layout="inline"
        formConfig={formConfig}
      />
    </div>
  );
});

export default UpgradeSelect;
