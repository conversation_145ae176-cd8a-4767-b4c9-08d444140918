import { FormConfig } from '@/components/CommonForm/formConfig';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  getTaskStatus,
  Device,
  getAppVersionListByModel,
  getAppTypeList,
  getAppTypeListByType,
  FirmwareFetch,
} from '@/fetch/bussiness';
import { FormInstance } from 'antd';
const deviceApi = new Device();
const firmwareApi = new FirmwareFetch();

export const TableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 100 },
  {
    title: '发布计划编号',
    width: 120,
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品型号',
    width: 100,
    dataIndex: 'productModelNames',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布内容',
    width: 150,
    dataIndex: 'appInfoList',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间设置',
    width: 120,
    dataIndex: 'immediatelyName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    dataIndex: 'issueTime',
    align: 'center',
    width: 170,
    ellipsis: true,
  },
  {
    title: '发布计划状态',
    width: 120,
    dataIndex: 'issueStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布数量',
    width: 90,
    dataIndex: 'deviceCount',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    width: 120,
    dataIndex: 'createUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    width: 170,
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 190,
    dataIndex: 'operate',
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      labelInValue: false,
      allowClear: false,
      options: [],
    },
    {
      fieldName: 'productModelNo',
      label: '产品型号',
      placeholder: '请选择产品型号',
      type: 'select',
      options: [],
    },
    {
      label: '升级版本选择',
      childrenList: ['appType', 'appName', 'appVersionNumber'],
      xxl: 10,
      xl: 10,
      lg: 14,
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'appType',
      type: 'select',
      placeholder: '请选择升级包类型',
      marginLeft: 0,
      marginRight: 0,
      width: '25%',
      isChild: true,
      options: [],
    },
    {
      fieldName: 'appName',
      type: 'select',
      placeholder: '请选择升级包名称',
      marginLeft: 0,
      marginRight: 0,
      width: '35%',
      isChild: true,
      options: [],
    },
    {
      fieldName: 'appVersionNumber',
      type: 'select',
      placeholder: '请选择版本号',
      marginLeft: 0,
      marginRight: 0,
      width: '40%',
      isChild: true,
      options: [],
    },
    {
      fieldName: 'issueTaskNumber',
      label: '发布计划编号',
      placeholder: '请输入发布计划编号',
      type: 'input',
      xxl: 5,
      xl: 14,
      lg: 10,
    },
    {
      fieldName: 'issueTaskStatus',
      label: '发布计划状态',
      placeholder: '请选择发布计划状态',
      type: 'select',
      options: [],
      xxl: 5,
      xl: 14,
      lg: 10,
    },
    {
      fieldName: 'createUser',
      label: '创建人',
      placeholder: '请输入创建人账号',
      type: 'input',
    },
    {
      fieldName: 'time',
      label: '发布时间',
      type: 'rangeTime',
      xxl: 8,
      xl: 12,
      lg: 16,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'issueTaskStatus',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await getTaskStatus();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.value,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appType',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: any) => {
          if (!val) {
            return [];
          }
          const res = await getAppTypeList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.value,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appType',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNo',
        rule: 'clear',
      },
    ],
    appType: [
      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
      {
        linkFieldName: 'appName',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue(true);
          const res = await getAppTypeListByType({
            productKey: values?.productKey,
            type: val.value,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.appAlias,
              value: item.appName,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue(true);
          const res = await firmwareApi.getVersionList({
            productKey: values?.productKey,
            appName: values?.appName?.value,
            type: values?.appType?.value,
            productModelNoList: values?.productModelNo?.value
              ? [values?.productModelNo?.value]
              : [],
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
    ],
  },
};
