import { DataType } from '@/views/ProductManage/utils/constant';
export enum CommandTaskStatus {
  creating = 0, // 创建中
  to_be_effective = 1, // 未生效
  already_effective = 2, // 已生效
  canceled = 3, // 已取消
  create_fail = 4, // 创建失败
}

export enum DeviceStatus {
  to_be_effective = 0, // 待生效
  not_received = 1, // 未接收
  received = 2, // 已接收
  success = 3, // 成功
  expired = 4, // 过期
  failed = 5, // 失败
  stop = 6, // 终止
}

export enum DeviceChoiceType {
  directional_select = 0,
  file_upload = 1,
  condition_select = 2,
}

const formatArrayStruct = (k: any, v: any) => {
  const data = [];
  const kList = k.split('-');
  data[kList[0]] = {};
  const { key, value } = formatStruct(kList.slice(1).join('-'), v);
  return {
    key: kList[0],
    index: Number(key) - 1,
    value,
  };
};

const formatArray = (k: any, v: any) => {
  const kList = k.split('-');
  return {
    key: kList[0],
    value: kList[1] && v ? [v] : [],
  };
};

const formatStruct = (k: any, v: any) => {
  const kList = k.split('-');
  return {
    key: kList[0],
    value: kList[1] ? { [kList[1]]: v } : {},
  };
};

export const formatInputParams = (val: AnyObj) => {
  const formatData: any = {};
  const keys = Object.keys(val || {});
  keys.forEach((k) => {
    const typeList = k.split(':');
    const v = val[k];
    if (
      typeList.includes(DataType.ARRAY) &&
      !typeList.includes(DataType.STRUCT)
    ) {
      const { key, value } = formatArray(typeList.slice(-1)[0], v);
      formatData[key] = (formatData[key] || []).concat(value);
    } else if (
      !typeList.includes(DataType.ARRAY) &&
      typeList.includes(DataType.STRUCT)
    ) {
      formatStruct(typeList.slice(-1)[0], v);
      const { key, value } = formatStruct(typeList.slice(-1)[0], v);
      formatData[key] = { ...formatData[key], ...value };
    } else if (
      typeList.includes(DataType.ARRAY) &&
      typeList.includes(DataType.STRUCT)
    ) {
      const { key, index, value } = formatArrayStruct(typeList.slice(-1)[0], v);
      if (!formatData[key]) {
        formatData[key] = [];
      }
      formatData[key][index] = {
        ...formatData[key][index],
        ...value,
      };
    } else {
      formatData[k] = v;
    }
  });
  return formatData;
};
