import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatLocation } from '@/utils/formatLocation';
import BreadCrumb from '@/components/BreadCrumb';
import FormTitle from '@/components/FormTitle';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message, Modal, Form, Radio } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import SelectConfTable from '@/components/SelectConfTable';
import CheckConf from '@/components/CheckConf';
import VehicleTypeManageRequest from '@/fetch/bussiness/vehicleTypeManage';
const fetchApi = new VehicleTypeManageRequest();
const BreadCrumbItemsMap = new Map([
  [
    'vehicleTypeManage',
    [
      { title: 'OTA管理', route: '' },
      { title: '车型配置管理', route: '' },
      { title: '车型配置', route: '' },
    ],
  ],
]);
const VehicleTypeConfig = () => {
  const navigator = useNavigate();
  const [formRef] = Form.useForm();
  const [selectedConfFrom] = Form.useForm();
  const [selectableConf, setSelectableConf] = useState<any[]>([]);
  const [initSelectedConf, setInitSelectedConf] = useState<any[]>([]);
  const urlData = formatLocation(window.location.search);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [selectableLoading, setSelectableLoading] = useState<boolean>(false);
  const [selectedLoading, setSelectedLoading] = useState<boolean>(false);
  const [checkConfShow, setCheckConfShow] = useState<boolean>(false);
  const [checkModaltype, setCheckModalType] = useState<string>();
  const [modalContent, setModalContent] = useState<any>();
  const [checkConfTitle, setCheckConfTitle] = useState<string>('');
  const [editId, setEditId] = useState<any>();
  const [coverModalShow, setCoverModalShow] = useState<boolean>(false);
  const [editedContent, setEditedContent] = useState<any>();
  const selectableColumn: any[] = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: 30,
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '所属产品',
      width: 50,
      dataIndex: 'productTypeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '模板编号',
      width: 80,
      dataIndex: 'number',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '所在位置',
      width: 50,
      dataIndex: 'position',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '配置文件名称',
      width: 150,
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
      width: 50,
      fixed: 'right',
      render: (item: any, record: any) => {
        return (
          <div className="operate-btn">
            <a
              onClick={() => {
                fetchSelectableConfContent(record);
                setCheckModalType('check');
                setCheckConfShow(true);
                setCheckConfTitle('查看配置文件');
              }}
            >
              查看
            </a>
          </div>
        );
      },
    },
  ];
  const selectedColumn: any[] = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: 50,
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '所在位置',
      width: 70,
      dataIndex: 'position',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '配置文件名称',
      width: 200,
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '最后操作人',
      width: 90,
      dataIndex: 'modifyUser',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '最后操作时间',
      width: 130,
      dataIndex: 'modifyTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (item: any, record: any) => {
        return (
          <div className="operate-btn">
            <a
              onClick={() => {
                setCheckConfShow(true);
                setCheckModalType('edit');
                setCheckConfTitle(`编辑车型配置-[${urlData.vehicleTypeName}]`);
                fetchSelectedConfContent(record);
                setEditId(record.id);
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                onDel(record);
              }}
            >
              删除
            </a>
          </div>
        );
      },
    },
  ];
  useEffect(() => {
    fetchCurrentSelectedConf(null);
    fetchSelectableConf('');
  }, [urlData.vehicleTypeName]);

  const fetchSelectableConf = async (name: any) => {
    setSelectableLoading(true);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    try {
      const res: any = await fetchApi.getConfList({
        name,
        productType: urlData.productType,
      });
      if (res && res.code === HttpStatusCode.Success) {
        setSelectableConf(res.data);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setSelectableLoading(false);
    }
  };
  const fetchCurrentSelectedConf = async (name: any) => {
    setSelectedLoading(true);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    try {
      const res = await fetchApi.getSelectedVehicleTypeConf({
        vehicleTypeId: urlData.vehicleTypeId,
        name: name,
      });
      if (res && res.code === HttpStatusCode.Success) {
        setInitSelectedConf(res.data);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setSelectedLoading(false);
    }
  };
  const fetchSelectableConfContent = (record: any) => {
    fetchApi
      .getConfTemplateDetail({
        number: record.number,
      })
      .then((res) => {
        if (res && res.code === HttpStatusCode.Success) {
          setModalContent(res.data);
        }
      })
      .catch((err) => {});
  };
  const fetchSelectedConfContent = (record: any) => {
    fetchApi
      .getVehicleTypeConfDetail({
        id: record.id,
      })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setModalContent(res.data);
        }
      })
      .catch((err) => {});
  };
  const onAdd = () => {
    if (selectedRows && selectedRows.length > 0) {
      const selectedRowsMap = new Map();
      selectedRows?.forEach((element: any) => {
        selectedRowsMap.set(element.name, element.position);
      });
      let repeatList: any[] = [];
      initSelectedConf?.forEach((value: any) => {
        if (
          selectedRowsMap.get(value.confTemplateNumber) &&
          selectedRowsMap.get(value.confTemplateNumber).position ===
            value.position &&
          selectedRowsMap.get(value.confTemplateNumber).name === value.name
        ) {
          repeatList.push(value.name);
        }
      });
      if (repeatList.length > 0) {
        message.error({
          content: `${repeatList.toString()}已存在，不允许重复添加！`,
          style: {
            wordBreak: 'break-all',
            wordWrap: 'break-word',
          },
        });
        return;
      } else {
        fetchApi
          .addVehicleTypeConf({
            vehicleTypeId: urlData.vehicleTypeId,
            numberList: selectedRowKeys,
          })
          .then((res: any) => {
            if (res && res.code === HttpStatusCode.Success) {
              message.success(res.message);
              fetchCurrentSelectedConf(
                selectedConfFrom.getFieldsValue().inputContainer,
              );
              setSelectedRowKeys([]);
              setSelectedRows([]);
            }
          })
          .catch((err) => {});
      }
    } else {
      message.error('未选中配置，添加失败！');
    }
  };
  const onDel = (record: any) => {
    Modal.confirm({
      title: (
        <p style={{ wordBreak: 'break-all', textAlign: 'left' }}>
          请确认删除{record.name}吗？
        </p>
      ),
      icon: <ExclamationCircleOutlined />,
      onCancel: () => {},
      onOk() {
        fetchApi
          .deleteVehicleTypeConf({
            id: record.id,
          })
          .then((res: any) => {
            if (res && res.code === HttpStatusCode.Success) {
              message.success(res.message);
              fetchCurrentSelectedConf(
                selectedConfFrom.getFieldsValue().inputContainer,
              );
            }
          })
          .catch((err) => {});
      },
    });
  };
  // 点击保存后先判断是否存在相同文件
  const onSubmit = (data: any) => {
    fetchApi
      .existSameVehicleTypeInfo({
        id: editId,
      })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          if (res.data) {
            setCoverModalShow(true);
            setEditedContent(data);
          } else {
            saveEdit(data, 0);
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const saveEdit = (data: any, bothModify: any) => {
    fetchApi
      .editVehicleTypeConf({
        id: editId,
        name: data.name,
        description: data.description,
        content: data.content,
        bothModify: bothModify,
      })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          message.success(res.message);
          fetchCurrentSelectedConf(
            selectedConfFrom.getFieldsValue().inputContainer,
          );
          setCheckConfShow(false);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const onConfirmModify = () => {
    // 必填项
    const bothModify = formRef.getFieldsValue().bothModify;
    if (bothModify === 0 || bothModify === 1) {
      saveEdit(editedContent, bothModify);
      setCoverModalShow(false);
      setCheckConfShow(false);
      formRef.resetFields();
    }
  };

  console.log(urlData);
  return (
    <div className="vehicle_config_contain">
      <BreadCrumb items={BreadCrumbItemsMap.get('vehicleTypeManage')} />
      <div className="content">
        <FormTitle title={`${urlData.vehicleTypeName}车型配置`} />
        <div className="select-config" style={{ marginTop: '50px' }}>
          <SelectConfTable
            title={'可选车型配置'}
            onSearchClick={(inputName: any) => fetchSelectableConf(inputName)}
            dataSource={selectableConf}
            loading={selectableLoading}
            tableColumns={selectableColumn}
            isSelect={true}
            selectedRowKeys={selectedRowKeys}
            selectedRows={selectedRows}
            saveSelectedConf={(selectedRowKeys: any, selectedRows: any) => {
              setSelectedRowKeys(selectedRowKeys);
              setSelectedRows(selectedRows);
            }}
            columnWidth={'16px'}
            rowKey={'number'}
          />
          <div
            className="submit-btn"
            style={{ marginTop: '50px', marginBottom: '50px' }}
          >
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              onSubmitClick={onAdd}
              height={50}
              otherCSSProperties={{ width: '300px' }}
              title={'确认添加'}
            />
          </div>
          <SelectConfTable
            title={'已添加车型配置'}
            onSearchClick={(inputName: any) =>
              fetchCurrentSelectedConf(inputName)
            }
            dataSource={initSelectedConf}
            loading={selectedLoading}
            tableColumns={selectedColumn}
            isSelect={false}
            rowKey={'id'}
            formRef={selectedConfFrom}
          />
        </div>
        <div className="submit_btn">
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => navigator('/vehicleTypeManage')}
            title={'返回'}
          />
        </div>
      </div>
      {checkConfShow && (
        <CheckConf
          title={checkConfTitle}
          show={checkConfShow}
          onCancel={() => {
            setCheckConfShow(false);
          }}
          content={modalContent}
          type={checkModaltype}
          onSubmit={(data: any) => onSubmit(data)}
        />
      )}
      {coverModalShow && (
        <Modal
          bodyStyle={{ textAlign: 'center' }}
          title={'提示'}
          okText={'确定'}
          cancelText={'取消'}
          visible={coverModalShow}
          onCancel={() => {
            setCoverModalShow(false);
          }}
          footer={
            <div>
              <CustomButton
                onSubmitClick={() => onConfirmModify()}
                title={'确定'}
              />
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  setCoverModalShow(false);
                  formRef.resetFields();
                }}
                title={'取消'}
              />
            </div>
          }
        >
          <p>control和compute存在一样的文件名，请确认是否同时修改？</p>
          <Form form={formRef}>
            <Form.Item name="bothModify">
              <Radio.Group name="bothModify">
                <Radio value={0}>不需要同时修改</Radio>
                <Radio value={1}>需要同时修改</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );
};

export default React.memo(VehicleTypeConfig);
