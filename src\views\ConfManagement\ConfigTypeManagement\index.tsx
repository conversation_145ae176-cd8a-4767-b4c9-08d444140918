import React, { useState, useRef, useEffect } from 'react';
import { Table, Button, Form, Input, Select, message, Space } from 'antd';

import BreadCrumb from '@/components/BreadCrumb';
import { ConfigTypeRequest } from '@/fetch/bussiness';
import Device from '@/fetch/bussiness/device';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

// 面包屑导航项
const breadCrumbItems = [
  { title: '通用设备管理', route: '/confManagement' },
  { title: '配置项管理', route: '/confManagement' },
  { title: '结构化配置项', route: '/confManagement' },
  { title: '配置类管理', route: '' },
];

// 配置类项接口
interface ConfigTypeItem {
  id?: number;
  productKey: string;
  productName?: string;
  itemTypeNo: string;
  name: string;
  description?: string;
  isEditing?: boolean;
  isNew?: boolean;
}

const ConfigTypeManagement: React.FC = () => {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<ConfigTypeItem[]>([]);
  const [productOptions, setProductOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const configTypeRequest = useRef(new ConfigTypeRequest());

  // 获取产品列表和配置类列表
  useEffect(() => {
    fetchProductList();
    fetchConfigTypeList();
  }, []);

  // 获取产品列表数据
  const fetchProductList = async () => {
    try {
      setLoading(true);
      const deviceInstance = new Device();
      const res = await deviceInstance.queryProductList();

      if (res && res.code === HttpStatusCode.Success && res.data) {
        const options = res.data.map((item: any) => ({
          label: item.productName,
          value: item.productKey,
        }));
        setProductOptions(options);
      } else {
        message.error(res.message || '获取产品列表失败');
      }
    } catch (error) {
      message.error('获取产品列表出错');
    } finally {
      setLoading(false);
    }
  };

  // 获取配置类列表
  const fetchConfigTypeList = async () => {
    try {
      setLoading(true);
      const res = await configTypeRequest.current.getConfigTypePage();

      if (res && res.code === HttpStatusCode.Success && res.data) {
        // 转换数据格式
        const configTypes = res.data.list || [];
        setDataSource(
          configTypes.map((item: any) => ({
            id: item.id,
            productKey: item.productKey,
            productName: item.productName,
            itemTypeNo: item.itemTypeNo,
            name: item.name,
            description: item.description || '',
          })),
        );
      } else {
        setDataSource([]);
      }
    } catch (error) {
      message.error('获取配置类列表出错');
      setDataSource([]);
    } finally {
      setLoading(false);
    }
  };

  // 添加新行
  const handleAdd = () => {
    // 检查是否已经有正在编辑的行
    const editingRow = dataSource.find((item) => item.isEditing || item.isNew);
    if (editingRow) {
      message.warning('请先完成当前编辑');
      return;
    }

    const newItem: ConfigTypeItem = {
      productKey: '',
      itemTypeNo: '',
      name: '',
      description: '',
      isEditing: true,
      isNew: true,
    };

    setDataSource([...dataSource, newItem]);
  };

  // 保存行
  const handleSave = async (record: ConfigTypeItem) => {
    try {
      // 验证表单
      await form.validateFields();
      const values = form.getFieldsValue();

      // 获取表单值

      // 如果是新增行，调用新增接口
      if (record.isNew) {
        const res = await configTypeRequest.current.addConfigType({
          productKey: values.productKey,
          itemTypeNo: values.itemTypeNo,
          name: values.name,
          description: values.description,
        });

        if (
          res &&
          (res.code === HttpStatusCode.Success ||
            res.code === 200 ||
            res.code === '200')
        ) {
          message.success('保存成功');

          // 重新加载配置类列表
          fetchConfigTypeList();
        } else {
          message.error(res.message || '保存失败');
        }
      } else {
        // 如果是编辑行，调用更新接口
        const res = await configTypeRequest.current.updateConfigType({
          id: record.id!.toString(),
          productKey: values.productKey,
          itemTypeNo: values.itemTypeNo,
          name: values.name,
          description: values.description,
        });

        if (
          res &&
          (res.code === HttpStatusCode.Success ||
            res.code === 200 ||
            res.code === '200')
        ) {
          message.success('更新成功');

          // 重新加载配置类列表
          fetchConfigTypeList();
        } else {
          message.error(res.message || '更新失败');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 取消编辑
  const handleCancel = (record: ConfigTypeItem) => {
    if (record.isNew) {
      // 如果是新增行，直接从数据源中移除
      setDataSource(dataSource.filter((item) => !item.isNew));
    } else {
      // 如果是编辑行，恢复原始数据
      const updatedDataSource = dataSource.map((item) => {
        if (item.id === record.id) {
          return { ...item, isEditing: false };
        }
        return item;
      });
      setDataSource(updatedDataSource);
    }
    form.resetFields();
  };

  // 编辑行
  const handleEdit = (record: ConfigTypeItem) => {
    // 检查是否已经有正在编辑的行
    const editingRow = dataSource.find((item) => item.isEditing || item.isNew);
    if (editingRow) {
      message.warning('请先完成当前编辑');
      return;
    }

    // 设置表单初始值
    form.setFieldsValue({
      productKey: record.productKey,
      itemTypeNo: record.itemTypeNo,
      name: record.name,
      description: record.description,
    });

    // 更新数据源，将当前行设置为编辑状态
    const updatedDataSource = dataSource.map((item) => {
      if (item.id === record.id) {
        return { ...item, isEditing: true };
      }
      return item;
    });
    setDataSource(updatedDataSource);
  };

  // 删除行
  const handleDelete = async (record: ConfigTypeItem) => {
    try {
      const res = await configTypeRequest.current.deleteConfigType(
        record.id!.toString(),
      );

      if (
        res &&
        (res.code === HttpStatusCode.Success ||
          res.code === 200 ||
          res.code === '200')
      ) {
        message.success('删除成功');

        // 重新加载配置类列表
        fetchConfigTypeList();
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '产品',
      dataIndex: 'productKey',
      key: 'productKey',
      width: 200,
      render: (text: string, record: ConfigTypeItem) => {
        return record.isEditing ? (
          <Form.Item
            name="productKey"
            rules={[{ required: true, message: '请选择产品' }]}
            style={{ margin: 0 }}
          >
            <Select
              placeholder="请选择"
              options={productOptions}
              style={{ width: '100%' }}
            />
          </Form.Item>
        ) : (
          productOptions.find((option) => option.value === text)?.label || text
        );
      },
    },
    {
      title: '配置类名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: ConfigTypeItem) => {
        return record.isEditing ? (
          <Form.Item
            name="name"
            rules={[{ required: true, message: '请输入配置类名称' }]}
            style={{ margin: 0 }}
          >
            <Input placeholder="请输入" />
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: '标识符',
      dataIndex: 'itemTypeNo',
      key: 'itemTypeNo',
      width: 200,
      render: (text: string, record: ConfigTypeItem) => {
        return record.isEditing ? (
          <Form.Item
            name="itemTypeNo"
            rules={[{ required: true, message: '请输入标识符' }]}
            style={{ margin: 0 }}
          >
            <Input placeholder="请输入" />
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string, record: ConfigTypeItem) => {
        return record.isEditing ? (
          <Form.Item name="description" style={{ margin: 0 }}>
            <Input placeholder="请输入" />
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: '操作',
      key: 'operation',
      width: 150,
      render: (_: any, record: ConfigTypeItem) => {
        return record.isEditing ? (
          <Space>
            <Button type="link" onClick={() => handleSave(record)}>
              保存
            </Button>
            <Button type="link" onClick={() => handleCancel(record)}>
              取消
            </Button>
          </Space>
        ) : (
          <Space>
            <Button type="link" onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Button type="link" danger onClick={() => handleDelete(record)}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <div className="config-type-management">
      <BreadCrumb items={breadCrumbItems} />

      <div className="config-type-content">
        <div className="add-button">
          <Button
            type="link"
            onClick={handleAdd}
            className="add-config-type-btn"
          >
            新增配置类
          </Button>
        </div>

        <Form form={form}>
          <Table
            rowKey={(record) => record.id?.toString() || Date.now().toString()}
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            loading={loading}
            bordered
          />
        </Form>
      </div>
    </div>
  );
};

export default ConfigTypeManagement;
