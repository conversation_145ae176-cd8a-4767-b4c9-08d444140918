# ProductModuleSelector 使用示例

## 重要说明
- `needFormConfig` 是必传参数
- 当 `needFormConfig={true}` 时，`formConfig` 也是必传参数
- 移除了 `step` 属性的向后兼容逻辑，需要明确传递配置

## 1. 基本使用（只显示模块选择，不显示表单）

```tsx
import ProductModuleSelector from '@/components/ProductModuleSelector';

// 只需要模块选择功能，不需要表单
<ProductModuleSelector
  ref={productModuleSelectorRef}
  needFormConfig={false} // 必传：不显示表单
  title="选择模块"
  selectedData={{
    productKey: 'some-product-key', // 直接传入产品key
  }}
  onChange={(data) => {
    console.log('选中的模块:', data.selectedModules);
  }}
/>
```

## 2. 自定义表单配置

```tsx
// 自定义表单配置
const customFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '选择产品',
      type: 'select',
      placeholder: '请选择产品',
      validatorRules: [{ required: true, message: '请选择产品' }],
    },
    {
      fieldName: 'customField',
      label: '自定义字段',
      type: 'input',
      placeholder: '请输入自定义内容',
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          // 自定义获取产品列表的逻辑
          return await getProductList();
        },
      },
    ],
  },
};

<ProductModuleSelector 
  ref={productModuleSelectorRef}
  needFormConfig={true}  // 必传：显示表单
  formConfig={customFormConfig}  // 必传：自定义表单配置
  formType="edit"
  title="自定义产品模块选择"
  onChange={(data) => {
    console.log('表单数据:', data);
  }}
/>
```

## 3. 第一步配置选择

```tsx
// 第一步表单配置
const firstStepFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      validatorRules: [{ required: true, message: '请选择产品' }],
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const deviceApi = new Device();
          const res = await deviceApi.queryProductList();
          return res.data?.map(item => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
        },
      },
    ],
  },
};

<ProductModuleSelector 
  ref={productModuleSelectorRef}
  needFormConfig={true}  // 必传
  formConfig={firstStepFormConfig}  // 必传
  formType="search"
  title="选择产品及模块"
  onChange={(data) => {
    console.log('产品和模块:', data);
  }}
/>
```

## 4. 第二步模板编辑

```tsx
// 第二步表单配置
const secondStepFormConfig = {
  fields: [
    {
      fieldName: 'templateName',
      label: '模板名称',
      type: 'input',
      validatorRules: [{ required: true, message: '请输入模板名称' }],
      maxLength: 50,
      showCount: true,
    },
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      disabled: true,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      multiple: true,
      validatorRules: [{ required: true, message: '请选择型号' }],
    },
    {
      fieldName: 'remark',
      label: '备注',
      type: 'textArea',
      maxLength: 150,
      showCount: true,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const deviceApi = new Device();
          const res = await deviceApi.queryModelList(val);
          return res.data?.map(item => ({
            label: item.modelName,
            value: item.modelNo,
          })) || [];
        },
      },
    ],
  },
};

<ProductModuleSelector 
  ref={productModuleSelectorRef}
  needFormConfig={true}  // 必传
  formConfig={secondStepFormConfig}  // 必传
  formType="edit"
  title="模板基本信息"
  selectedData={{
    productKey: 'product-123',
    templateName: '模板名称',
    productModelNoList: ['model-1', 'model-2'],
    remark: '备注信息',
  }}
/>
```

## 5. 获取数据

```tsx
const handleGetData = async () => {
  try {
    const data = await productModuleSelectorRef.current?.getSelectedData();
    console.log('获取的数据:', data);
    
    // 数据结构：
    // {
    //   productKey: string,
    //   selectedModules: string[],
    //   // 如果有表单配置，还会包含表单字段：
    //   templateName?: string,
    //   productModelNoList?: string[],
    //   remark?: string,
    //   customField?: any, // 自定义字段
    // }
  } catch (error) {
    console.error('验证失败:', error.message);
  }
};
```

## Props 说明

| 属性 | 类型 | 默认值 | 必传 | 说明 |
|------|------|--------|------|------|
| selectedData | any | - | 否 | 初始选中的数据 |
| onChange | function | - | 否 | 数据变化回调 |
| needFormConfig | boolean | - | **是** | 是否需要表单配置 |
| formConfig | any | - | 条件必传* | 表单配置对象 |
| formType | 'search' \| 'edit' | 'search' | 否 | 表单类型 |
| title | string | '产品模块选择' | 否 | 组件标题 |

*当 `needFormConfig={true}` 时，`formConfig` 为必传参数

## 迁移指南

### 从旧版本迁移

```tsx
// 旧版本（不再支持）
<ProductModuleSelector 
  step="first"
  selectedData={data}
  onChange={handleChange}
/>

// 新版本（必须明确传递配置）
<ProductModuleSelector 
  needFormConfig={true}  // 必传
  formConfig={firstStepFormConfig}  // 必传
  formType="search"
  selectedData={data}
  onChange={handleChange}
/>
```

### 错误处理

```tsx
// 错误：缺少必传参数
<ProductModuleSelector 
  // needFormConfig 未传递 - 会报错
  selectedData={data}
/>

// 错误：needFormConfig为true但未传递formConfig
<ProductModuleSelector 
  needFormConfig={true}
  // formConfig 未传递 - 会报错
  selectedData={data}
/>

// 正确：只要模块选择
<ProductModuleSelector 
  needFormConfig={false}  // 明确不需要表单
  selectedData={{ productKey: 'known-product' }}
/>

// 正确：需要表单配置
<ProductModuleSelector 
  needFormConfig={true}
  formConfig={myFormConfig}  // 提供表单配置
  selectedData={data}
/>
```
