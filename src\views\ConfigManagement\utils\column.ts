import {
  DropDownType,
  dropDownKey,
  dropDownList<PERSON>ey,
  ClstagKey,
} from '@/utils/searchFormEnum';
import { FormConfig } from '@/components/CommonForm/formConfig';

export const ConfigTableData: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '模板编号',
    width: 190,
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属产品',
    width: 150,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所在位置',
    width: 90,
    dataIndex: 'position',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置模板名称',
    width: 220,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '模板状态',
    width: 90,
    dataIndex: 'enableName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 130,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 160,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 170,
    fixed: 'right',
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '配置模板名称',
      placeholder: '请输入关键字',
      type: 'input',
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请输入关键字',
      type: 'select',
      dropDownKey: dropDownKey.productTypeList,
      dropDownListKey: dropDownListKey.productTypeList,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'position',
      label: '所在位置',
      placeholder: '请选择',
      type: 'select',
      dropDownKey: dropDownKey.positionList,
      dropDownListKey: dropDownListKey.positionList,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'enable',
      label: '模板状态',
      placeholder: '请选择',
      type: 'select',
      dropDownKey: dropDownKey.enableEnumList,
      dropDownListKey: dropDownListKey.enableEnumList,
      specialFetch: 'commonDown',
    },
  ],
};
