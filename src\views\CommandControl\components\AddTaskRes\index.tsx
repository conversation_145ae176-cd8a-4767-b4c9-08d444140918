import React from 'react';
import './index.scss';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const AddTaskRes = ({
  success,
  message,
  taskNo,
  productKey,
}: {
  success: boolean;
  message: string;
  taskNo: string;
  productKey: string;
}) => {
  const navigator = useNavigate();

  const goBack = () => {
    navigator('/commandControl');
  };

  const handleCheck = () => {
    navigator(
      '/commandControl/CheckTask?taskNo=' +
        taskNo +
        '&productKey=' +
        productKey,
    );
  };
  return (
    <div className="add-task-res">
      <div className={`tag ${success}`}>
        {success ? <CheckOutlined /> : <CloseOutlined />}
      </div>
      <div className="message">{message}</div>
      <div className="btns">
        {success && <a onClick={handleCheck}>查看任务详情</a>}
        <a onClick={goBack}>返回</a>
      </div>
    </div>
  );
};

export default React.memo(AddTaskRes);
