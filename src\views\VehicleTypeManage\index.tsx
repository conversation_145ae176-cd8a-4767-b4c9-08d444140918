import React, { useState, useEffect, useRef } from 'react';
import { Form, Table } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Searchform from '@/components/Searchform';
import {
  saveSearchValues,
  removeSearchValues,
  searchformSelector,
} from '@/redux/reducers/searchform';
import {
  pageSizeOptions,
  SearchCondition,
  TableListType,
} from '@/utils/constant';
import { SearchConfig, VehicleTypeTableData } from './utils/column';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/formatLocation';
import './index.scss';
import VehicleTypeManageRequest from '@/fetch/bussiness/vehicleTypeManage';
import CommonForm from '@/components/CommonForm';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
const fetchApi = new VehicleTypeManageRequest();
const VehicleTypeManage = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const historySearchValue = useSelector(searchformSelector).searchValues;
  const formRef = useRef<any>(null);
  const [searchFormConfig, setSearchFormConfig] =
    useState<FormConfig>(SearchConfig);
  const [loading, setLoading] = useState<boolean>(false);
  const urlData: any = formatLocation(window.location.search);

  const { vehicleTypeName, vehicleTypeId } = urlData;
  const initSearchCondition = {
    searchForm: {
      vehicleType:
        vehicleTypeName && vehicleTypeId
          ? { key: vehicleTypeId, value: vehicleTypeId, label: vehicleTypeName }
          : null,
      isConfig: null,
      productType: null,
    },
    current: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValue ? historySearchValue : initSearchCondition;
    },
  );
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });

  useEffect(() => {
    fetchTableData(searchCondition);
    fetchVehicleTypeNameList();
  }, []);
  const fetchTableData = async (searchValues: any) => {
    setLoading(true);
    try {
      const res = await fetchApi.getVehicleTypeTableList(
        {
          vehicleTypeId: searchValues.searchForm.vehicleType?.value,
          isConfig: searchValues.searchForm.isConfig?.value,
          productType: searchValues.searchForm.productType?.value,
        },
        {
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize,
        },
      );
      if (res && res.code === HttpStatusCode.Success) {
        setTableList({
          list: res.data.list,
          totalPage: res.data.pages,
          totalNumber: res.data.total,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  const fetchVehicleTypeNameList = () => {
    try {
      fetchApi.getVehicleTypeNameList().then((res: any) => {
        const vehicleTypeField = searchFormConfig?.fields?.find(
          (field: FieldItem) => field.fieldName === 'vehicleType',
        );
        if (vehicleTypeField) {
          vehicleTypeField.options = res?.data?.map((item: any) => {
            return { label: item.name, value: item.code };
          });
        }
        setSearchFormConfig({ ...searchFormConfig });
      });
    } catch (err) {}
  };

  const formatColumns = () => {
    return VehicleTypeTableData?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'configurationFileCount':
          col.render = (text: any, record: any, index: number) => `${text}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    dispatch(
                      saveSearchValues({
                        routeName: location.pathname,
                        searchValues: searchCondition,
                      }),
                    );
                    navigator(
                      `/vehicleTypeManage/vehicleTypeConfig?vehicleTypeName=${record.vehicleTypeName}&vehicleTypeId=${record.vehicleTypeId}&productType=${record.productType}`,
                    );
                  }}
                >
                  车型配置
                </a>
                <a
                  onClick={() => {
                    dispatch(
                      saveSearchValues({
                        routeName: location.pathname,
                        searchValues: searchCondition,
                      }),
                    );
                    navigator(
                      '/vehicleTypeManage/vehicleTypeHandleHistory?vehicleTypeName=' +
                        record.vehicleTypeName +
                        '&vehicleTypeId=' +
                        record.vehicleTypeId,
                    );
                  }}
                >
                  操作记录
                </a>
                <a
                  onClick={() => {
                    sessionStorage.setItem(
                      'vehicleTypeName',
                      record.vehicleTypeName,
                    );
                    sessionStorage.setItem(
                      'vehicleTypeId',
                      record.vehicleTypeId,
                    );
                    navigator(
                      '/vehicleConfigAndRelease?vehicleTypeName=' +
                        record.vehicleTypeName +
                        '&vehicleTypeId=' +
                        record.vehicleTypeId,
                    );
                  }}
                >
                  查看车辆配置
                </a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onSearchClick = () => {
    const data = {
      ...searchCondition,
      searchForm: formRef.current.getFieldsValue(),
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
    fetchTableData(data);
  };
  const onResetClick = () => {
    formRef.current?.setFieldsValue(initSearchCondition.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    setSearchCondition(initSearchCondition);
    fetchTableData(initSearchCondition);
    if (vehicleTypeId && vehicleTypeName) {
      navigator('/vehicleTypeManage');
    }
  };
  return (
    <div className="vehicle-type-manage">
      <div className="searchform">
        <CommonForm
          formConfig={searchFormConfig}
          colon={false}
          layout={'inline'}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          getFormInstance={(formInstance: any) => {
            formRef.current = formInstance;
          }}
          defaultValue={searchCondition?.searchForm}
          formType="search"
        />
      </div>
      <div className="table-container">
        <Table
          rowKey={(record) => record.vehicleTypeId}
          columns={formatColumns()}
          dataSource={tableList.list}
          loading={loading}
          bordered
          scroll={{
            y: 600,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: searchCondition.current,
            pageSize: searchCondition.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any,
          ) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                current,
                pageSize,
              };
              dispatch(
                saveSearchValues({
                  routeName: location.pathname,
                  searchValues: newSearchValue,
                }),
              );
              setSearchCondition(newSearchValue);
              fetchTableData(newSearchValue);
            }
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(VehicleTypeManage);
