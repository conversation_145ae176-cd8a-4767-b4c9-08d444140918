import React, { useRef, useState } from 'react';
import CommonSteps from '@/components/CommonSteps';
import BreadCrumb from '@/components/BreadCrumb';
import SelectInstruction from '../components/SelectInstruction';
import SelectDevice from '../components/SelectDevice';
import SelectStrategy from '../components/SelectStrategy';
import AddTaskRes from '../components/AddTaskRes';
import './index.scss';
import { CommandControlFetch } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { DeviceChoiceType } from '../utils/constant';
import showModal from '@/components/commonModal';

const AddTask = () => {
  const fetchApi = new CommandControlFetch();
  const ref1 = useRef<any>(null);
  const ref2 = useRef<any>(null);
  const ref3 = useRef<any>(null);
  const taskInfo = useRef<any>(null);
  const deviceNum = useRef<any>(null);
  const [productKey, setProductKey] = useState<any>(null);
  const [createTaskRes, setCreateTaskRes] = useState<{
    success: boolean;
    taskNo: string;
    message: string;
  }>({
    success: true,
    taskNo: '',
    message: '成功创建指令下发任务',
  });

  const handleSubmit = async () => {
    const data = await ref3.current?.checkData();
    const flag = await new Promise((resolve) => {
      showModal({
        title: `确认推送${deviceNum.current}台设备的指令下发？`,
        width: '600px',
        content: '',
        footer: [
          {
            text: '确定',
            type: 'notCancelBtn',
            onClick: async (cb: any) => {
              const res = await fetchApi.createTask({
                ...data,
                ...taskInfo.current,
              });
              setCreateTaskRes({
                success: res.code === HttpStatusCode.Success,
                taskNo: res?.data,
                message:
                  res.code === HttpStatusCode.Success
                    ? '成功创建指令下发任务'
                    : `任务下发失败(${res.message})`,
              });
              if (res.code) {
                cb();
                resolve(true);
              }
            },
          },
          {
            text: '取消',
            type: 'cancelBtn',
            onClick: (cb: any) => {
              cb();
              resolve(false);
            },
          },
        ],
      });
    });
    return flag;
  };

  const checkStepOne = async () => {
    const data = await ref1.current?.checkData();
    setProductKey(data?.productKey);
    taskInfo.current = {
      ...taskInfo.current,
      ...data,
    };
    return data ? true : false;
  };

  const checkStepTwo = async () => {
    const data = ref2.current?.checkData();
    deviceNum.current = data.num;
    taskInfo.current = {
      ...taskInfo.current,
      deviceChoiceInfo: data.deviceChoiceInfo,
    };
    return data ? true : false;
  };

  return (
    <>
      <BreadCrumb
        items={[
          { title: '指令控制', route: '' },
          { title: '新建任务', route: '' },
        ]}
      />
      <div className="command-task-edit-content">
        <CommonSteps
          stepTipList={[
            '选择指令',
            '选择指令下发范围',
            '指令下发规则',
            '指令下发',
          ]}
          children={[
            <SelectInstruction ref={ref1} />,
            <SelectDevice ref={ref2} productKey={productKey} />,
            <SelectStrategy ref={ref3} />,
            <AddTaskRes {...createTaskRes} productKey={productKey} />,
          ]}
          onNextCheckList={[checkStepOne, checkStepTwo]}
          onSubmit={handleSubmit}
        />
      </div>
    </>
  );
};

export default React.memo(AddTask);
