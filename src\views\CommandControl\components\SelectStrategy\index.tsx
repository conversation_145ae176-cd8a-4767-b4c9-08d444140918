import CommonForm from '@/components/CommonForm';
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { SelectStrategyConfig } from '../../utils/column';
import dayjs from 'dayjs';
import { sendGlobalEvent } from '@/utils/emit';

const SelectStrategy = forwardRef((props, ref) => {
  const formRef = useRef<any>(null);

  useImperativeHandle(
    ref,
    () => {
      return {
        checkData,
      };
    },
    [],
  );

  const checkData = async () => {
    const data = await formRef.current.validateFields();
    if (data.unit) {
      if (data.unit.value === 'min') {
        data.retainTime = data.retainTime * 60;
      } else if (data.unit.value === 'hour') {
        data.retainTime = data.retainTime * 60 * 60;
      } else if (data.unit.value === 'day') {
        data.retainTime = data.retainTime * 60 * 60 * 24;
      }
    }

    const val = {
      ...data,
      executionTime:
        data.date && data.time
          ? `${data.date.format('YYYY-MM-DD')} ${data.time.format('HH:mm:ss')}`
          : null,
    };
    delete val.time;
    delete val.date;
    return val;
  };

  const formatDisableTime = (delay: boolean) => {
    const oneHourLater = dayjs().add(1, 'hours');
    const val = SelectStrategyConfig.fields.filter(
      (v) => v.fieldName === 'time',
    );
    sendGlobalEvent('FORCE_UPDATE_ONE_CONFIG', {
      name: 'select-strategy-form',
      fieldName: 'time',
      config: {
        ...val[0],
        disabledTime: () => {
          return {
            disabledHours: () => {
              let hours = [];
              if (delay) {
                for (let i = 0; i < 24; i++) {
                  if (i < oneHourLater.hour()) {
                    hours.push(i);
                  }
                }
              } else {
                hours = [];
              }
              return hours;
            },
            disabledMinutes: (selectedHour: any) => {
              let minutes = [];
              if (delay) {
                if (selectedHour === oneHourLater.hour()) {
                  for (let i = 0; i < oneHourLater.minute(); i++) {
                    minutes.push(i);
                  }
                }
              } else {
                minutes = [];
              }
              return minutes;
            },
            disabledSeconds: () => [],
          };
        },
      },
    });
  };
  return (
    <>
      <CommonForm
        name="select-strategy-form"
        formConfig={SelectStrategyConfig}
        layout="horizontal"
        defaultValue={{
          immediately: 1,
          batch: 0,
          retain: 0,
          unit: { value: 'min', label: '分钟' },
        }}
        getFormInstance={(val: any) => (formRef.current = val)}
        onValueChange={(allValues: any, changedFieldName: any) => {
          if (changedFieldName === 'date') {
            const now = new Date();
            if (allValues.date > now) {
              formatDisableTime(false);
            } else {
              formatDisableTime(true);
            }
          }
        }}
      />
    </>
  );
});

export default React.memo(SelectStrategy);
