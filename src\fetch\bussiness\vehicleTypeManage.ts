import { request } from '@/fetch/core';
class VehicleTypeManageRequest {
  /**
   * 获取车型配置分页列表
   * @param {number} param.vehicleTypeId 车型Id
   * @param {string} param.isConfig 车型配置状态
   * @param {string} param.productType 所属产品
   * @param {number} pageInfo.pageNum 当前页码
   * @param {number} pageInfo.pageSize 每页数量
   * @return {Promise}
   */
  public getVehicleTypeTableList = async (
    params: {
      vehicleTypeId?: number;
      isConfig?: number;
      productType?: string;
    },
    pageInfo: {
      pageNum: number;
      pageSize: number;
    },
  ): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/vehicle_type_conf_info_page_list`,
      urlParams: {
        pageNum: pageInfo.pageNum,
        pageSize: pageInfo.pageSize,
      },
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 获取车型名称下拉list
   */
  public getVehicleTypeNameList() {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: `/ota/web/get_vehicle_type_name_list`,
    };
    return request(requestOptions);
  }

  /**
   * 模板详情
   * @param {string} param.number 模板编号
   * @return {Promise}
   */
  public getConfTemplateDetail = (param: { number: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: `/ota/web/conf_template_detail`,
      urlParams: {
        number: param.number,
      },
    };
    return request(requestOptions);
  };

  /**
   * 查看车型配置文件内容详情
   * @param {string} param.id
   * @return {Promise}
   */
  public getVehicleTypeConfDetail = (param: { id: string }) => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: `/ota/web/vehicle_type_conf_info_detail`,
      urlParams: {
        id: param.id,
      },
    };
    return request(requestOptions);
  };

  /**
   * 添加车型配置模板
   * @param {number} param.vehicleTypeId 车型Id
   * @param {string[]} param.numberList 配置模板编号列表
   * @return {Promise}
   */
  public addVehicleTypeConf = (param: {
    vehicleTypeId: number;
    numberList: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/add_vehicle_type_conf_info`,
      body: {
        vehicleTypeId: param.vehicleTypeId,
        numberList: param.numberList,
      },
    };
    return request(requestOptions);
  };

  public getConfList = (param: { name: string; productType: string }) => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/conf_template_list`,
      body: {
        ...param,
        enable: 1,
      },
    };
    return request(requestOptions);
  };

  /**
   * 查看当前车型已添加配置
   * @param {number} param.vehicleTypeId 车型Id
   * @param {number} param.name 模板名称
   * @return {Promise}
   */
  public getSelectedVehicleTypeConf = (param: {
    vehicleTypeId: number;
    name: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/vehicle_type_conf_info_added`,
      body: {
        vehicleTypeId: param.vehicleTypeId,
        name: param.name,
      },
    };
    return request(requestOptions);
  };

  public deleteVehicleTypeConf = (param: { id: string }) => {
    const requestOptions: RequestOptions = {
      method: 'PUT',
      path: `/ota/web/delete_vehicle_type_conf_info`,
      urlParams: {
        id: param.id,
      },
    };
    return request(requestOptions);
  };

  public existSameVehicleTypeInfo = (param: { id: string }) => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: `/ota/web/exist_same_name_vehicle_type_conf_info`,
      urlParams: {
        id: param.id,
      },
    };
    return request(requestOptions);
  };

  public editVehicleTypeConf = (param: {
    id: string;
    name: string;
    description: string;
    content: string;
    bothModify: string;
  }) => {
    const requestOptions: RequestOptions = {
      method: 'PUT',
      path: `/ota/web/edit_vehicle_type_conf_info`,
      body: {
        id: param.id,
        name: param.name,
        description: param.description,
        content: param.content,
        bothModify: param.bothModify,
      },
    };
    return request(requestOptions);
  };

  /**
   * 查询车型操作记录列表
   * @param {number} param.vehicleTypeId 车型id
   * @param {string} param.isExist 目前是否存在
   * @return {Promise}
   */
  public getVehicleTypeRecord = (param: {
    vehicleTypeId: number;
    isExist: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/ota/web/vehicle_type_record_list`,
      body: {
        vehicleTypeId: param.vehicleTypeId,
        isExist: param.isExist,
      },
    };
    return request(requestOptions);
  };
}

export default VehicleTypeManageRequest;
