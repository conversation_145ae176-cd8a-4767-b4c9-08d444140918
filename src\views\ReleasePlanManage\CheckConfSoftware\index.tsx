import React, { useState, useEffect, useRef } from 'react';
import { Form, Radio, Input, Table, message, Modal } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import BreadCrumb from '@/components/BreadCrumb';
import FormTitle from '@/components/FormTitle';
import {
  CheckModuleUpgradeRes,
  CheckTableColumns,
  UpgradeResSearchConfig,
  UpgradeResTableColumns,
} from '../utils/columns';
import { api } from '@/fetch/core/api';
import './index.scss';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/formatLocation';
import SelectedVehcile from '@/views/VehicleConfigAndRelease/components/SelectedVehcile';
import {
  SearchCondition,
  TableListType,
  pageSizeOptions,
} from '@/utils/constant';
import Searchform from '@/components/Searchform';
import { UpgradeStatus } from '../utils/constant';
import showModal from '@/components/commonModal';
import { ReleasePlanManageFetch } from '../utils/fetch';
import dayjs from 'dayjs';
const fetchApi = new ReleasePlanManageFetch();
const CheckConfSoftware = () => {
  const breadCrumbItems = [
    { title: 'OTA管理', route: '' },
    { title: '发布计划管理', route: '' },
    { title: '配置及软件发布', route: '' },
  ];
  const navigator = useNavigate();
  const [form] = Form.useForm();
  const [formRef] = Form.useForm();
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    searchForm: {
      city: null,
      station: null,
      vehicleName: null,
      ownerUseCase: [],
      latestIssueTaskResult: null,
    },
    current: 1,
    pageSize: 10,
  });
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const urlData = formatLocation(window.location.search);
  const selectedVehicleRef = useRef<Map<string, any>>(new Map());
  const [data, setData] = useState<any>({
    selectVehicleCount: null,
    number: null,
    isImmediately: null,
    issueTime: null,
    type: null,
    issueAppInfoList: [],
    isHaveLowestAppVersion: null,
    lowestAppInfoList: [],
  });

  useEffect(() => {
    fetchDetail();
  }, [urlData.issueTaskNumber]);

  const fetchDetail = () => {
    request({
      method: 'GET',
      path: api.getIssueTaskDetail,
      urlParams: {
        issueTaskNumber: urlData.issueTaskNumber,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          form.setFieldsValue({ ...res.data });
          setData(res.data);
          const vehicleInfoMap = new Map<string, any>();
          res.data?.vehicleInfoList &&
            res.data?.vehicleInfoList.forEach((item: any) => {
              vehicleInfoMap.set(item.vehicleName, item);
            });
          selectedVehicleRef.current = vehicleInfoMap;
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const formatColumns = () => {
    return CheckTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${index + 1}`;
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  useEffect(() => {
    fetchTableData(searchCondition);
  }, []);

  /**
   * 获取列表数据
   * @param {Object} searchValues 搜索条件
   */
  const fetchTableData = (searchValues: any) => {
    setLoading(true);
    try {
      request({
        method: 'POST',
        path: api.getIssueTaskResultList,
        body: {
          serialNo: searchValues.searchForm?.serialNo,
          issueTaskNumber: urlData.issueTaskNumber,
          cityId: searchValues.searchForm.city?.value,
          stationId: searchValues.searchForm.station?.value,
          vehicleName: searchValues.searchForm.vehicleName,
          ownerUseCaseList: searchValues.searchForm.ownerUseCase?.map(
            (item: any) => {
              return item.value;
            },
          ),
          status: searchValues.searchForm.latestIssueTaskResult?.value,
        },
        urlParams: {
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          });
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const formatUpgradeColumns = () => {
    return UpgradeResTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'statusName':
          col.render = (text: any, record: any, index: number) => {
            return record.status === UpgradeStatus.NOTUPGRADE ? (
              <div style={{ color: 'red' }}>{record.statusName}</div>
            ) : (
              <div>{record.statusName}</div>
            );
          };
          break;
        case 'moduleUpgradeRes':
          col.render = (text: any, record: any, index: number) => {
            return (
              <>
                <a
                  onClick={() => {
                    checkModuleUpgradeRes(record);
                  }}
                >
                  查看模块升级结果
                </a>
                <a
                  style={{
                    marginLeft: '10px',
                    color:
                      dayjs().valueOf() - dayjs(record.issueTime).valueOf() < 0
                        ? '#1677ff'
                        : 'gray',
                  }}
                  onClick={() => {
                    if (
                      dayjs().valueOf() - dayjs(record.issueTime).valueOf() >=
                      0
                    ) {
                      return;
                    }
                    Modal.confirm({
                      content: '确定要取消升级吗',
                      onOk: cancelVehicleIssueTask.bind(null, record),
                    });
                  }}
                >
                  取消
                </a>
              </>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const formatCheckModuleUpgradeResCol = () => {
    return CheckModuleUpgradeRes?.map((col: any) => {
      switch (col.dataIndex) {
        case 'statuaName':
          col.render = (text: any, record: any, index: number) => {
            return record.status === UpgradeStatus.NOTUPGRADE ? (
              <div style={{ color: 'red' }}>{record.statusName}</div>
            ) : (
              <div>{record.statusName}</div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const checkModuleUpgradeRes = async (record: any) => {
    const res: any = await fetchApi.fetchModuleUpgradeRes({
      vehicleName: record.vehicleName,
      issueTaskNumber: urlData.issueTaskNumber,
    });
    if (res.code === HttpStatusCode.Success && res.data) {
      showModal({
        title: '查看模块升级结果',
        type: 'confirm',
        width: '650',
        content: (
          <Table
            columns={formatCheckModuleUpgradeResCol()}
            dataSource={res.data}
            bordered
            pagination={false}
          />
        ),
        footer: [
          {
            type: 'cancelBtn',
            text: '关闭',
          },
        ],
      });
    } else {
      message.error(res.message);
    }
  };

  const onSearchClick = () => {
    const data = {
      searchForm: formRef.getFieldsValue(),
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    fetchTableData(data);
  };

  const cancelVehicleIssueTask = (record: any) => {
    request({
      path: '/ota/web/cancel_vehicle_issue_task',
      method: 'POST',
      body: {
        vehicleName: record.vehicleName,
        issueTaskNumber: urlData.issueTaskNumber,
      },
    })
      .then((res: any) => {
        if (res.code === HttpStatusCode.Success) {
          message.success('取消升级成功');
        } else {
          message.warning('取消升级失败');
        }
      })
      .catch((e) => {});
  };
  return (
    <div className="check-conf-software">
      <BreadCrumb items={breadCrumbItems} />
      <div className="content">
        <FormTitle title={'配置及软件发布'} />
        <Form labelCol={{ span: 3 }} wrapperCol={{ span: 19 }} form={form}>
          <Form.Item name={'number'} label={'发布计划编号'}>
            <Input disabled style={{ color: 'black' }} bordered={false} />
          </Form.Item>
          <Form.Item name={'type'} label={'发布内容'} required>
            <Radio.Group disabled>
              <Radio value={'app'}>应用或配置文件</Radio>
              <Radio value={'productPackage'}>产品包</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name={'isImmediately'}
            label={'发布时间设置'}
            rules={[{ required: true, message: '请设置发布时间' }]}
          >
            <Radio.Group disabled>
              <Radio value={0}>定时发布</Radio>
              <Radio value={1}>立即发布</Radio>
            </Radio.Group>
          </Form.Item>
          {data.isImmediately === 0 && (
            <Form.Item
              name={'issueTime'}
              label={'发布时间'}
              extra={'说明：请注意避开“运营时间段”发布功能。'}
              rules={[{ required: true, message: '请输入发布时间' }]}
            >
              <Input disabled />
            </Form.Item>
          )}
          <Form.Item
            name={'issueAppInfoList'}
            label={'发布模块'}
            rules={[{ required: true, message: '请选择发布模块' }]}
          >
            <Table
              columns={formatColumns()}
              dataSource={data.issueAppInfoList}
              bordered
              pagination={false}
            />
          </Form.Item>
          <Form.Item
            name={'isHaveLowestAppVersion'}
            label={'依赖于车端最低版本'}
            rules={[{ required: true, message: '请选择依赖于车端最低版本' }]}
          >
            <Radio.Group disabled>
              <Radio value={0}>没有</Radio>
              <Radio value={1}>有</Radio>
            </Radio.Group>
          </Form.Item>
          {data.isHaveLowestAppVersion === 1 && (
            <Form.Item
              name={'lowestAppInfoList'}
              label={'        '}
              colon={false}
            >
              <Table
                columns={formatColumns()}
                dataSource={data.lowestAppInfoList}
                bordered
                pagination={false}
              />
            </Form.Item>
          )}
          <Form.Item
            name={'issueDescription'}
            label={'发版描述'}
            rules={[{ required: true, message: '请输入发版描述' }]}
          >
            <Input.TextArea rows={4} placeholder={'请输入发版描述'} disabled />
          </Form.Item>
        </Form>
        <FormTitle title={'发布车辆升级详情'} />
        <Searchform
          configData={UpgradeResSearchConfig}
          onSearchClick={onSearchClick}
          initValues={searchCondition.searchForm}
          formRef={formRef}
          noResetBtn={true}
        />
        <Table
          rowKey={(record) => record.vehicleName}
          loading={loading}
          bordered
          dataSource={tableList.list}
          columns={formatUpgradeColumns()}
          scroll={{
            y: 500,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: searchCondition.current,
            pageSize: searchCondition.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any,
          ) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                current,
                pageSize,
              };
              setSearchCondition(newSearchValue);
              fetchTableData(newSearchValue);
            }
          }}
        />
        <div className="submit-btns">
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            otherCSSProperties={{ marginLeft: '20px' }}
            onSubmitClick={() => navigator('/releasePlanManage')}
            title={'关闭'}
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(CheckConfSoftware);
