.tag-label {
  padding: 4px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  .placeholder {
    color: rgba(0, 0, 0, 0.2);
  }

  .tag-list-wrapper {
    display: flex;
  }

  .tag-item {
    display: flex;
    align-items: center;
    margin-right: 4px;
    .tag-text {
      display: inline-block;
    }
    .delete-btn {
      display: inline-block;
      width: 16px;
      height: 16px;
      background: url(@/assets/images/delete.png) no-repeat center;
      background-size: 100%;
      margin-left: 4px;
      cursor: pointer;
    }
  }
}

.modal-footer-btn {
  button {
    margin-left: 8px;
  }
}
.ant-table-cell {
  input {
    border: 1px solid #d9d9d9;
    padding: 8px;
    border-radius: 6px;
    font-size: 14px;
  }
}
