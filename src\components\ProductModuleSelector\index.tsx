import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { CommonForm } from '@jd/x-coreui';
import { message } from 'antd';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

interface ModuleOption {
  productModelNo: string;
  productModelName: string;
}

interface ProductModuleSelectorProps {
  selectedData?: any;
  onChange?: (data: any) => void;
  needFormConfig: boolean; // 是否需要表单配置，必传
  formConfig?: any; // 表单配置，当needFormConfig为true时必传
  formType?: 'search' | 'edit'; // 表单类型，默认search
  title?: string; // 自定义标题，默认"产品模块选择"
}

export interface ProductModuleSelectorRef {
  getSelectedData: () => Promise<{
    productKey: string;
    selectedModules: string[];
    templateName?: string;
    productModelNoList?: string[];
    remark?: string;
  }>;
}

const ProductModuleSelector = forwardRef<
  ProductModuleSelectorRef,
  ProductModuleSelectorProps
>(
  (
    {
      selectedData,
      onChange,
      needFormConfig,
      formConfig,
      formType = 'search',
      title = '产品模块选择',
    },
    ref,
  ) => {
    const deviceApi = new Device();
    const formRef = React.useRef<any>(null);

    const [moduleOptions, setModuleOptions] = useState<ModuleOption[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<string>();
    const [selectedModules, setSelectedModules] = useState<string[]>([]);

    // 验证必传参数
    if (needFormConfig && !formConfig) {
      throw new Error('当needFormConfig为true时，formConfig是必传参数');
    }

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getSelectedData: async () => {
        if (!selectedProduct) {
          throw new Error('请选择产品');
        }
        // 如果需要表单配置，验证模块选择（可选验证，由父组件决定）
        // if (needFormConfig && selectedModules.length === 0) {
        //   throw new Error('请选择至少一个模块');
        // }

        const baseData = {
          productKey: selectedProduct,
          selectedModules,
        };

        // 如果需要表单配置且有表单引用，获取表单数据
        if (needFormConfig && formRef.current) {
          try {
            const formValues = await formRef.current?.getFieldsValue();
            return {
              ...baseData,
              ...formValues, // 返回所有表单字段
            };
          } catch (error) {
            // 如果表单验证失败，仍然返回基础数据
            return baseData;
          }
        }

        return baseData;
      },
    }));

    // 当选中数据变化时，同步表单
    useEffect(() => {
      if (selectedData?.productKey) {
        setSelectedProduct(selectedData.productKey);
        formRef.current?.setFieldsValue({
          productKey: selectedData.productKey,
        });
        fetchModuleList(selectedData.productKey);
      }
      if (selectedData?.productModelNoList) {
        setSelectedModules(selectedData.productModelNoList);
      }
    }, [selectedData]);

    const fetchModuleList = async (productKey: string) => {
      try {
        const res = await deviceApi.queryModelList(productKey);
        if (res.code === HttpStatusCode.Success) {
          setModuleOptions(res.data || []);
        } else {
          message.error(res.message || '获取模块列表失败');
          setModuleOptions([]);
        }
      } catch (error) {
        message.error('获取模块列表失败');
        setModuleOptions([]);
      }
    };

    const handleFormChange = (values: any) => {
      if (values.productKey !== selectedProduct) {
        setSelectedProduct(values.productKey);
        setSelectedModules([]);
        setModuleOptions([]);
        if (values.productKey) {
          fetchModuleList(values.productKey);
        }

        // 通知父组件数据变化
        if (onChange) {
          onChange({
            productKey: values.productKey,
            selectedModules: [],
            ...values, // 传递所有表单值
          });
        }
      }
    };

    const handleModuleClick = (moduleNo: string) => {
      const newSelectedModules = selectedModules.includes(moduleNo)
        ? selectedModules.filter((item) => item !== moduleNo)
        : [...selectedModules, moduleNo];

      setSelectedModules(newSelectedModules);

      // 通知父组件数据变化
      if (onChange) {
        const baseData = {
          productKey: selectedProduct,
          selectedModules: newSelectedModules,
        };

        // 如果有表单，获取表单数据
        if (needFormConfig && formRef.current) {
          try {
            const formValues = formRef.current?.getFieldsValue();
            onChange({
              ...baseData,
              ...formValues,
            });
          } catch (error) {
            onChange(baseData);
          }
        } else {
          onChange(baseData);
        }
      }
    };

    const renderModuleTabs = () => {
      if (moduleOptions.length === 0) {
        return <div className="no-modules">请先选择产品</div>;
      }

      return (
        <div className="module-tabs">
          <div className="module-tabs-header">
            <span className="label">模块选择：</span>
            <span className="tip">（点击选择模块，支持多选）</span>
          </div>
          <div className="module-tabs-content">
            {moduleOptions.map((module) => (
              <div
                key={module.productModelNo}
                className={`module-tab ${
                  selectedModules.includes(module.productModelNo)
                    ? 'selected'
                    : ''
                }`}
                onClick={() => handleModuleClick(module.productModelNo)}
              >
                {module.productModelName}
              </div>
            ))}
          </div>
          {selectedModules.length > 0 && (
            <div className="selected-info">
              已选择 {selectedModules.length} 个模块
            </div>
          )}
        </div>
      );
    };

    return (
      <div className="product-module-selector">
        <div className="section-title">{title}</div>
        <div className="section-content">
          {needFormConfig && (
            <CommonForm
              formConfig={formConfig}
              onRef={(ref: any) => (formRef.current = ref)}
              formType={formType}
              layout="vertical"
              defaultValue={{
                fetchProductKey: true,
                productKey: selectedData?.productKey,
                templateName: selectedData?.templateName,
                productModelNoList: selectedData?.productModelNoList,
                remark: selectedData?.remark,
                ...selectedData, // 支持更多字段
              }}
              onValuesChange={handleFormChange}
            />
          )}

          {renderModuleTabs()}
        </div>
      </div>
    );
  },
);

ProductModuleSelector.displayName = 'ProductModuleSelector';

export default ProductModuleSelector;
