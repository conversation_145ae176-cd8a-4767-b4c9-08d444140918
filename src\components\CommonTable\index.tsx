import React, { useEffect, useState } from 'react';
import { But<PERSON>, Popconfirm, Table } from 'antd';
import './index.scss';
import { pageSizeOptions } from '@/utils/constant';
import { AnyObject } from '@/global';
interface MiddleBtns {
  title: string;
  sourceCode?: string;
  btnType?: 'primary' | 'default' | 'textBtn';
  onClick: Function;
  enablePopConfirm?: boolean;
  popConfirmContent?: string;
}
interface Props<T> {
  tableListData: { list: object[]; totalNumber?: number; totalPage?: number };
  columns: any[];
  loading: boolean;
  rowKey: string; // table里面每一行的唯一标识key
  middleBtns?: MiddleBtns[]; // 中间按键
  searchRef?: any;
  searchCondition?: T;
  onPageChange?: Function;
  expandable?: any; // 配置展开属性
  rowSelection?: object | null;
  notPage?: boolean;
  scrollY?: number;
  tableKey?: string;
  selectedNum?: number | null;
}

export const customLocale = {
  filterConfirm: '确定',
  filterReset: '重置',
  emptyText: '暂无数据',
  pagination: {
    items_per_page: '/页',
    jump_to: '跳至',
    page: '',
    all_item_text: '全部',
    next_page_text: '下一页',
    prev_page_text: '上一页',
    prev_5_text: '前 5 页',
    next_5_text: '后 5 页',
    first_page_text: '第一页',
    last_page_text: '最后一页',
  },
};
function CommonTable<T extends AnyObject = AnyObject>(props: Props<T>) {
  const {
    middleBtns,
    loading,
    columns,
    searchRef,
    tableListData,
    searchCondition,
    onPageChange,
    rowKey,
    expandable,
    rowSelection,
    notPage,
    scrollY,
    tableKey,
    selectedNum,
  } = props;

  // /* 计算搜索框高度，以使Table高度自适应屏幕高度 */
  const tableSize = () => {
    if (searchRef && searchRef.current && searchRef.current.clientHeight) {
      const height = `calc(80vh - ${searchRef.current.clientHeight + 100}px)`;
      return height;
    }
    return '65vh';
  };

  return (
    <div className="common-table">
      <div className="middle-btn">
        {middleBtns &&
          middleBtns.map((item: any) => {
            return (
              <>
                {item?.enablePopConfirm ? (
                  <Popconfirm
                    title={item.popConfirmContent}
                    onConfirm={item.onClick}
                    key={item.key}
                  >
                    <Button type="primary">{item.title}</Button>
                  </Popconfirm>
                ) : (
                  <Button
                    type={item.btnType ?? 'primary'}
                    key={item.key}
                    onClick={() => item.onClick()}
                  >
                    {item.title}
                  </Button>
                )}
              </>
            );
          })}
      </div>
      <Table
        key={tableKey}
        columns={columns}
        loading={loading}
        locale={customLocale}
        dataSource={tableListData?.list ?? []}
        rowKey={(record: any) => (record[rowKey] ? record[rowKey] : rowKey)}
        expandable={expandable}
        scroll={{
          y: scrollY ?? tableSize(),
        }}
        rowSelection={rowSelection ?? rowSelection}
        pagination={
          notPage
            ? false
            : {
                position: ['bottomCenter'],
                total: tableListData?.totalNumber,
                current: searchCondition?.pageNum,
                pageSize: searchCondition?.pageSize,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: pageSizeOptions,
                showTotal: (total) =>
                  selectedNum
                    ? `已选${selectedNum},共${total}条记录`
                    : `共${total}条记录`,
                locale: customLocale.pagination,
              }
        }
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            const newSearchValue = {
              ...searchCondition,
              pageNum: current,
              pageSize: pageSize,
            };
            onPageChange && onPageChange(newSearchValue);
          }
        }}
      />
    </div>
  );
}

export default React.memo(CommonTable);
