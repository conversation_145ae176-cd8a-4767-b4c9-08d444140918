import React from 'react';
import { Form, Input, Flex } from 'antd';

const BoolModule = React.memo(({ disabled }: { disabled: boolean }) => {
  return (
    <>
      <Form.Item label="布尔值" required>
        <Flex gap="0" justify="space-between">
          <Form.Item
            label="0-"
            name="name-0"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            rules={[
              { required: true, message: '布尔值不能为空' },
              {
                pattern:
                  /^[\u4e00-\u9fa5a-zA-Z0-9][\u4e00-\u9fa5a-zA-Z0-9_]*$/g,
                message: '请输入格式正确的内容',
              },
            ]}
          >
            <Input placeholder="如：关" allowClear disabled={disabled} />
          </Form.Item>
          <Form.Item
            label="1-"
            name="name-1"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            rules={[
              { required: true, message: '布尔值不能为空' },
              {
                pattern:
                  /^[\u4e00-\u9fa5a-zA-Z0-9][\u4e00-\u9fa5a-zA-Z0-9_]*$/g,
                message: '请输入格式正确的内容',
              },
            ]}
          >
            <Input placeholder="如：关" allowClear disabled={disabled} />
          </Form.Item>
        </Flex>
      </Form.Item>
    </>
  );
});

export default BoolModule;
