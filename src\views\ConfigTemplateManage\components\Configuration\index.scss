.configuration-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  .search-item {
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    .label {
      margin-right: 8px;
      font-size: 14px;
      position: relative;

      &::before {
        content: '*';
        color: #ff4d4f;
        margin-right: 4px;
        font-family: SimSun, sans-serif;
        line-height: 1;
      }
    }
  }
  .content-container {
    display: flex;
    flex-direction: column;
    padding: 16px;
    height: calc(100% - 46px); // 减去 tab 的高度
    gap: 16px; // 上下间距
  }

  .module-section {
    min-height: 200px;
    max-height: 300px;
    overflow: auto;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    padding: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #333;

      .header-actions {
        display: flex;
        gap: 8px;

        .ant-btn-link {
          padding: 0 4px;
          height: 24px;
          line-height: 24px;
          font-size: 12px;

          &:first-child {
            color: #1890ff;
          }

          &:last-child {
            color: #ff4d4f;
          }
        }
      }
    }

    .ant-tree {
      background: transparent;

      .ant-tree-treenode {
        padding: 4px 0;

        &.ant-tree-treenode-parent {
          font-weight: 500;
        }

        &.ant-tree-treenode-leaf-last {
          font-weight: normal;
        }
      }

      .ant-tree-checkbox {
        margin: 4px 8px 0 0;
      }

      .ant-tree-node-content-wrapper {
        &:hover {
          background-color: transparent;
        }
      }
    }
  }

  .file-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    padding: 16px;

    .table-info {
      margin: 8px 0;
      color: #666;
      font-size: 14px;
    }

    .ant-table-wrapper {
      .ant-table-selection-column {
        width: 48px;
      }
    }

    .section-header {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #333;
    }

    .search-bar {
      margin-bottom: 16px;

      .ant-input-search {
        max-width: 400px;
      }
    }

    .ant-table-wrapper {
      flex: 1;

      .ant-table {
        height: 100%;
      }
    }
  }

  // Tab 样式覆盖
  .ant-tabs-nav {
    margin: 0;
    padding: 0 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
  }
}
