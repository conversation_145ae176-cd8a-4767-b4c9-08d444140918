import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  Radio,
  Row,
  Select,
  Table,
  message,
} from 'antd';
import ProductManageFetch from '@/fetch/bussiness/productManage';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
import CreateTag from '@/components/TagLabel/CreateTag';

const AddProduct = () => {
  const fetchApi = new ProductManageFetch();
  const tagRef = useRef<any>(null);
  const modelRef = useRef<any>(null);
  const navigator = useNavigate();
  const [formRef] = Form.useForm();
  const goBack = () => {
    navigator('/product');
  };
  const submit = async () => {
    const val = await formRef.validateFields();
    const productModelList = modelRef.current.getTagList();
    if (!productModelList) {
      return;
    }
    const tagList = tagRef.current.getTagList();
    if (!tagList) {
      return;
    }
    const info = {
      productName: val.productName,
      productKey: val.productKey,
      productModelList: productModelList.map((v: AnyObj) => v.name),
      productDescription: val.productDescription,
      tagList: tagList.map((v: AnyObj) => ({
        tagKey: v.name,
        tagValue: v.value,
      })),
    };
    const res: AnyObj = await fetchApi.addProduct(info);
    if (res.code === HttpStatusCode.Success) {
      navigator('/product');
      message.success(res.message);
    } else {
      message.error(res.message);
    }
  };
  return (
    <div className="add-product">
      <div className="title" onClick={goBack}>
        <ArrowLeftOutlined />
        <span>创建产品</span>
      </div>

      <Form form={formRef} labelCol={{ span: 3 }} wrapperCol={{ span: 19 }}>
        <Form.Item
          label="产品名称"
          name="productName"
          rules={[
            { required: true, message: '请输入产品名称' },
            {
              pattern:
                /^[A-Za-z0-9\u4e00-\u9fa5\u3040-\u309f\u30a0-\u30ff_@()-]+$/g,
              message:
                '支持中文、英文字母、、数字、下划线（_）、短划线（-）、at（@）和英文圆括号（()），长度限制1~30个字数',
            },
          ]}
        >
          <Input maxLength={30} allowClear placeholder="请输入产品名称" />
        </Form.Item>
        <Form.Item
          label="产品标识"
          name="productKey"
          rules={[
            {
              pattern: /^[A-Za-z0-9_]+$/g,
              message: '支持英文字母、数字，下划线（_），长度限制1~30个字数',
            },
          ]}
        >
          <Input maxLength={30} allowClear placeholder="请输入产品标识" />
        </Form.Item>
        <Form.Item label="型号" name="productModelList">
          <CreateTag
            key={'modal-create'}
            ref={modelRef}
            maxLength={50}
            addBtn="+添加型号"
            columnsList={[
              {
                title: '型号',
                dataIndex: 'name',
                ellipsis: true,
                placeholder: '请输入型号值',
              },
              {
                title: '',
                dataIndex: 'operate',
                ellipsis: true,
              },
            ]}
          />
        </Form.Item>
        <Form.Item label="产品描述" name="productDescription">
          <Input.TextArea
            placeholder="请输入产品描述"
            showCount
            allowClear
            maxLength={400}
            style={{ height: 120, resize: 'none' }}
          />
        </Form.Item>
        <Form.Item label="产品标签" name="tagList">
          <CreateTag key={'tag-create'} ref={tagRef} maxLength={50} />
        </Form.Item>
      </Form>

      <div className="bottom-btns">
        <Button type="primary" onClick={submit}>
          确认
        </Button>
        <Button onClick={goBack}>取消</Button>
      </div>
    </div>
  );
};

export default React.memo(AddProduct);
