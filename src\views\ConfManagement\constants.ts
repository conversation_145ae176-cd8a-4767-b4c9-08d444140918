import { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import TSLModel from '@/fetch/bussiness/TSLModel';
import { message } from 'antd';

// HTTPResponse 接口
export interface HTTPResponse {
  code: string | number;
  data: any;
  message?: string;
  errorCode?: number;
}

// 产品选项类型
export interface ProductOption {
  label: string;
  value: string;
}

// 模块选项类型
export interface BlockOption {
  label: string;
  value: string;
}

// 搜索条件接口
export interface SearchCondition {
  pageNum: number;
  pageSize: number;
  productKey?: string;
  blockNo?: string;
  itemNo?: string;
  itemName?: string;
  createUser?: string;
  enable?: number;
  createTimeStart?: string;
  createTimeEnd?: string;
}

// 表单配置
export const formConfig: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '产品',
      fieldName: 'productKey',
      options: [], // 将在组件中动态设置
      placeholder: '请选择产品',
      showSearch: true,
      allowClear: true,
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 6,
      xl: 8,
    },
    {
      type: 'select',
      label: '模块',
      fieldName: 'blockNo',
      placeholder: '请选择',
      options: [], // 将在组件中动态设置
      showSearch: true,
      allowClear: true,
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 6,
      xl: 8,
    },
    {
      type: 'input',
      label: '配置项标识符',
      fieldName: 'itemNo',
      placeholder: '请输入',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
    },
    {
      type: 'input',
      label: '配置项名称',
      fieldName: 'itemName',
      placeholder: '请输入',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
    },
    {
      type: 'input',
      label: '创建人',
      fieldName: 'createUser',
      placeholder: '请输入创建人账号',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
    },
    {
      type: 'select',
      label: '状态',
      fieldName: 'enable',
      placeholder: '请选择',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      labelInValue: false,
      xxl: 6,
      xl: 8,
    },
    {
      type: 'rangeTime',
      label: '创建时间',
      fieldName: 'createTimeRange',
      placeholder: '请选择时间范围',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 8,
      xl: 10,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'blockNo',
        rule: 'fetchData',
        fetchFunc: async (productKey: string) => {
          if (!productKey) {
            return [];
          }
          try {
            const tslModelInstance = new TSLModel();
            const res = await tslModelInstance.getThingModelBlocks({
              productKey,
            });

            if (
              res &&
              (res.code === HttpStatusCode.Success ||
                res.code === 200 ||
                res.code === '200') &&
              res.data
            ) {
              return res.data.map((item: any) => ({
                label: item.blockName,
                value: item.blockNo,
              }));
            } else {
              message.error(res.message || '获取模块列表失败');
              return [];
            }
          } catch (error) {
            console.error('获取模块列表出错:', error);
            message.error('获取模块列表出错');
            return [];
          }
        },
      },
      {
        linkFieldName: 'blockNo',
        rule: 'clear',
      },
    ],
  },
};

// 表格列配置
export const columns = [
  {
    title: '序号',
    dataIndex: 'id',
    width: 80,
    align: 'center',
  },
  {
    title: '配置项标识符',
    dataIndex: 'itemNo',
    width: 150,
  },
  {
    title: '配置项名称',
    dataIndex: 'itemName',
    width: 150,
  },
  {
    title: '模块',
    dataIndex: 'blockNo',
    width: 120,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
  },
  {
    title: '更新人',
    dataIndex: 'updater',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    render: (text: string) => {
      // 返回一个字符串，在组件中会被解析为 HTML
      return `<span style="color: ${
        text === '启用' ? '#52c41a' : '#ff4d4f'
      }">${text}</span>`;
    },
  },
];
