import { Button, Form, Input, message, Select, Table, Row, Col } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useContext, useEffect, useRef, useState } from 'react';
import SelectVersion from '../SelectVersion';
import { api } from '@/fetch/core/api';
import { application } from '../../utils/constant';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
const EditableContext = React.createContext<FormInstance<any> | null>(null);
interface Item {
  key: string;
  appName: any;
  version: string; // 版本名
  versionNumber: string; // 版本号
}
interface EditableRowProps {
  index: number;
}
const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  children: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  moduleOptions: any;
  saveSelectModule: Function;
  onSelectVersion: Function;
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  children,
  dataIndex,
  record,
  saveSelectModule,
  onSelectVersion,
  moduleOptions,
  ...restProps
}) => {
  let childNode = children;
  const form: any = useContext(EditableContext);
  useEffect(() => {
    form?.setFieldsValue({
      appName: record.appName,
      version: record.version,
    });
  }, [record]);
  switch (dataIndex) {
    case 'appName':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'appName'}>
          <Select
            labelInValue
            placeholder={`请选择模块`}
            options={moduleOptions}
            onChange={(value: any) => {
              saveSelectModule(value, record.key);
            }}
          />
        </Form.Item>
      );
      break;
    case 'version':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'version'}>
          <Input
            style={{ textAlign: 'center' }}
            placeholder={`请选择版本号`}
            onFocus={() => onSelectVersion(record.key)}
            value={record.version}
            disabled={record.appName?.value === application.CONF ? true : false}
          />
        </Form.Item>
      );
      break;
    case 'versionNumber':
      childNode = <div>{record.versionNumber}</div>;
      break;
  }
  return <td {...restProps}>{childNode}</td>;
};

type EditableTableProps = Parameters<typeof Table>[0];
type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;
const UpdateSetting = (props: any) => {
  const [moduleOptions, setModuleOptions] = useState<any[]>();
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>();
  const [count, setCount] = useState<number>(2);
  const [dataSource, setDataSource] = useState<any[]>([
    {
      key: 1,
      appName: null,
      version: null,
      versionNumber: '系统带入',
      repeat: false,
    },
  ]);
  const defaultColumns = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: '10%',
      ellipsis: true,
      render: (text: any, record: number, index: number) => index + 1,
    },
    {
      title: (
        <div>
          <span style={{ color: 'red' }}>*</span>&nbsp;模块
        </div>
      ),
      dataIndex: 'appName',
      align: 'center',
      width: '20%',
      ellipsis: true,
    },
    {
      title: (
        <div>
          <span style={{ color: 'red' }}>*</span>&nbsp;版本号
        </div>
      ),
      dataIndex: 'version',
      align: 'center',
      width: '45%',
      ellipsis: true,
    },
    {
      title: '系统版本编号',
      dataIndex: 'versionNumber',
      align: 'center',
      width: '25%',
      ellipsis: true,
    },
  ];
  const columns = defaultColumns?.map((col) => {
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        dataIndex: col.dataIndex,
        title: col.title,
        moduleOptions: moduleOptions,
        saveSelectModule: saveSelectModule,
        onSelectVersion: onSelectVersion,
      }),
    };
  });
  useEffect(() => {
    fetchModuleOptions();
  }, []);

  /**
   * 获取模块下拉框列表
   */
  const fetchModuleOptions = () => {
    request({
      method: 'POST',
      path: api.getApplicationList,
      body: {
        enable: props.enable,
        productType: props.productType,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setModuleOptions(
            res.data?.map((item: any) => {
              return {
                value: item.appName,
                label: item.appAlias,
              };
            }),
          );
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  /**
   * 保存当前选中的模块
   * @param {Object} value 选中的模块数据
   * @param {number} key 当前行的key
   */
  const saveSelectModule = (value: any, key: number) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => key === item.key);
    const item = newData[index];
    let isRepeat: boolean = false;
    dataSource?.forEach((item: any) => {
      if (item.appName?.value === value.value) {
        isRepeat = true;
        return;
      }
    });
    if (isRepeat) {
      message.error(`${value.label}模块已存在，不允许重复选择！`);
      newData.splice(index, 1, {
        key: item.key,
        appName: value,
        version: null,
        versionNumber: '系统带入',
        repeat: isRepeat,
      });
      setDataSource(newData);
    } else {
      newData.splice(index, 1, {
        key: item.key,
        appName: value,
        version: value.value === application.CONF ? '系统生成当前时间' : null,
        versionNumber:
          value.value === application.CONF ? '系统生成当前时间' : '系统带入',
        repeat: isRepeat,
      });
      setDataSource(newData);
    }
  };

  /**
   * 点击选择版本名，拿到当前行，并获取版本信息
   * @param {number} key 当前行的key
   */
  const onSelectVersion = (key: number) => {
    const data = dataSource.find((item: any) => item.key === key);
    if (data?.appName && !data.repeat) {
      if (data.appName.value !== application.CONF) {
        setCurrentRow(dataSource.find((item: any) => item.key === key));
        setModalShow(true);
      }
    } else if (data?.appName && data.repeat) {
      message.error(`请先修改重复模块`);
    } else if (!data?.appName) {
      message.error('请先选择模块');
    }
  };

  /**
   * 选中版本号后点击提交按钮
   * @param {Object} value 选中的版本信息
   * @param {Object} row 当前行的信息
   */
  const onSubmit = (value: any, row: any) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      version: value[0].version,
      versionNumber: value[0].versionNumber,
    });
    setDataSource(newData);
    setModalShow(false);
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  /**
   * 增加删除表的数据源
   */
  const makeEditDatasource = () => {
    const editList = dataSource?.map((item, index) => {
      return {
        name: index === 0 ? '+' : '-',
        key: item.key,
      };
    });
    return editList;
  };

  /**
   * 增加一行
   */
  const onAddClick = () => {
    const filteredList = dataSource.filter(
      (item: any) =>
        item.appName === undefined ||
        item.version === undefined ||
        item.version === null,
    );
    if (filteredList.length > 0) {
      message.error('请先完善当前信息');
      return;
    }
    setDataSource([
      ...dataSource,
      {
        key: count,
        appName: null,
        version: null,
        versionNumber: '系统带入',
        repeat: false,
      },
    ]);
    setCount(count + 1);
  };

  /**
   * 删除一行
   * @param {number} index 要删除行的index
   */
  const onDeleteClick = (index: number) => {
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
  };

  useEffect(() => {
    props.form.setFieldsValue({
      upgradeAppInfoList: dataSource?.map((item: any) => {
        if (item.appName?.value === application.CONF) {
          return {
            appName: item.appName?.value,
            versionNumber: null,
          };
        } else {
          return {
            appName: item.appName?.value,
            versionNumber: item.versionNumber,
          };
        }
      }),
    });
  }, [JSON.stringify(dataSource)]);
  return (
    <>
      <Row>
        <Col span={23}>
          <Table
            pagination={false}
            components={components}
            rowClassName={() => 'editable-row'}
            bordered
            dataSource={dataSource}
            columns={columns as ColumnTypes}
          />
        </Col>
        <Col span={1}>
          <Table
            columns={[
              {
                title: 'has',
                dataIndex: 'name',
                align: 'left',
                onHeaderCell: (data, index) => {
                  return {
                    style: {
                      backgroundColor: 'rgba(0, 0, 0, 0)',
                      color: 'white',
                      borderWidth: 0,
                    },
                  };
                },
                onCell: (data, index) => {
                  return {
                    style: {
                      backgroundColor: 'white',
                      borderWidth: 0,
                      padding: 0,
                      height: 65,
                    },
                  };
                },
                render: (name, _: any, index: number) => {
                  const bgColor = name === '+' ? '#31C2A6' : '#D9001B';
                  return name === '-' ? (
                    <Button
                      onClick={() => {
                        onDeleteClick && onDeleteClick(index);
                      }}
                      style={{
                        marginLeft: 15,
                        color: bgColor,
                        width: 55,
                        borderColor: bgColor,
                        borderRadius: 4,
                      }}
                    >
                      {' '}
                      {name}
                    </Button>
                  ) : (
                    <Button
                      onClick={() => {
                        onAddClick && onAddClick();
                      }}
                      style={{
                        marginLeft: 15,
                        color: bgColor,
                        width: 55,
                        borderColor: bgColor,
                        borderRadius: 4,
                      }}
                    >
                      {' '}
                      {name}
                    </Button>
                  );
                },
              },
            ]}
            dataSource={makeEditDatasource()}
            pagination={false}
          />
        </Col>
      </Row>
      {modalShow && (
        <SelectVersion
          currentRow={currentRow}
          modalShow={modalShow}
          closeModal={() => {
            setModalShow(false);
          }}
          onSubmit={(value: any, module: any) => onSubmit(value, module)}
        />
      )}
    </>
  );
};

export default React.memo(UpdateSetting);
