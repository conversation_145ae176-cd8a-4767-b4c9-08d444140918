import {
  render as reactRender,
  unmount as reactUnmount,
} from 'rc-util/lib/React/render';
import React, { ReactNode, useEffect, useState } from 'react';
import { Modal, Button, message } from 'antd';
import { debounce } from 'lodash';

interface Props {
  type?: 'confirm';
  okText?: string;
  cancelText?: string;
  footer?: Array<{
    type?: 'cancelBtn' | 'notCancelBtn';
    text: string;
    onClick?: Function;
    showLoading?: boolean;
    otherCSSProperties?: React.CSSProperties;
    needValidate?: boolean;
  }>;
  content: ReactNode;
  title: ReactNode;
  width?: string | number;
  onCancel?: Function;
  onOk?: Function;
}

interface ModalProps {
  onCancel?: Function;
  onOk?: Function;
  type?: 'confirm' | 'modalForm';
  okText?: string;
  cancelText?: string;
  footer?: Array<{
    type?: 'cancelBtn' | 'notCancelBtn';
    text: string;
    showLoading?: boolean;
    onClick?: Function;
    otherCSSProperties?: React.CSSProperties;
  }>;
  content: ReactNode;
  title: ReactNode;
  visible: boolean;
  width?: string | number;
}
// 只适用于二次确认的弹窗，比如停用启用删除
const NewModal = (modalProps: ModalProps) => {
  const {
    onCancel,
    onOk,
    okText,
    cancelText,
    footer,
    content,
    title,
    visible,
    width,
    type,
  } = modalProps;

  const [modalVisible, setModalVisible] = useState(visible);
  const [loading, setLoading] = useState<boolean>(false);
  const handleClose = () => {
    const el = document.getElementById('commonModal');
    if (el) {
      el.parentElement && el.parentElement.removeChild(el);
    }
    setModalVisible(false);
    onCancel && onCancel();
  };

  const showLoading = () => {
    setLoading(true);
  };
  const hideLoading = () => {
    setLoading(false);
  };
  const footerNode: ReactNode = (
    <div className="modal-footer-btn">
      {footer &&
        footer.length > 0 &&
        footer.map((item: any, index: number) => {
          return (
            <Button
              key={index}
              onClick={() => {
                if (loading) {
                  return;
                }
                item.onClick &&
                  item.onClick(handleClose, showLoading, hideLoading);
                !item.needValidate && handleClose();
              }}
              type={item.type === 'cancelBtn' ? 'default' : 'primary'}
              style={item.otherCSSProperties}
            >
              {loading ? '上传中...' : item.text}
            </Button>
          );
        })}
    </div>
  );

  return (
    <Modal
      width={width ?? 520}
      destroyOnClose={true}
      visible={modalVisible}
      title={title}
      onOk={() => {
        debounce(() => {
          onOk && onOk(handleClose);
          type === 'confirm' && handleClose();
        }, 500)();
      }}
      onCancel={handleClose}
      footer={footer && footerNode}
      cancelText={cancelText}
      okText={okText}
    >
      {content}
    </Modal>
  );
};

export default function showModal(props: Props) {
  let el = document.getElementById('commonModal');
  if (!el) {
    el = document.createElement('div');
    el.setAttribute('id', 'commonModal');
    document.body.appendChild(el);
  }

  reactRender(
    <NewModal
      type={props.type}
      visible={true}
      okText={props.okText ?? '确定'}
      cancelText={props.cancelText ?? '取消'}
      onCancel={props.onCancel}
      onOk={props.onOk}
      content={props.content}
      footer={props.footer}
      title={props.title}
      width={props.width}
    />,
    el,
  );
}
