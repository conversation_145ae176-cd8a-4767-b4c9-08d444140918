# ProductModuleSelector 使用示例

## 重要说明
- **简化设计**：通过 `formConfig` 的存在与否来决定是否渲染表单
- **传递 `formConfig`**：渲染表单 + 模块选择
- **不传递 `formConfig`**：只显示模块选择
- **减少依赖**：移除了props之间的复杂依赖关系

## 1. 基本使用（只显示模块选择）

```tsx
import ProductModuleSelector from '@/components/ProductModuleSelector';

// 只需要模块选择功能，不传递formConfig
<ProductModuleSelector
  ref={productModuleSelectorRef}
  title="选择模块"
  selectedData={{
    productKey: 'some-product-key', // 直接传入产品key
  }}
  onChange={(data) => {
    console.log('选中的模块:', data.selectedModules);
  }}
/>
```

## 2. 带表单的使用（第一步配置选择）

```tsx
// 第一步表单配置
const firstStepFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      validatorRules: [{ required: true, message: '请选择产品' }],
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const deviceApi = new Device();
          const res = await deviceApi.queryProductList();
          return res.data?.map(item => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
        },
      },
    ],
  },
};

<ProductModuleSelector 
  ref={productModuleSelectorRef}
  formConfig={firstStepFormConfig}  // 传递配置则渲染表单
  formType="search"
  title="选择产品及模块"
  onChange={(data) => {
    console.log('产品和模块:', data);
  }}
/>
```

## 3. 带表单的使用（第二步模板编辑）

```tsx
// 第二步表单配置
const secondStepFormConfig = {
  fields: [
    {
      fieldName: 'templateName',
      label: '模板名称',
      type: 'input',
      validatorRules: [{ required: true, message: '请输入模板名称' }],
      maxLength: 50,
      showCount: true,
    },
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      disabled: true,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      multiple: true,
      validatorRules: [{ required: true, message: '请选择型号' }],
    },
    {
      fieldName: 'remark',
      label: '备注',
      type: 'textArea',
      maxLength: 150,
      showCount: true,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const deviceApi = new Device();
          const res = await deviceApi.queryModelList(val);
          return res.data?.map(item => ({
            label: item.modelName,
            value: item.modelNo,
          })) || [];
        },
      },
    ],
  },
};

<ProductModuleSelector 
  ref={productModuleSelectorRef}
  formConfig={secondStepFormConfig}  // 传递配置则渲染表单
  formType="edit"
  title="模板基本信息"
  selectedData={{
    productKey: 'product-123',
    templateName: '模板名称',
    productModelNoList: ['model-1', 'model-2'],
    remark: '备注信息',
  }}
/>
```

## 4. 获取数据

```tsx
const handleGetData = async () => {
  try {
    const data = await productModuleSelectorRef.current?.getSelectedData();
    console.log('获取的数据:', data);
    
    // 数据结构：
    // {
    //   productKey: string,
    //   selectedModules: string[],
    //   // 如果有表单配置，还会包含表单字段：
    //   templateName?: string,
    //   productModelNoList?: string[],
    //   remark?: string,
    //   // ... 其他自定义字段
    // }
  } catch (error) {
    console.error('验证失败:', error.message);
  }
};
```

## Props 说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| selectedData | any | - | 初始选中的数据 |
| onChange | function | - | 数据变化回调 |
| formConfig | any | - | 表单配置对象，传递则渲染表单，不传递则只显示模块选择 |
| formType | 'search' \| 'edit' | 'search' | 表单类型 |
| title | string | '产品模块选择' | 组件标题 |

## 设计优势

### 1. 简化的API
- 只需要5个props，减少了复杂性
- 通过formConfig的存在与否自动判断是否需要表单
- 移除了needFormConfig等冗余参数

### 2. 直观的使用方式
```tsx
// 只要模块选择
<ProductModuleSelector />

// 要表单 + 模块选择
<ProductModuleSelector formConfig={myConfig} />
```

### 3. 减少错误
- 不会出现needFormConfig=true但formConfig为空的情况
- 参数间没有复杂的依赖关系
- 更容易理解和使用

## 迁移指南

### 从旧版本迁移

```tsx
// 旧版本
<ProductModuleSelector 
  needFormConfig={false}
  selectedData={data}
/>

// 新版本
<ProductModuleSelector 
  selectedData={data}
/>

// 旧版本
<ProductModuleSelector 
  needFormConfig={true}
  formConfig={myConfig}
  selectedData={data}
/>

// 新版本
<ProductModuleSelector 
  formConfig={myConfig}
  selectedData={data}
/>
```

### 核心变化
1. 移除了 `needFormConfig` 参数
2. 通过 `formConfig` 是否存在来判断是否渲染表单
3. 简化了组件的使用方式
4. 减少了参数验证的复杂性
