import React, { useEffect, useState } from 'react';
import { Select, Modal, message, Spin, Row, Col } from 'antd';
import { request } from '@/fetch/core';
import { api } from '@/fetch/core/api';
import './index.scss';
import { ButtonType, CustomButton } from '../CustomButton';
import { HttpStatusCode } from '@/fetch/core/constant';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';

interface Props {
  deviceName: string;
  productKey: string;
  selectedIssueNumber: string;
  diffModalOpen: boolean; // 是否显示modal
  onCancel: AnyFunc; // 关闭弹窗
  footer?: React.ReactNode;
  title?: React.ReactNode;
}
const BatchDiffModal = (props: Props) => {
  const {
    deviceName,
    productKey,
    selectedIssueNumber,
    diffModalOpen,
    footer,
    title,
    onCancel,
  } = props;
  const [issueNumberList, setIssueNumberList] = useState<
    { label: string; value: string }[]
  >([]);
  const [comparedIssueNumber, setComparedIssueNumber] = useState<
    string | undefined
  >();
  const [loading, setLoading] = useState<boolean>(false);
  const [contentList, setContentList] = useState<
    | {
        fileName: string;
        selectedFileContent: string;
        comparedFileContent: string;
      }[]
    | null
  >(null);
  const getIssueListIncludeConfOfDevice = async () => {
    try {
      const requestOptions: RequestOptions = {
        method: 'POST',
        path: api.getIssueListIncludeConfOfDevice,
        body: {
          deviceName,
          productKey,
        },
      };
      const res: any = await request(requestOptions);
      if (res.code === HttpStatusCode.Success) {
        res.data &&
          setIssueNumberList(
            res.data.map((issueNumber: string) => {
              return {
                label: issueNumber,
                value: issueNumber,
              };
            }),
          );
      } else {
        message.error(res.message || '获取发布计划编号列表失败');
        return;
      }
    } catch (err) {
      console.error('获取发布计划编号列表失败', err);
      return;
    }
  };
  const getIssueConfCompare = async () => {
    try {
      setLoading(true);
      const requestOptions: RequestOptions = {
        method: 'POST',
        path: api.getIssueConfCompare,
        body: {
          productKey,
          deviceName,
          selectedIssueNumber,
          comparedIssueNumber,
        },
      };
      const res: any = await request(requestOptions);
      if (res.code === HttpStatusCode.Success) {
        setContentList(res.data);
      } else {
        message.error(res.message || '获取config对比失败');
        return;
      }
    } catch (err) {
      console.error('获取config对比失败', err);
    } finally {
      setLoading(false);
    }
  };
  const closeModal = () => {
    onCancel();
    setIssueNumberList([]);
    setComparedIssueNumber(undefined);
    setContentList(null);
  };
  useEffect(() => {
    diffModalOpen && getIssueListIncludeConfOfDevice();
  }, [diffModalOpen]);

  return (
    <>
      <Modal
        open={diffModalOpen}
        width={1500}
        title={title ?? `${deviceName}版本对比`}
        destroyOnClose
        onCancel={closeModal}
        footer={
          footer ?? (
            <>
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={closeModal}
                title={'关闭'}
              />
            </>
          )
        }
      >
        <div className="header">
          <Row justify={'space-between'} align={'middle'}>
            <Col>发布计划编号：{selectedIssueNumber}</Col>
            <Col
              span={12}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <div className="form" style={{ width: '80%' }}>
                对比发布计划编号：
                <Select
                  options={issueNumberList || []}
                  onChange={(value) => {
                    setComparedIssueNumber(value);
                  }}
                  style={{ width: '70%' }}
                  placeholder={'请选择，支持关键字联想全称'}
                  showSearch
                  allowClear
                  onClear={() => {
                    setComparedIssueNumber(undefined);
                    setContentList(null);
                  }}
                ></Select>
              </div>
              <CustomButton
                buttonType={ButtonType.PrimaryButton}
                onSubmitClick={getIssueConfCompare}
                title={'确定对比'}
              />
            </Col>
          </Row>
        </div>
        <div className="diff-wrapper">
          <Spin tip="Loading..." spinning={loading}>
            {contentList && contentList.length > 0 ? (
              <>
                {contentList.map((content, index) => {
                  return (
                    <Row
                      key={`${content?.fileName}_index`}
                      style={{ marginBottom: '12px' }}
                    >
                      <ReactDiffViewer
                        leftTitle={content?.fileName}
                        rightTitle={content?.fileName}
                        oldValue={content?.selectedFileContent ?? ''}
                        newValue={content?.comparedFileContent ?? ''}
                        compareMethod={DiffMethod.WORDS}
                        splitView={true}
                        styles={{
                          content: {
                            fontSize: '12px',
                            overflowWrap: 'anywhere',
                            width: '50%',
                          },
                        }}
                      />
                    </Row>
                  );
                })}
              </>
            ) : (
              <></>
            )}
          </Spin>
        </div>
      </Modal>
    </>
  );
};
export default React.memo(BatchDiffModal);
