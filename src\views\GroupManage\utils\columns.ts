import { FormConfig } from '@/components/CommonForm/formConfig';
import { regex } from '@/utils/utils';
import dayjs from 'dayjs';

export const SearchForm: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '产品',
      fieldName: 'productKey',
      placeholder: '请选择产品',
      labelInValue: false,
      allowClear: false,
    },
    {
      type: 'input',
      label: '分组名称',
      fieldName: 'groupName',
      placeholder: '请输入分组名称',
    },
    {
      type: 'ReactNode',
      label: '分组标签',
      fieldName: 'tagList',
      placeholder: '请选择分组标签',
      multiple: true,
    },
  ],
};

export const GroupColumns: any[] = [
  {
    title: '分组编号',
    dataIndex: 'groupNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分组名称',
    dataIndex: 'groupName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分组路径',
    dataIndex: 'levelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    ellipsis: true,
  },
];

export const GroupForm: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '产品',
      validatorRules: [{ required: true, message: '请选择产品' }],
      fieldName: 'productKey',
      placeholder: '请选择产品',
      labelInValue: false,
    },
    {
      type: 'cascader',
      label: '父组',
      fieldName: 'parentNo',
      placeholder: '请选择',
      labelInValue: false,
    },
    {
      type: 'input',
      label: '分组名称',
      fieldName: 'name',
      placeholder: '请输入分组名称',
      maxLength: 30,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            if (!value?.trim()) {
              return Promise.reject(new Error('请输入分组名称'));
            }
            if (regex.test(value?.trim())) {
              return Promise.resolve();
            } else {
              return Promise.reject(
                new Error('请输入中文、英文字母、数字和下划线（_）'),
              );
            }
          },
        },
      ],
    },
    {
      type: 'textarea',
      label: '分组描述',
      fieldName: 'description',
      placeholder: '请输入分组描述',
      maxLength: 200,
      showCount: true,
      autoSize: { minRows: 2, maxRows: 6 },
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            if (!value?.trim()) {
              return Promise.resolve();
            }
            if (regex.test(value?.trim())) {
              return Promise.resolve();
            } else {
              return Promise.reject(
                new Error('请输入中文、英文字母、数字和下划线（_）'),
              );
            }
          },
        },
      ],
    },
    {
      type: 'ReactNode',
      fieldName: 'tagList',
    },
  ],
};

export const GroupDetailSearchForm: FormConfig = {
  fields: [
    {
      type: 'select',
      label: '型号',
      fieldName: 'productModelNo',
      placeholder: '请选择型号',
      labelInValue: false,
    },
    {
      type: 'input',
      label: '设备名称',
      fieldName: 'deviceName',
      placeholder: '请输入设备名称',
    },
  ],
};

export const DeviceListColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属产品',
    dataIndex: 'productName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'productModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备别名',
    dataIndex: 'remarkName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '注册时间',
    dataIndex: 'registerTime',
    align: 'center',
    ellipsis: true,
    render: (text: any) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];
