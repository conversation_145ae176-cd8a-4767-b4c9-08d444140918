import React, { useState, useRef, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  Form,
  Input,
  Select,
  Tabs,
  Button,
  Table,
  Space,
  message,
  Card,
} from 'antd';
import type { FormInstance } from 'antd';
import type { TableRowSelection } from 'antd/es/table/interface';

import { HttpStatusCode } from '@/fetch/core/constant';
import { ConfigTemplateApi, Device } from '@/fetch/bussiness';
import { SelectedData } from '../Configuration';
import './index.scss';
import showModal from '@/components/commonModal';
import TextArea from 'antd/es/input/TextArea';
import StructuredConfigEditor from '@/components/StructuredConfigEditor';

interface EditConfigurationProps {
  selectedConfig: SelectedData;
}

interface TemplateFormData {
  templateName: string;
  productKey: string;
  productModelNoList: string[];
  remark: string;
}

interface FileItem {
  fileNo: string;
  fileName: string;
  content: string;
  blockNo: string;
  blockName: string;
}

// 编辑配置文件弹窗
const EditConfigFileModal = forwardRef<FormInstance, { data: any }>(
  ({ data }, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => form);

    return (
      <Form
        form={form}
        layout="vertical"
        initialValues={data}
        className="edit-config-file-form"
      >
        <Form.Item label="配置文件编号" name="fileNo">
          <Input disabled />
        </Form.Item>
        <Form.Item label="配置文件名称" name="fileName">
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="配置内容"
          name="content"
          rules={[{ required: true, message: '请输入配置内容' }]}
        >
          <TextArea
            autoSize={{ minRows: 8, maxRows: 15 }}
            placeholder="请输入配置内容"
          />
        </Form.Item>
      </Form>
    );
  }
);

const EditConfiguration = forwardRef<
  {
    getTemplateData: () => Promise<any>;
  },
  EditConfigurationProps
>(({ selectedConfig }, ref) => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const deviceApi = useRef(new Device());
  const [form] = Form.useForm();
  
  const [configData, setConfigData] = useState<any[]>([]);
  const [activeBlockNo, setActiveBlockNo] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [productOptions, setProductOptions] = useState<any[]>([]);
  const [modelOptions, setModelOptions] = useState<any[]>([]);
  
  // 存储配置数据
  const configDataRef = useRef<Record<string, any>>({});
  const fileDataRef = useRef<Record<string, any>>({});

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getTemplateData: async () => {
      try {
        // 验证基本信息表单
        const formValues = await form.validateFields();
        
        // 构造API参数
        const templateData = {
          ...formValues,
          blockConfigList: configData.map(block => ({
            blockNo: block.blockNo,
            itemList: Object.keys(configDataRef.current[block.blockNo] || {}).map(itemNo => ({
              itemNo,
              content: JSON.stringify(configDataRef.current[block.blockNo][itemNo] || {}),
            })),
            fileList: Object.keys(fileDataRef.current[block.blockNo] || {}).map(fileNo => ({
              fileNo,
              content: fileDataRef.current[block.blockNo][fileNo] || '{}',
            })),
          })),
        };
        
        return templateData;
      } catch (error) {
        throw new Error('请完善必填信息');
      }
    },
  }));

  // 获取产品列表
  useEffect(() => {
    fetchProductList();
  }, []);

  // 当选中数据变化时，获取配置数据
  useEffect(() => {
    if (selectedConfig?.product?.value) {
      fetchConfigData();
      fetchModelList(selectedConfig.product.value);
      
      // 设置表单初始值
      form.setFieldsValue({
        productKey: selectedConfig.product.value,
      });
    }
  }, [selectedConfig]);

  const fetchProductList = async () => {
    try {
      const res = await deviceApi.current.queryProductList();
      if (res.code === HttpStatusCode.Success) {
        const options = res.data?.map((item: any) => ({
          label: item.productName,
          value: item.productKey,
        })) || [];
        setProductOptions(options);
      }
    } catch (error) {
      message.error('获取产品列表失败');
    }
  };

  const fetchModelList = async (productKey: string) => {
    try {
      const res = await deviceApi.current.queryModelList(productKey);
      if (res.code === HttpStatusCode.Success) {
        const options = res.data?.map((item: any) => ({
          label: item.modelName,
          value: item.modelNo,
        })) || [];
        setModelOptions(options);
      }
    } catch (error) {
      message.error('获取型号列表失败');
    }
  };

  const fetchConfigData = async () => {
    setLoading(true);
    try {
      const res = await configTemplateApi.current.getEnableConfigDetailList({
        productKey: selectedConfig.product.value,
        blockConfigList: selectedConfig.blockData.map((v: any) => ({
          blockNo: v.blockNo,
          itemNoList: v.selectedItems,
          fileNoList: v.selectedFiles,
        })),
      });

      if (res.code === HttpStatusCode.Success) {
        setConfigData(res.data || []);
        if (res.data && res.data.length > 0) {
          setActiveBlockNo(res.data[0].blockNo);
          
          // 初始化数据存储
          const initialConfigData: Record<string, any> = {};
          const initialFileData: Record<string, any> = {};
          
          res.data.forEach((block: any) => {
            initialConfigData[block.blockNo] = {};
            initialFileData[block.blockNo] = {};
            
            // 初始化文件数据
            if (block.fileList) {
              block.fileList.forEach((file: any) => {
                initialFileData[block.blockNo][file.fileNo] = file.content || '{}';
              });
            }
          });
          
          configDataRef.current = initialConfigData;
          fileDataRef.current = initialFileData;
        }
      } else {
        message.error(res.message || '获取配置详情失败');
      }
    } catch (error) {
      message.error('获取配置数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理配置项数据变化
  const handleConfigDataChange = (blockNo: string, data: Record<string, any>) => {
    configDataRef.current = {
      ...configDataRef.current,
      [blockNo]: data,
    };
  };

  // 编辑配置文件
  const handleEditFile = async (file: FileItem) => {
    const formRef = useRef<FormInstance>(null);
    
    showModal({
      title: '编辑配置文件',
      content: <EditConfigFileModal ref={formRef} data={file} />,
      width: '800px',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: () => void) => {
            cb();
          },
        },
        {
          text: '保存',
          type: 'primaryBtn',
          onClick: async (cb: () => void) => {
            try {
              const values = await formRef.current?.validateFields();
              if (values) {
                // 更新文件内容
                fileDataRef.current = {
                  ...fileDataRef.current,
                  [file.blockNo]: {
                    ...fileDataRef.current[file.blockNo],
                    [file.fileNo]: values.content,
                  },
                };
                message.success('保存成功');
                cb();
              }
            } catch (error) {
              message.error('请完善配置内容');
            }
          },
        },
      ],
    });
  };

  // 渲染配置文件表格
  const renderFileTable = (fileList: FileItem[]) => {
    const columns = [
      {
        title: '序号',
        key: 'index',
        width: 80,
        render: (_: any, __: any, index: number) => index + 1,
      },
      {
        title: '模块',
        dataIndex: 'blockName',
        key: 'blockName',
      },
      {
        title: '配置文件名称',
        dataIndex: 'fileName',
        key: 'fileName',
      },
      {
        title: '操作',
        key: 'action',
        render: (record: FileItem) => (
          <Space size="middle">
            <Button type="link" onClick={() => handleEditFile(record)}>
              编辑
            </Button>
          </Space>
        ),
      },
    ];

    return (
      <Table
        columns={columns}
        dataSource={fileList}
        rowKey="fileNo"
        pagination={false}
        size="small"
      />
    );
  };

  return (
    <div className="edit-configuration">
      <Card title="基本信息" className="basic-info-card">
        <Form form={form} layout="vertical">
          <Form.Item
            label="模板名称"
            name="templateName"
            rules={[
              { required: true, message: '请输入模板名称' },
              { max: 50, message: '模板名称不能超过50个字符' },
              { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/, message: '只支持中文、字母、数字、中划线、下划线' },
            ]}
          >
            <Input placeholder="请输入模板名称" maxLength={50} showCount />
          </Form.Item>
          
          <Form.Item label="产品" name="productKey">
            <Select
              disabled
              options={productOptions}
              placeholder="请选择产品"
            />
          </Form.Item>
          
          <Form.Item
            label="型号"
            name="productModelNoList"
            rules={[{ required: true, message: '请选择型号' }]}
          >
            <Select
              mode="multiple"
              options={modelOptions}
              placeholder="请选择型号"
              allowClear
            />
          </Form.Item>
          
          <Form.Item
            label="备注"
            name="remark"
            rules={[{ max: 150, message: '备注不能超过150个字符' }]}
          >
            <TextArea
              placeholder="请输入备注"
              maxLength={150}
              showCount
              autoSize={{ minRows: 3, maxRows: 5 }}
            />
          </Form.Item>
        </Form>
      </Card>

      <Card title="配置编辑" className="config-edit-card">
        {configData.length > 0 ? (
          <Tabs
            activeKey={activeBlockNo}
            onChange={setActiveBlockNo}
            items={configData.map((block) => ({
              key: block.blockNo,
              label: block.blockName,
              children: (
                <div className="block-content">
                  {/* 结构化配置项 */}
                  {block.itemsTypeList && block.itemsTypeList.length > 0 && (
                    <div className="structured-config-section">
                      <h4>结构化配置项</h4>
                      <StructuredConfigEditor
                        configTypes={block.itemsTypeList}
                        blockNo={block.blockNo}
                        onDataChange={handleConfigDataChange}
                      />
                    </div>
                  )}
                  
                  {/* 配置文件 */}
                  {block.fileList && block.fileList.length > 0 && (
                    <div className="config-file-section">
                      <h4>配置文件</h4>
                      {renderFileTable(block.fileList)}
                    </div>
                  )}
                  
                  {/* 如果没有配置项和文件 */}
                  {(!block.itemsTypeList || block.itemsTypeList.length === 0) &&
                   (!block.fileList || block.fileList.length === 0) && (
                    <div className="empty-content">
                      该模块下暂无选中的配置项和配置文件
                    </div>
                  )}
                </div>
              ),
            }))}
          />
        ) : (
          <div className="no-data">暂无配置数据</div>
        )}
      </Card>
    </div>
  );
});

EditConfiguration.displayName = 'EditConfiguration';

export default EditConfiguration;
