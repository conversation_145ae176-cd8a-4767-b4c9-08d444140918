import React, { useEffect, useRef, useState } from 'react';
import FieldItem from '@/components/FieldItem';
import './index.scss';
import Device from '@/fetch/bussiness/device';
import { HttpStatusCode } from '@/fetch/core/constant';
import { Input, Table, message } from 'antd';
import CommonForm from '@/components/CommonForm';
import { DeviceListColumns, GroupDetailSearchForm } from '../utils/columns';
import { FormConfig } from '@/components/CommonForm/formConfig';
import CommonTable from '@/components/CommonTable';
import CreateTag, { TagItem } from '@/components/TagLabel/CreateTag';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useTableData } from '@/components/CommonTable/useTableData';
import showModal from '@/components/commonModal';
import { validateTagList } from '@/components/TagLabel';
const fetchApi = new Device();
const Detail = (props: {
  groupNo: string[];
  goBack: AnyFunc;
  prodKey?: string;
}) => {
  const formRef = useRef<any>(null);
  const tagModalRef = useRef<any>(null);
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );
  const [groupDetail, setGroupDetail] = useState<any>(null);
  const [groupSearchForm, setGroupSearchForm] = useState<FormConfig>(
    GroupDetailSearchForm,
  );
  const [edit, setEdit] = useState(false);
  const [tagList, setTagList] = useState<any>([]);
  const [descValue, setDescValue] = useState<string>('');
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const initSearchCondition = {
    productKey: null,
    productModelNo: null,
    deviceName: null,
    groupNoList: null,
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValue.searchValues
      ? historySearchValue.searchValues
      : initSearchCondition;
  });
  const { tableData, loading, reloadTable } = useTableData(
    { ...searchCondition, groupNoList: props.groupNo },
    fetchApi.queryDeviceList,
    'init',
    autoFetch,
  );

  const getDetail = () => {
    fetchApi.getDetailByGroupNo(props.groupNo[0]).then((res) => {
      if (res.code === HttpStatusCode.Success) {
        const prodKey =
          props.groupNo && props.groupNo[0] === 'NULL'
            ? props.prodKey
            : res?.data?.productKey;
        setGroupDetail({
          ...res?.data,
          productKey: prodKey,
        });
        setDescValue(res?.data?.description);
        setSearchCondition({
          ...searchCondition,
          groupNoList: props.groupNo,
          productKey: prodKey,
        });
        prodKey && getModalList(prodKey);
        setTagList(
          res?.data?.tagDTOList?.map((item: any) => ({
            name: item.key,
            value: item.value,
          })),
        );
        setAutoFetch(true);
      }
    });
  };
  const getModalList = (productKey: string) => {
    if (!productKey) {
      message.warning('请先选择产品');
      return;
    }
    fetchApi.queryModelList(productKey).then((res: any) => {
      if (res?.code === HttpStatusCode.Success) {
        const data = res?.data?.map(
          (item: {
            modelName: string;
            modelNo: string;
            productKey: string;
          }) => ({
            label: item.modelName,
            value: item.modelNo,
          }),
        );
        const modelNo = groupSearchForm.fields.find(
          (field: any) => field.fieldName === 'productModelNo',
        );
        modelNo!.options = data;
        setGroupSearchForm({
          ...groupSearchForm,
        });
      }
    });
  };

  const formatColumns = () => {
    return DeviceListColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) => {
            const { pageSize, pageNum } = searchCondition;
            return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
          };
          break;
        default:
          break;
      }
      return col;
    });
  };

  const showAddTagModal = () => {
    showModal({
      title: '编辑分组标签',
      width: '600px',
      content: <CreateTag tagList={tagList} ref={tagModalRef} />,
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: (cb: any) => {
            const tagList = tagModalRef.current.getTagList();
            const flag = validateTagList(tagList);
            if (!tagList || !flag) {
              return;
            }
            fetchApi
              .updateTagList({
                dataNo: groupDetail?.groupNo,
                tagType: 3,
                tagInfoList: tagList?.map((item: TagItem) => ({
                  tagKey: item.name,
                  tagValue: item.value,
                })),
              })
              .then((res: HTTPResponse) => {
                if (res?.code === HttpStatusCode.Success) {
                  setTagList([...tagList]);
                  message.success('编辑成功');
                } else {
                  res?.message && message.error(res?.message);
                }
              })
              .finally(() => {
                cb();
              });
          },
        },
      ],
    });
  };

  useEffect(() => {
    getDetail();
  }, []);

  return (
    <div className="detail">
      <div className="detail-group-info">
        <div className="title">
          分组信息{' '}
          <i
            className="goback"
            onClick={() => {
              props.goBack([]);
            }}
          ></i>
        </div>
        <div className="group-info">
          <FieldItem label="分组名称" text={groupDetail?.groupName} />
          <FieldItem label="分组路径" text={groupDetail?.levelName} />
          <FieldItem label="分组ID" text={groupDetail?.groupNo} />
        </div>
        <div className="group-create-time">
          <FieldItem label="创建时间" text={groupDetail?.createTime} />
        </div>
        <div className="group-tag-list">
          <i className="tag-item-label">分组标签：</i>
          {tagList?.map((item: any) => {
            return (
              <i className="group-tag-item" key={`${item.name + item.value}`}>
                {item.name}/{item.value}
              </i>
            );
          })}
          <a onClick={showAddTagModal}>编辑</a>
        </div>
        <div className="group-desc">
          <i className="field-item__label">分组描述</i>
          <Input.TextArea
            value={descValue}
            disabled={!edit}
            onChange={(e) => {
              setDescValue(e.target.value);
            }}
          />
          {edit ? (
            <a
              onClick={() => {
                fetchApi
                  .updateGroupInfo({
                    productKey: groupDetail?.productKey,
                    groupNo: groupDetail?.groupNo,
                    description: descValue,
                  })
                  .then((res: any) => {
                    if (res?.code === HttpStatusCode.Success) {
                      message.success('编辑成功');
                    } else {
                      message.error(res?.message);
                    }
                  })
                  .finally(() => {
                    setEdit(false);
                  });
              }}
            >
              保存
            </a>
          ) : (
            <a
              onClick={() => {
                setEdit(true);
              }}
            >
              编辑
            </a>
          )}
        </div>
      </div>
      <div className="device-list">
        <div className="title">设备列表</div>
        <CommonForm
          formConfig={groupSearchForm}
          formType="search"
          onSearchClick={() => {
            const values = formRef.current?.getFieldsValue();
            setSearchCondition({
              ...searchCondition,
              ...values,
              productKey: values?.productKey,
              productModelNo: values?.productModelNo,
            });
          }}
          getFormInstance={(form: any) => {
            formRef.current = form;
          }}
          onResetClick={() => {
            formRef.current?.resetFields();
            setSearchCondition({
              ...initSearchCondition,
              groupNoList: props.groupNo,
              productKey: groupDetail?.productKey,
            });
          }}
          onValueChange={(formValues: any, changedFieldName: string) => {
            if (changedFieldName === 'productKey') {
              getModalList(formValues?.productKey);
            }
          }}
        />
        <CommonTable
          searchCondition={searchCondition}
          loading={false}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          columns={formatColumns()}
          rowKey={'deviceName'}
          onPageChange={(paginationData: any) => {
            setSearchCondition({
              ...searchCondition,
              pageNum: paginationData.pageNum,
              pageSize: paginationData.pageSize,
            });
          }}
        ></CommonTable>
      </div>
    </div>
  );
};

export default Detail;
