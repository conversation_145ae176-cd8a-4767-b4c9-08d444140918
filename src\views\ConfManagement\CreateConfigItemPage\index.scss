.create-config-item-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .page-header {
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fafafa;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }

    .form-container {
      padding: 24px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .create-config-item-page {
    padding: 10px;

    .page-content {
      .page-header {
        padding: 16px 20px;

        h2 {
          font-size: 16px;
        }
      }

      .form-container {
        padding: 20px;
      }
    }
  }
}
