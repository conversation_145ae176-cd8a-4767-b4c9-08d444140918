import React, { useEffect, useState, useRef } from 'react';
import {
  DatePicker,
  Select,
  Form,
  Cascader,
  Input,
  Radio,
  Checkbox,
  Switch,
  Row,
  Col,
  Grid,
  Button,
  InputNumber,
  TimePicker,
} from 'antd';
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import { cloneDeep } from 'lodash';
import { filterOption } from './util';
import { FormConfig, FieldItem } from './formConfig';
import { CommonApi } from '@/fetch/bussiness/commonFetch';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
import { actionSaveStationInfo } from '@/redux/reducers/stationInfo';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
} from '@/utils/emit';
import dayjs from 'dayjs';
const { RangePicker } = DatePicker;
export interface FormProps {
  name?: string;
  formConfig: FormConfig;
  layout?: 'horizontal' | 'vertical' | 'inline';
  defaultValue?: {
    [key in string]: any;
  };
  formType?: 'search' | 'edit'; // search表示是搜索表单会有查询重置按钮
  colon?: boolean;
  className?: string;
  labelAlign?: 'left' | 'right' | undefined;
  updateForm?: any;
  onValueChange?: Function;
  getFormInstance?: Function;
  onResetClick?: Function;
  onSearchClick?: Function;
  onFieldFocus?: Function;
}
const CommonForm = (props: FormProps) => {
  const {
    name,
    formConfig,
    layout = 'horizontal',
    formType = 'edit',
    labelAlign,
    className,
    colon,
    defaultValue = {},
    onValueChange,
    getFormInstance,
    onResetClick,
    onSearchClick,
    onFieldFocus,
  } = props;
  const cloneFormConfig = cloneDeep(formConfig);
  const [formData, setFormData] = useState<any>(null);
  const formDataRef = useRef<any>(null);
  const [commonFormRef] = Form.useForm();
  const commonFetch = new CommonApi();
  const [selectList, setSelectList] = useState<any>({});
  const showCheckedStrategyMap = new Map([
    ['SHOW_PARENT', Cascader.SHOW_PARENT],
    ['SHOW_CHILD', Cascader.SHOW_CHILD],
  ]);

  useEffect(() => {
    setFormData(cloneFormConfig);
    formDataRef.current = cloneFormConfig;
    const cb = (data: { name: string; config: FormConfig }) => {
      if (data.name === props.name) {
        setFormData({ ...data.config });
        formDataRef.current = { ...data.config };
      }
    };
    const updateOneConfig = (data: {
      name: string;
      fieldName: string;
      config: any;
    }) => {
      if (data.name === props.name) {
        formDataRef.current = {
          ...formDataRef.current,
          fields: formDataRef.current.fields?.map((v: any) => {
            if (v.fieldName === data.fieldName) {
              return data.config;
            } else {
              return v;
            }
          }),
        };
        setFormData({ ...formDataRef.current });
      }
    };
    addGlobalEventListener('FORCE_UPDATE_CONFIG', cb);
    addGlobalEventListener('FORCE_UPDATE_ONE_CONFIG', updateOneConfig);
    return () => {
      removeGlobalEventListener('FORCE_UPDATE_CONFIG', cb);
      removeGlobalEventListener('FORCE_UPDATE_ONE_CONFIG', updateOneConfig);
    };
  }, [JSON.stringify(cloneFormConfig)]);

  useEffect(() => {
    commonFormRef.setFieldsValue(defaultValue);
    defaultValue &&
      Object.keys(defaultValue).forEach((v) => {
        onFieldsChange(v, defaultValue[v], 'init');
      });
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    getFormInstance && getFormInstance(commonFormRef);
  }, []);

  const getDropDownList = async (item: any) => {
    if (selectList[item.fieldName]) {
      return;
    }
    if (item.dropDownKey) {
      const res = await commonFetch.getCommonDropDown({
        keyList: [item.dropDownKey],
      });
      if (res && res.code === HttpStatusCode.Success) {
        setSelectList({
          ...selectList,
          [item.fieldName]: res.data[item.dropDownListKey].map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          }),
        });
      }
    }
  };

  const getDepartmentList = async (item: any, type: string) => {
    if (selectList[item.fieldName]) {
      return;
    }
    let res;
    if (type === 'station') {
      res = await commonFetch.getStationDepartment(item.departmentParams ?? {});
    } else if (type === 'city') {
      res = await commonFetch.getCityDepartment(item.departmentParams ?? {});
    }
    if (res.code === HttpStatusCode.Success) {
      setSelectList({
        ...selectList,
        [item.fieldName]: res.data,
      });
    }
  };

  const renderFieldItem = (dataItem: FieldItem) => {
    const {
      fieldName,
      type,
      options,
      placeholder,
      multiple,
      showSearch,
      maxLength,
      allowClear,
      disabled,
      showSelectAll,
      autoSize,
      showCount,
      maxTagCount,
      defaultChecked,
      mapRelation,
      specialFetch,
      renderFunc,
      changeOnSelect,
      showCheckedStrategy,
      labelInValue = true,
      showNow = false,
      disabledDate,
      disabledTime,
    } = dataItem;
    switch (type) {
      case 'input':
        return (
          <Input
            placeholder={placeholder ?? ''}
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            maxLength={maxLength}
            disabled={disabled}
          />
        );
      case 'inputNumber':
        return (
          <InputNumber
            placeholder={placeholder ?? ''}
            type="number"
            controls={false}
            maxLength={maxLength}
            disabled={disabled}
          />
        );
      case 'radioGroup':
        return <Radio.Group options={options} disabled={disabled} />;
      case 'textarea':
        return (
          <Input.TextArea
            placeholder={placeholder ?? ''}
            autoSize={autoSize === true ? { minRows: 2, maxRows: 6 } : autoSize}
            maxLength={maxLength}
            disabled={disabled}
            showCount={
              showCount && {
                formatter: ({ value, count, maxLength }) =>
                  `${count}/${maxLength}`,
              }
            }
          />
        );
      case 'select':
        let _selectOptions: any[] = [];
        if (selectList[fieldName!]) {
          _selectOptions = selectList[fieldName!];
        } else if (options) {
          _selectOptions = options;
        }
        return (
          <Select
            placeholder={placeholder ?? ''}
            options={_selectOptions}
            showSearch={showSearch}
            labelInValue={labelInValue}
            disabled={disabled}
            mode={multiple ? 'multiple' : undefined}
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            filterOption={showSearch && filterOption}
            onFocus={() => {
              specialFetch === 'commonDown' && getDropDownList(dataItem);
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue(),
                );
            }}
          />
        );
      case 'rangeTime':
        return (
          <RangePicker
            locale={locale}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            disabled={disabled}
            showNow={showNow}
            // disabledDate={disabledDate}
          />
        );
      case 'timePicker':
        return (
          <TimePicker
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            showNow={showNow}
            needConfirm={false}
          />
        );
      case 'datePicker':
        return (
          <DatePicker
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            locale={locale}
            format="YYYY-MM-DD"
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
          />
        );
      case 'dateTime':
        return (
          <DatePicker
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            locale={locale}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
          />
        );
      case 'cascader':
        let _cascaderOptions: any[] = [];
        if (selectList[fieldName!]) {
          _cascaderOptions = selectList[fieldName!];
        } else if (options) {
          _cascaderOptions = options;
        }
        return (
          <Cascader
            fieldNames={mapRelation}
            options={_cascaderOptions}
            placeholder={placeholder ?? ''}
            maxTagCount={maxTagCount}
            showSearch={typeof showSearch === 'undefined' ? true : showSearch}
            disabled={disabled}
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            multiple={multiple}
            showCheckedStrategy={
              typeof showCheckedStrategy == 'undefined'
                ? Cascader.SHOW_CHILD
                : showCheckedStrategyMap.get(showCheckedStrategy)
            }
            changeOnSelect={
              typeof changeOnSelect === 'undefined' ? true : changeOnSelect
            }
            onDropdownVisibleChange={(value) => {
              if (
                value &&
                specialFetch &&
                ['city', 'station'].includes(specialFetch!)
              ) {
                getDepartmentList(dataItem, specialFetch);
              }
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue(),
                );
            }}
          />
        );
      case 'switch':
        return <Switch defaultChecked={defaultChecked} />;
      case 'checkboxGroup':
        return <Checkbox.Group options={options} disabled={disabled} />;
      case 'ReactNode':
        return <>{renderFunc && renderFunc()}</>;
      default:
        return null;
    }
  };

  const onFieldsChange = (
    changedFieldName: any,
    val: any,
    type: 'init' | 'operate',
  ) => {
    if (!formDataRef.current?.linkRules) {
      return;
    }
    // 找到当前元素改变后需要的联动的表单项
    const hasLinkRule = Object.keys(formDataRef.current?.linkRules).includes(
      changedFieldName,
    );
    if (!hasLinkRule) {
      return;
    }
    formDataRef.current?.linkRules[changedFieldName]?.forEach((item: any) => {
      const {
        linkFieldName,
        rule,
        dependenceData,
        disabledValue,
        fetchFunc,
        refreshFunc,
      } = item;
      let needChangeField: FieldItem = formDataRef.current.fields?.find(
        (f: FieldItem) => f.fieldName === linkFieldName,
      );
      switch (rule) {
        case 'visible':
          needChangeField.hidden = !dependenceData.includes(val);
          break;
        case 'refresh':
          if (refreshFunc) {
            const data = refreshFunc(val, needChangeField);
            needChangeField.options = data;
          }
          commonFormRef.setFieldsValue({
            [linkFieldName]: defaultValue[linkFieldName],
          });
          break;
        case 'clear':
          if (type === 'operate') {
            commonFormRef.setFieldsValue({
              [linkFieldName]: null,
            });
          }
          break;
        case 'fetchData':
          fetchFunc(val, commonFormRef).then((res: any[]) => {
            setSelectList((selectList: any) => ({
              ...selectList,
              [linkFieldName]: res,
            }));
          });
          break;
        case 'valueDisable':
          if (needChangeField.options) {
            needChangeField.options = needChangeField.options.map((v: any) => {
              if (
                disabledValue.includes(v.value) &&
                dependenceData.includes(val)
              ) {
                return {
                  ...v,
                  disabled: true,
                };
              } else {
                return { ...v, disabled: false };
              }
            });
          }
          break;
        case 'fieldItemDisable':
          needChangeField.disabled = dependenceData.includes(val);
          break;
      }
    });
    // setTimeout(() => {
    setFormData({ ...formDataRef.current });
    // }, 100);
  };

  const makeLayout = () => {
    let titleMaxLength = 4;
    formData?.fields.forEach((item: any) => {
      if (item.label?.length && item.label?.length > titleMaxLength) {
        titleMaxLength = item.label.length || item.label.length;
      }
    });
    titleMaxLength = titleMaxLength >= 9 ? 9 : titleMaxLength;

    return {
      labelCol: { span: titleMaxLength + 2 },
      wrapperCol:
        layout === 'inline'
          ? { span: 24 - (titleMaxLength + 2) }
          : { span: 23 - (titleMaxLength + 1) },
    };
  };

  return (
    <div
      className={
        formType === 'search' ? 'searchform-conatiner' : 'common-form-container'
      }
    >
      <Form
        {...makeLayout()}
        initialValues={defaultValue}
        name={name || 'complex-form'}
        labelAlign={labelAlign ?? 'right'}
        layout={layout}
        colon={colon ?? true}
        form={commonFormRef}
        autoComplete="off"
        className={className || ''}
        style={{
          alignItems: 'center',
        }}
        onValuesChange={(changedValues, allValues) => {
          const changedFieldName = Object.keys(changedValues)[0];
          const changedVal = changedValues[changedFieldName];
          onValueChange &&
            onValueChange(commonFormRef.getFieldsValue(), changedFieldName);
          changedFieldName &&
            onFieldsChange(changedFieldName, changedVal, 'operate');
        }}
      >
        <Row gutter={24} align="middle" style={{ width: '100%' }}>
          {formData?.fields?.map((item: FieldItem, index: number) => {
            if (item.childrenList) {
              const cWidth = Math.floor(100 / item.childrenList.length);
              return (
                !item.hidden && (
                  <Col
                    xxl={layout === 'inline' ? (item.xxl ? item.xxl : 4) : 23}
                    xl={layout === 'inline' ? (item.xl ? item.xl : 6) : 23}
                    lg={layout === 'inline' ? (item.lg ? item.lg : 8) : 23}
                    md={item.md ? item.md : 23}
                    key={index}
                  >
                    <Form.Item
                      label={item.label}
                      style={{ marginBottom: 0 }}
                      labelCol={item.labelCol && item.labelCol}
                      wrapperCol={item.wrapperCol && item.wrapperCol}
                      tooltip={item.tooltip && item.tooltip}
                    >
                      {item.childrenList.map((name: string, index: number) => {
                        const _cField = formData?.fields.find(
                          (v: FieldItem) => v.fieldName === name,
                        );
                        const _marginLeft =
                          _cField?.marginLeft || _cField?.marginLeft === 0
                            ? _cField.marginLeft
                            : 8;
                        const _marginRight =
                          _cField?.marginRight || _cField?.marginRight === 0
                            ? _cField.marginRight
                            : 8;
                        if (_cField.hidden) {
                          return '';
                        }
                        return (
                          <Form.Item
                            labelCol={_cField?.labelCol && _cField.labelCol}
                            wrapperCol={
                              _cField?.wrapperCol && _cField.wrapperCol
                            }
                            name={_cField?.fieldName}
                            label={_cField?.label}
                            help={_cField?.help}
                            rules={_cField?.validatorRules ?? []}
                            className={
                              'field_' + _cField?.fieldName?.toString()
                            }
                            style={{
                              display: 'inline-block',
                              width:
                                _cField?.width ??
                                `calc(${cWidth}% - ${_marginLeft}px - ${_marginRight}px)`,
                              marginLeft: `${_marginLeft}px`,
                              marginRight: `${_marginRight}px`,
                            }}
                          >
                            {renderFieldItem(_cField)}
                          </Form.Item>
                        );
                      })}
                    </Form.Item>
                  </Col>
                )
              );
            } else if (!item.isChild) {
              return (
                !item.hidden && (
                  <Col
                    xxl={layout === 'inline' ? (item.xxl ? item.xxl : 4) : 23}
                    xl={layout === 'inline' ? (item.xl ? item.xl : 6) : 23}
                    lg={layout === 'inline' ? (item.lg ? item.lg : 8) : 23}
                    md={item.md ? item.md : 23}
                    key={item.fieldName?.toString()}
                  >
                    <Form.Item
                      labelCol={item.labelCol && item.labelCol}
                      wrapperCol={item.wrapperCol && item.wrapperCol}
                      name={item.fieldName}
                      label={item.label}
                      help={item.help}
                      rules={item.validatorRules ?? []}
                      className={'field_' + item.fieldName?.toString()}
                      tooltip={item.tooltip && item.tooltip}
                    >
                      {renderFieldItem(item)}
                    </Form.Item>
                  </Col>
                )
              );
            }
          })}
        </Row>
      </Form>
      {formType === 'search' && (
        <Row justify={'end'} className="searchbtns">
          <Button
            className="reset"
            style={{ marginRight: '12px' }}
            onClick={() => {
              commonFormRef.setFieldsValue(defaultValue);
              Object.keys(defaultValue).forEach((v) => {
                onFieldsChange(v, defaultValue[v], 'operate');
              });
              onResetClick && onResetClick();
            }}
          >
            重置
          </Button>

          <Button
            type="primary"
            onClick={() => {
              onSearchClick && onSearchClick(commonFormRef.getFieldsValue());
            }}
          >
            查询
          </Button>
        </Row>
      )}
    </div>
  );
};

export default React.memo(CommonForm);
