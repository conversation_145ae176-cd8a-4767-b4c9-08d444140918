export const api = {
  // 公共接口
  getParentLinked: `/ota/web/common_get_parent_linked`, // 联动获取父辈节点
  getDepartmentCurrentDownList: `/ota/web/common_get_current_down_list`, // 联动获取当前下拉框
  getDropDownList: `/ota/web/common_get_down_list`, // 获取下拉列表数据
  getVehicleTypeNameList: `/ota/web/get_vehicle_type_name_list`, // 获取车型名称下拉列表数据
  getVehicleConfTemplateDropDownList: `/ota/web/vehicle_conf_template_get_list`, // 获取车辆配置模板下拉列表数据
  getApplicationInfoList: `/ota/web/get_application_info_list`, // 获取发布模块下拉列表数据

  // 配置模板管理
  getConfList: `/ota/web/conf_template_list`, // 获取配置文件列表
  getConfTemplateDetail: `/ota/web/conf_template_detail`, // 配置模板详情
  addConfTemplate: `/ota/web/add_conf_template`, // 新建配置模板
  getConfTemplatePageList: `/ota/web/conf_template_page_list`, // 配置文件分页列表查询
  ChangeConfTemplateState: `/ota/web/change_conf_template_state`, // 配置模板失效/生效
  editConfTemplate: `/ota/web/edit_conf_template`, // 编辑配置模板
  isExistSameNameConfTemplate: `/ota/web/exist_same_name_conf_template`, // 是否其他位置存在相同文件名
  deleteConfTemplate: `/ota/web/delete_conf_template`, // 删除配置模板

  // 车型配置管理
  getVehicleTypeTableList: `/ota/web/vehicle_type_conf_info_page_list`, // 车型配置分页列表查询
  getSelectedVehicleTypeConf: `/ota/web/vehicle_type_conf_info_added`, // 查看当前车型已添加配置文件
  deleteVehicleTypeConf: `/ota/web/delete_vehicle_type_conf_info`, // 删除车型配置文件
  addVehicleTypeConf: `/ota/web/add_vehicle_type_conf_info`, // 添加车型配置文件
  getVehicleTypeConfDetail: `/ota/web/vehicle_type_conf_info_detail`, // 已添加车型配置文件详情查看接口
  editVehicleTypeConf: `/ota/web/edit_vehicle_type_conf_info`, // 编辑车型配置文件提交接口
  decideSameVehicleTypeConf: `/ota/web/exist_same_name_vehicle_type_conf_info`, // 同一车型下是否存在名称相同的配置文件
  getVehicleTypeRecord: `/ota/web/vehicle_type_record_list`, // 车型操作记录列表查询(现有和历史)
  getVehicleTypeConfInfoRecord: `/ota/web/conf_info_record_page_list`, // 配置文件操作记录列表查询
  getDifferentVersionConfInfo: `/ota/web/different_version_conf_info_content`, // 获取不同版本号配置文件内容

  // 车辆配置模板管理接口
  getVehicleConfTableList: `/ota/web/vehicle_conf_template_page_list`, // 车辆配置模板分页列表查询
  delVehicleConf: `/ota/web/delete_vehicle_conf_template`, // 删除车辆配置模板
  getVehicleConfDetail: `/ota/web/get_vehicle_conf_template_detail`, // 获取车辆配置模板详情
  getVehicleConfContent: `/ota/web/get_vehicle_conf_template_file_content`, // 查看车辆配置模板中文件具体内容
  addVehicleConf: `/ota/web/add_vehicle_conf_template`, // 新建/编辑 车辆配置文件
  delVehicleConfFile: `/ota/web/delete_vehicle_conf_template_file`, // 删除车辆配置模板中文件
  getVehicleTypeList: `/ota/web/get_vehicle_type_list`, // 获取车型名称列表
  getAddedVehicleConf: `/ota/web/get_added_vehicle_conf_template_info`, // 获取已添加的配置文件

  // 车辆配置与发布
  getVehicleConfIssuePageList: `/ota/web/vehicle_conf_issue_get_page_list`, // 车辆配置及发布分页列表接口
  getVehicleConfigDetail: `/ota/web/vehicle_conf_get_list`, // 单车车辆配置接口
  getVehicleConfTemplateList: `/ota/web/vehicle_conf_template_get_list`, // 车辆配置模板列表方法
  submitVehicleConf: `/ota/web/add_vehicle_conf`, // 车辆配置确定接口
  saveAsTemplte: `/ota/web/vehicle_conf_save_as_template`, // 车辆配置另存为模板接口
  getApplicationList: `/ota/web/application_get_list`, // 获取应用(模块)列表
  getApplicationVersionList: `/ota/web/application_version_get_list`, // 获取应用的版本列表
  getApplicationVersionInfoList: `/ota/web/application_version_info_get_page_list`, // 获取应用版本分页列表
  getNotApplicableVehicleNameList: `/ota/web/get_not_applicable_vehicle_name_list`, // 获取不满足条件的车辆列表
  createIssueTask: `/ota/web/create_issue_task`, // 创建发布计划接口
  getIssueTaskHistoryList: `/ota/web/issue_task_history_of_vehicle_get_page_list`, // 获取车辆发布记录分页列表接口
  getVehicleConfRecordList: `/ota/web/vehicle_conf_record_list`, // 获取 现有&已删除 的车辆配置列表接口
  getVehicleConfHistoryList: `/ota/web/vehicle_conf_get_history_page_list`, // 查看车辆某配置文件的历史版本分页列表接口
  getVehicleConfHistoryVersionList: `/ota/web/vehicle_conf_get_history_version_list`, // 查看车辆某配置文件的历史版本列表
  getDifferentVehicleConfContent: `/ota/web/get_different_version_of_vehicle_conf_content`, // 获取车辆某配置文件两个版本的配置内容
  downloadIssueModule: `/ota/web/download_issue_module`, // 车辆发布记录模块下载
  getIssueListIncludeConfOfDevice:
    '/ota/web/issue/get_issue_list_include_conf_of_device', //获取包含车辆conf发布的发布计划列表
  getIssueConfCompare: '/ota/web/issue/get_issue_conf_compare', // 获取两次发布计划中conf的内容对应

  // 发布计划管理
  getIssueTaskList: `/ota/web/issue_task_info_get_page_list`, // 获取发布计划分页列表接口
  getIssueTaskDetail: `/ota/web/issue_task_info_get_detail`, // 查看发布计划详情接口
  getIssueTaskResultList: `/ota/web/issue_task_vehicle_result_info_get_page_list`, // 查看发布计划升级结果列表接口
  obsoleteIssueTask: `/ota/web/issue_task_obsolete`, // 发布计划作废接口

  // 车辆基础信息管理
  getHardwareSerialNumberPageList: `/ota/web/hardware_serial_number_page_list`, // 硬件序列号列表查询
  getHardwareSerialNumberDetail: `/ota/web/get_hardware_serial_number_detail`, // 硬件序列号详情查询
};
