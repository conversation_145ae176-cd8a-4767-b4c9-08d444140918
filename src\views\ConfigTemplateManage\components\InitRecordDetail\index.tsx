import React, { useState, useMemo } from 'react';
import { CommonTable, useTableData } from '@jd/x-coreui';
import { InitRecordDetailColumns } from '../../utils/columns';
import { ConfigTemplateApi } from '@/fetch/bussiness';

const configManageApi = new ConfigTemplateApi();

const InitRecordDetail = (props: any) => {
  const { recordId } = props;
  const initSearchCondition = {
    recordId,
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState(initSearchCondition);
  const { tableData } = useTableData(
    searchCondition,
    configManageApi.getConfigTemplateInitDeviceDetailPage,
  );

  const formatColumns = useMemo(() => {
    return InitRecordDetailColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  }, [searchCondition.pageNum, searchCondition.pageSize]);

  return (
    <div>
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formatColumns}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </div>
  );
};

export default InitRecordDetail;
