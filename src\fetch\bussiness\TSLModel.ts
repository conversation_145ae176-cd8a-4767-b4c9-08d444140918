import { request } from '@/fetch/core';
import { HttpStatusCode } from '../core/constant';
class TSLModelFetch {
  getThingModelDetails = (params: { productKey?: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/getProductThingModelDetails',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getThingModelBlocks = (params: {
    productKey: string;
    version?: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/getThingModelBlocks',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getThingModel = (params: {
    productKey: string;
    version?: string;
    blockNo: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/getThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  createBlock = (params: {
    productKey: string;
    blockNo: string;
    blockName: string;
    description: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/createThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  delBlockOrTSL = (params: {
    productKey: string;
    blockNo: string;
    propertyIdentifiers?: string[];
    serviceIdentifiers?: string[];
    eventIdentifiers?: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/deleteThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  getReleaseVersion = (params: { productKey: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/getReleaseVersion',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  createTSLInfo = (params: {
    productKey: string;
    blockNo: string;
    blockName?: string;
    description?: string;
    content?: string;
    identifier?: string | null; // 没有表示新增，有表示编辑
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/upsertThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getTSLInfo = (params: {
    productKey: string;
    blockNo: string;
    version: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/getThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  // 恢复历史版本和快读导入拷贝产品使用
  copyThingModel = (params: {
    sourceProductKey: string;
    targetProductKey: string;
    sourceVersion: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/copyThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  importThingModel = (params: {
    productKey: string;
    bucketName: string;
    fileKey: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/importThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  publishThingModel = (params: {
    productKey: string;
    version: string;
    description?: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/publishThingModel',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getProductList = async (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/product/query_list',
      newGeteway: true,
    };
    const res: any = await request(requestOptions);
    if (res.code === HttpStatusCode.Success && res.data?.length > 0) {
      return res.data.map((v: any) => {
        return {
          value: v.productKey,
          label: v.productName,
        };
      });
    } else {
      return [];
    }
  };

  getFunctionInfo = (params: {
    productKey: string;
    blockNo: string;
    identifier: string;
    version?: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/thingModel/getThingModelAbility',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
}

export default TSLModelFetch;
