import React, { useRef, useState } from 'react';
import { message } from 'antd';
import './index.scss';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import CommonSteps from '@/components/CommonSteps';
import BreadCrumb from '@/components/BreadCrumb';
import Configuration, { SelectedData } from '../components/Configuration';
import EditConfiguration from '../components/EditConfiguration';
import ResultFeedback, { ButtonConfig } from '@/components/ResultFeedback';

// 移除重复的接口定义，使用从Configuration组件导入的SelectedData

const AddConfigTemplate: React.FC = () => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const configRef = useRef<{
    checkAndGetSelectedData: () => Promise<SelectedData>;
  }>();
  const editConfigRef = useRef<any>(null);

  const [selectedConfig, setSelectedConfig] = useState<SelectedData>({
    product: {},
    blockData: [],
  });
  const [isSubmitSuccess, setIsSubmitSuccess] = useState<boolean>(false);

  // 第一步：选择配置项和配置文件
  const checkStepOne = async () => {
    try {
      const selectedData: any =
        await configRef.current?.checkAndGetSelectedData();
      console.log('选中的数据：', selectedData);
      setSelectedConfig(selectedData);
      return true;
    } catch (error: any) {
      message.error(error.message);
      return false;
    }
  };

  // 提交创建配置模板
  const handleSubmit = async () => {
    try {
      const templateData = await editConfigRef.current?.getTemplateData();
      const res = await configTemplateApi.current.addConfigTemplate(
        templateData,
      );
      if (res.code === HttpStatusCode.Success) {
        setIsSubmitSuccess(true);
        message.success('配置模板创建成功');
        return true;
      } else {
        setIsSubmitSuccess(false);
        message.error(res.message || '创建失败');
        return false;
      }
    } catch (error: any) {
      setIsSubmitSuccess(false);
      message.error(error.message || '创建失败');
      return false;
    }
  };

  const resultButton: ButtonConfig[] = [
    {
      text: '返回列表',
      clickFunc: () => {
        window.location.href = '/ota/configTemplate';
      },
      showScope: ['success', 'fail'],
    },
    {
      text: '继续创建',
      clickFunc: () => {
        window.location.reload();
      },
      showScope: ['success', 'fail'],
    },
  ];

  return (
    <>
      <BreadCrumb
        items={[
          { title: '通用设备管理', route: '' },
          { title: '配置模板管理', route: '/ota/configTemplate' },
          { title: '创建配置模板', route: '' },
        ]}
      />
      <div className="add-config-template-content">
        <CommonSteps
          stepTipList={[
            '分模块选择配置项和配置文件',
            '分模块编辑配置模板',
            '完成创建',
          ]}
          children={[
            <Configuration onRef={(ref) => (configRef.current = ref)} />,
            <EditConfiguration
              ref={editConfigRef}
              selectedConfig={selectedConfig}
            />,
            <ResultFeedback
              result={isSubmitSuccess}
              buttonConfig={resultButton}
              resultMessage={{
                success: '配置模板创建成功！',
                fail: '配置模板创建失败，请重试',
              }}
            />,
          ]}
          onNextCheckList={[checkStepOne]}
          onSubmit={handleSubmit}
        />
      </div>
    </>
  );
};

export default AddConfigTemplate;
