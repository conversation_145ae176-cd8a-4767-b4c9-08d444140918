import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Radio, Row, Select, Table } from 'antd';
import ProductManageFetch from '@/fetch/bussiness/productManage';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/formatLocation';
import showModal from '@/components/commonModal';
import CreateTag from '@/components/TagLabel/CreateTag';

const ProductDetail = () => {
  const fetchApi = new ProductManageFetch();
  const { productKey } = formatLocation(window.location.search);
  const navigator = useNavigate();
  const modelModalRef = useRef<any>();
  const tagModalRef = useRef<any>();
  const [detail, setDetail] = useState<any>({});
  const [editing, setEditing] = useState<any>({
    type: null,
    info: null,
  });

  useEffect(() => {
    getProductDetail();
  }, [productKey]);

  const goBack = () => {
    navigator('/product');
  };
  const getProductDetail = async () => {
    const res = await fetchApi.getProductDetail({ productKey });
    if (res.code === HttpStatusCode.Success) {
      setDetail(res.data);
    }
  };

  const updateEditInfo = (
    type: 'productName' | 'productDescription',
    val: any,
  ) => {
    setEditing({ type: type, info: val });
  };

  const saveInfo = async (info: any) => {
    const res = await fetchApi.editProductInfo({ ...info, productKey });
    if (res.code === HttpStatusCode.Success) {
      setEditing({ type: null, info: null });
      getProductDetail();
    }
  };

  const editTag = () => {
    showModal({
      title: '编辑标签',
      content: (
        <CreateTag
          maxLength={50}
          ref={tagModalRef}
          tagList={
            detail.tagDTOList
              ? detail.tagDTOList?.map((v: any) => ({
                  name: v.key,
                  value: v.value,
                }))
              : []
          }
        />
      ),
      onOk: (cb: AnyFunc) => {
        const tagList = tagModalRef.current!.getTagList();
        if (!tagList) {
          return;
        }
        saveInfo({
          type: 'productTag',
          info: tagList.map((v: any) => ({
            tagKey: v.name,
            tagValue: v.value,
          })),
        });
        cb && cb();
      },
      onCancel: (cb: AnyFunc) => {
        cb && cb();
      },
    });
  };

  const editModel = () => {
    showModal({
      title: '编辑型号',
      content: (
        <CreateTag
          maxLength={50}
          ref={modelModalRef}
          checkDevice={true}
          tagList={
            detail.productModelDTOList
              ? detail.productModelDTOList?.map((v: any) => ({
                  name: v.modelName,
                  value: v.modelNo,
                }))
              : []
          }
          addBtn="+添加型号"
          columnsList={[
            {
              title: '型号',
              dataIndex: 'name',
              ellipsis: true,
            },
            {
              title: '',
              dataIndex: 'operate',
              ellipsis: true,
            },
          ]}
        />
      ),
      onOk: (cb: AnyFunc) => {
        const modelList = modelModalRef.current!.getTagList();
        if (!modelList) {
          return;
        }
        saveInfo({
          type: 'productModel',
          info: modelList.map((v: any) => ({
            productModelNo: v.value,
            productModelName: v.name,
          })),
        });
        cb && cb();
      },
      onCancel: (cb: AnyFunc) => {
        cb && cb();
      },
    });
  };
  return (
    <div className="product-detail">
      <div className="title" onClick={goBack}>
        <ArrowLeftOutlined />
        <span>产品详情</span>
      </div>
      <Row>
        <Col span={8}>
          <span>产品名称：</span>
          {editing.type === 'productName' ? (
            <div className="edit-input">
              <Input
                allowClear
                maxLength={30}
                value={editing.info}
                onChange={(e) =>
                  setEditing({ type: editing.type, info: e.target.value })
                }
              />
              <a onClick={() => saveInfo(editing)}>确认</a>
              <a onClick={() => setEditing({ type: null, info: null })}>取消</a>
            </div>
          ) : (
            <>
              {detail.productName}
              <a
                onClick={() =>
                  updateEditInfo('productName', detail.productName)
                }
              >
                编辑
              </a>
            </>
          )}
        </Col>
        <Col span={8}>产品标识：{detail.productKey}</Col>
        <Col span={8}>创建时间：{detail.createTime}</Col>
      </Row>
      <Row>
        <Col span={8}>设备数：{detail.deviceNum}</Col>
        <Col span={16}>
          <span>型号：</span>
          {detail.productModelDTOList?.length > 0 &&
            detail.productModelDTOList.map((v: any, index: number) => {
              return (
                <span key={`${v.modelNo}${index}`} className="tag">
                  {v.modelName}
                </span>
              );
            })}

          <a onClick={editModel}>编辑</a>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <span>描述：</span>
          {editing.type === 'productDescription' ? (
            <div className="edit-input">
              <Input.TextArea
                placeholder="请输入产品描述"
                showCount
                allowClear
                maxLength={400}
                value={editing.info}
                onChange={(e) =>
                  setEditing({ type: editing.type, info: e.target.value })
                }
              />
              <a onClick={() => saveInfo(editing)}>确认</a>
              <a onClick={() => setEditing({ type: null, info: null })}>取消</a>
            </div>
          ) : (
            <>
              {detail.productDescription}
              <a
                onClick={() =>
                  updateEditInfo(
                    'productDescription',
                    detail.productDescription,
                  )
                }
              >
                编辑
              </a>
            </>
          )}
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <span>标签：</span>
          {detail.tagDTOList?.length > 0 &&
            detail.tagDTOList.map((v: AnyObj) => {
              return (
                <span key={v.key} className="tag">
                  {v.key}/{v.value}
                </span>
              );
            })}
          <a onClick={editTag}>编辑</a>
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(ProductDetail);
