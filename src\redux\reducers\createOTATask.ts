/* eslint-disable no-unused-vars */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
const initialState: any = {
  info: null,
};
const createOTATaskSlice = createSlice({
  name: 'createOTATask',
  initialState,
  reducers: {
    saveInfo(state, actions) {
      state.info = actions.payload;
    },
    removeInfo(state, actions) {
      state.info = null;
    },
  },
});

export const createOTATaskReducer = createOTATaskSlice.reducer;
export const { saveInfo, removeInfo } = createOTATaskSlice.actions;
export const createOTATaskSelector = (state: RootState) => state.createOTATask;
