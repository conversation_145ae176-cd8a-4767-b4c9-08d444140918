import React, { useCallback, useEffect, useRef, useState } from 'react';
import BreadCrumb from '@/components/BreadCrumb';
import { formatLocation } from '@/utils/formatLocation';
import CommonTable from '@/components/CommonTable';
import { getAddPackageForm, FirmwareInfoTableConfig } from '../utils/column';
import CommonForm from '@/components/CommonForm';
import './index.scss';
import { Button, Form, message, Select } from 'antd';
import UploadPackage from '../components/UploadPackage';
import { FirmwareFetch, Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const AddPackage = () => {
  const {
    operateType,
    type,
    productKey,
    productName,
    appName,
    appAlias,
    appEnable,
    appVersionNumber,
  } = formatLocation(window.location.search);
  const [formRef] = Form.useForm();
  const basicFormRef = useRef<any>(null);
  const navigator = useNavigate();
  const fetchApi = new FirmwareFetch();
  const [uploadFileType, setUploadFileType] = useState<string>('');
  const [packageTypeDisable, setPackageTypeDisable] = useState<boolean>(false);
  const [basicValue, setBasicValue] = useState<any>({});
  const [diffPackage, setDiffPackage] = useState<
    {
      id: string | number;
      s3bucketName: null | string;
      S3Key: null | string;
      md5: null | string;
      type: 'add' | 'edit';
    }[]
  >([]);
  const [fullPackage, setFullPackage] = useState<{
    s3bucketName: null | string;
    S3Key: null | string;
    md5: null | string;
    type: 'add' | 'edit';
  }>({
    s3bucketName: null,
    S3Key: null,
    md5: null,
    type: 'add',
  });
  const [curVersion, setCurVersion] = useState<any>(null);
  const [versionOptions, setVersionOptions] = useState<any>(null);
  useEffect(() => {
    setBasicValue({
      productKey: { label: productName, value: productKey },
      appName: { label: appAlias, value: appName },
    });
    if (operateType === 'add') {
      getVersionList();
    }
  }, []);
  useEffect(() => {
    if (
      operateType === 'edit' &&
      type &&
      productKey &&
      appVersionNumber &&
      appName
    ) {
      getVersionInfo();
    }
  }, [type, productKey, appVersionNumber, appName]);
  const getVersionInfo = async () => {
    const res = await fetchApi.getAppVersionInfo({
      type,
      productKey,
      appVersionNumber,
      appName,
    });
    if (res.code === HttpStatusCode.Success && res.data) {
      setBasicValue({
        ...basicValue,
        productModelNoList: Object.keys(res.data.productModelNoMap)?.map(
          (v) => ({ label: res.data.productModelNoMap[v], value: v }),
        ),
        appVersion: res.data.appVersion,
        appVersionNumber: res.data.appVersionNumber,
        updateInfo: res.data.updateInfo,
        defect: res.data.defect,
        description: res.data.description,
        packageType: res.data.packageType,
      });
      setCurVersion(res.data.appVersion);
      setUploadFileType(res.data.packageType);
      setFullPackage({
        s3bucketName: null,
        S3Key: res.data.fullPackageS3Key,
        md5: null,
        type: 'edit',
      });
      let diffList: any[] = [];
      let versionInfo = {};
      res.data.diffPackageInfoList?.forEach((v: any, i: number) => {
        const id = Date.now() + i;
        versionInfo = { ...versionInfo, [id]: v.sourceAppVersionNumber };
        diffList.push({
          id: id,
          s3bucketName: null,
          S3Key: v.diffPackageS3Key,
          md5: null,
          type: 'edit',
        });
      });
      setDiffPackage(diffList);
      formRef.setFieldsValue({
        ...versionInfo,
        fullPackage: res.data.fullPackageS3Key,
      });
      getVersionList(res.data.appVersionNumber);
    }
  };
  const getVersionList = async (targetVersion?: string) => {
    const res = await fetchApi.getVersionList({
      productKey,
      type,
      appName,
      enable: 1,
    });
    if (res.code === HttpStatusCode.Success && res?.data) {
      let list: any[] = [];
      res?.data?.forEach((v: any) => {
        if (operateType === 'edit') {
          if (v.appVersionNumber !== targetVersion) {
            list.push({
              label: v.appVersion,
              value: v.appVersionNumber,
            });
          }
        } else {
          list.push({
            label: v.appVersion,
            value: v.appVersionNumber,
          });
        }
      });
      setVersionOptions(list);
    }
  };

  const onFormValChange = (changedVal: any, changedFieldName: string) => {
    if (changedFieldName === 'packageType') {
      setUploadFileType(changedVal.packageType);
    } else if (changedFieldName === 'appVersion') {
      setCurVersion(changedVal.appVersion);
    }
  };

  const handleAddDiffPackage = () => {
    setDiffPackage(
      diffPackage.concat([
        {
          id: Date.now(),
          s3bucketName: null,
          S3Key: null,
          md5: null,
          type: 'add',
        },
      ]),
    );
  };
  const handleDel = (index: number) => {
    const diffList = diffPackage.filter((_: any, i: number) => i !== index);
    setDiffPackage(diffList);
  };

  const saveFullPackageInfo = (info: any) => {
    formRef.setFieldsValue({
      fullPackage: info ? info.s3bucketName : null,
    });
    setFullPackage({
      ...info,
    });
  };

  const saveDiffPackageInfo = useCallback(
    (id: any, info: any) => {
      const hasCurInfo = diffPackage.filter((v: any, i: number) => v.id === id);
      if (hasCurInfo.length < 1) {
        return;
      }
      const diffList = diffPackage.map((v: any) => {
        if (v.id === id) {
          return info;
        }
        return v;
      });
      setDiffPackage(diffList);
    },
    [JSON.stringify(diffPackage)],
  );

  const changeVersion = (id: any, val: any, packageInfo: any) => {
    const formInfo = formRef.getFieldsValue();
    delete formInfo[id];
    const allVersion = Object.values(formInfo);
    const versionRepeat = allVersion.includes(val);
    if (versionRepeat) {
      message.error('查分包的起始包版本不可重复！');
      formRef.setFieldsValue({ [id]: null });
      return;
    }
  };

  const handleBack = () => {
    const path = type === 'firmware' ? '/firmware' : '/app';
    navigator(
      path +
        '/firmwareInfo?type=' +
        type +
        '&productKey=' +
        productKey +
        '&productName=' +
        productName +
        '&appName=' +
        appName +
        '&appAlias=' +
        appAlias +
        '&appEnable=' +
        appEnable,
    );
  };
  const handleSubmit = async () => {
    const diffPackageInfo = await formRef.validateFields();
    const val = await basicFormRef.current?.validateFields();
    let emptyDiffPackage = false;
    for (let i = 0; i < diffPackage.length; i++) {
      const v = diffPackage[i];
      if (v.type === 'edit') {
        continue;
      }
      if (v.s3bucketName && v.S3Key && v.md5 && !diffPackageInfo[v.id]) {
        emptyDiffPackage = true;
        message.error('请选择版本号！');
        break;
      }
      if (!v.s3bucketName || !v.S3Key || (!v.md5 && diffPackageInfo[v.id])) {
        emptyDiffPackage = true;
        message.error('请上传差分包');
        break;
      }
    }
    if (emptyDiffPackage) {
      return;
    }
    if (operateType === 'add') {
      handleAddSubmit(val, diffPackageInfo);
    } else if (operateType === 'edit') {
      handleEditSubmit(val, diffPackageInfo);
    }
  };

  const handleEditSubmit = async (val: any, diffPackageInfo: any) => {
    const diffInfoList: any[] = [];
    diffPackage?.forEach((v) => {
      if (v.type === 'add') {
        diffInfoList.push({
          sourceAppVersionNumber: diffPackageInfo[v.id],
          diffPackageS3BucketName: v.s3bucketName,
          diffPackageS3Key: v.S3Key,
          diffPackageMd5: v.md5,
        });
      }
    });
    const info = {
      productKey: val.productKey?.value,
      type: type,
      appName: appName,
      appVersionNumber: basicValue?.appVersionNumber,
      updateInfo: val.updateInfo,
      defect: val.defect,
      description: val.description,
      diffPackageInfoList: diffInfoList,
    };
    const res = await fetchApi.editAppVersion(info);
    if (res.code === HttpStatusCode.Success) {
      message.success('版本编辑成功');
      handleBack();
    } else {
      message.error(res.message);
    }
  };

  const handleAddSubmit = async (val: any, diffPackageInfo: any) => {
    const info = {
      productKey: val.productKey?.value,
      type: type,
      appName: appName,
      appVersion: val.appVersion,
      productModelNoList: val.productModelNoList?.map((v: any) => v.value),
      updateInfo: val.updateInfo,
      defect: val.defect,
      description: val.description,
      packageType: val.packageType,
      fullPackageS3BucketName: fullPackage?.s3bucketName,
      fullPackageS3Key: fullPackage?.S3Key,
      fullPackageMd5: fullPackage?.md5,
      diffPackageInfoList: diffPackage?.map((v) => ({
        sourceAppVersionNumber: diffPackageInfo[v.id],
        diffPackageS3BucketName: v.s3bucketName,
        diffPackageS3Key: v.S3Key,
        diffPackageMd5: v.md5,
      })),
    };
    const res = await fetchApi.createAppVersion(info);
    if (res.code === HttpStatusCode.Success) {
      message.success('版本创建成功');
      handleBack();
    } else {
      message.error(res.message);
    }
  };

  useEffect(() => {
    // 修改升级包格式置灰不可更改
    if (operateType === 'add') {
      if (fullPackage.S3Key) {
        setPackageTypeDisable(true);
        return;
      }
      const emptyDiff = diffPackage.filter(
        (v: any, i: number) => !v.s3bucketName,
      );
      if (emptyDiff.length < diffPackage.length) {
        setPackageTypeDisable(true);
      } else {
        setPackageTypeDisable(false);
      }
    } else if (operateType === 'edit') {
      setPackageTypeDisable(true);
    }
  }, [JSON.stringify(fullPackage), JSON.stringify(diffPackage), operateType]);

  return (
    <>
      <BreadCrumb
        items={[
          {
            title: '通用设备管理',
            route: type === 'firmware' ? '/firmware' : '/app',
          },
          {
            title: '升级包管理',
            route: type === 'firmware' ? '/firmware' : '/app',
          },
          {
            title: type === 'firmware' ? '固件' : '应用',
            route: type === 'firmware' ? '/firmware' : '/app',
          },
          {
            title: type === 'firmware' ? '固件信息' : '应用信息',
            route:
              `${
                type === 'firmware' ? '/firmware' : '/app'
              }/firmwareInfo?type=` +
              type +
              '&productKey=' +
              productKey +
              '&productName=' +
              productName +
              '&appName=' +
              appName +
              '&appAlias=' +
              appAlias +
              '&appEnable=' +
              appEnable,
          },
          {
            title: operateType === 'edit' ? '编辑升级包' : '添加升级包',
            route: '',
          },
        ]}
      />
      <div className="add-package-container">
        <h2>{operateType === 'edit' ? '编辑升级包' : '添加升级包'}</h2>
        <CommonForm
          name="firmware-searchForm"
          formConfig={getAddPackageForm(type)}
          defaultValue={{
            ...basicValue,
            appVersionDisable: operateType === 'edit' ? true : false,
            productModelNoListDisable: operateType === 'edit' ? true : false,
            packageTypeDisable: packageTypeDisable,
          }}
          onValueChange={(changedVal: any, changedFieldName: string) => {
            onFormValChange(changedVal, changedFieldName);
          }}
          getFormInstance={(instance: any) => (basicFormRef.current = instance)}
        />
        <Form form={formRef} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
          <Form.Item
            label="全量包维护"
            name="fullPackage"
            rules={[
              {
                required: true,
                message: '请上传全量包',
              },
            ]}
          >
            <UploadPackage
              key="full"
              prefix={`${appName}_${Date.now()}_`}
              uploadFileType={uploadFileType}
              info={fullPackage}
              onFileChange={(info) =>
                saveFullPackageInfo({ ...info, type: 'add' })
              }
              onStartUpload={() => {
                setPackageTypeDisable(true);
              }}
            />
          </Form.Item>
          <Form.Item label="差分包维护" name="diffPackage">
            <div className="diff-package-container">
              {diffPackage.map((v: any, i: number) => {
                return (
                  <div className="row" key={v.id}>
                    <Form.Item name={v.id} label={i + 1} colon={false}>
                      <Select
                        options={versionOptions}
                        placeholder="请选择初始包"
                        disabled={v.type === 'edit'}
                        onChange={(value) => changeVersion(v.id, value, v)}
                      />
                    </Form.Item>
                    <div className="cur-version">
                      <span>到</span>
                      <span>{curVersion || '-'}</span>
                    </div>
                    <UploadPackage
                      key={`${v.id}_diff`}
                      prefix={`${appName}_${Date.now()}_`}
                      uploadFileType={uploadFileType}
                      info={v}
                      onFileChange={(info) =>
                        saveDiffPackageInfo(v.id, info ? { ...v, ...info } : v)
                      }
                    />
                    {v.type === 'add' && (
                      <div className="del-btn">
                        <DeleteOutlined onClick={() => handleDel(i)} />
                      </div>
                    )}
                  </div>
                );
              })}
              <div className="add-btn" onClick={handleAddDiffPackage}>
                +
              </div>
            </div>
          </Form.Item>
        </Form>
        <div className="submit-btns">
          <Button type="primary" onClick={() => handleSubmit()}>
            保存
          </Button>
          <Button onClick={() => handleBack()}>取消</Button>
        </div>
      </div>
    </>
  );
};

export default React.memo(AddPackage);
