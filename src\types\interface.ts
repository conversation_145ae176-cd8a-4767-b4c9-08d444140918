/** 通用响应 */
export interface CommonResponse<T> {
  /** 响应码 */
  code: string;
  /** 响应信息 */
  message: string | null;
  /** 响应数据 */
  data: T | null;
}

/** 通用表格请求 */
export interface CommonTableRequest {
  /** 页码 */
  pageNum: number;
  /** 分页大小 */
  pageSize: number;
}

/** 通用表格响应 */
export interface CommonTableResponse<T> {
  /** 页码 */
  pageNum: number;
  /** 分页大小 */
  pageSize: number;
  /** 总页数 */
  pages: number;
  /** 总数据条数 */
  total: number;
  /** 数据列表（泛型） */
  list: T[];
}
