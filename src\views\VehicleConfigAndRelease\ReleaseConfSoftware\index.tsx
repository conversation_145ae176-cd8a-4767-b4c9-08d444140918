import React, { useState, useEffect } from 'react';
import { Form, Radio, Modal, message, DatePicker, Spin, Input } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  selectedVehicleSelector,
  removeSelectedVehicle,
} from '@/redux/reducers/selectedVehicle';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import BreadCrumb from '@/components/BreadCrumb';
import FormTitle from '@/components/FormTitle';
import UpdateSetting from '../components/UpdateSetting';
import LowVersionInfo from '../components/LowVersionInfo';
import { upgradeType } from '../utils/constant';
import { api } from '@/fetch/core/api';
import './index.scss';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import moment from 'moment';
import { formatTimeToSecond } from '@/utils/formatTime';
import SelectedVehcile from '../components/SelectedVehcile';
import _ from 'lodash';
import { UpgradeTypeMap } from '@/utils/constant';
import ProductPackage from '../components/ProductPackage';
const breadCrumbItems = [
  { title: 'OTA管理', route: '' },
  { title: '车辆配置与发布', route: '' },
  { title: '配置及软件发布', route: '' },
];
const ReleaseConfSoftware = () => {
  const dispatch = useDispatch();
  const selectedVehicle = useSelector(selectedVehicleSelector).selectedVehicle;
  const [form] = Form.useForm();
  const navigator = useNavigate();
  const [changeTimeVal, setChangeTimeVal] = useState<number | undefined>();
  const [changeLowVersion, setChangeLowVersion] = useState<number | undefined>(
    0,
  );
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [spinning, setSpinning] = useState<boolean>(false);
  const [productPackageList, setProductPackageList] = useState<any>([]);
  const [releaseType, setReleaseType] = useState<'app' | 'productPackage'>(
    'app',
  );
  useEffect(() => {
    if (selectedVehicle.size <= 0) {
      message.error('请重新选择车辆');
    }
  }, [selectedVehicle]);

  useEffect(() => {
    return () => {
      dispatch(removeSelectedVehicle(null));
    };
  }, []);

  /**
   * 点击提交按钮
   */
  const onSubmitClick = () => {
    form
      .validateFields()
      .then((values) => {
        // 校验升级设置
        const checkUpgradeSetting = values.upgradeAppInfoList.filter(
          (item: any) => {
            return item.versionNumber === '系统带入';
          },
        );
        // 校验依赖的最低版本
        const checkLowestVersion = values?.lowestAppVersionInfoList?.filter(
          (item: any) => {
            return item.versionNumber === '系统带入';
          },
        );
        if (releaseType === 'app') {
          if (checkUpgradeSetting.length > 0) {
            message.error('请选择升级设置');
          } else if (
            values.ishasLowestAppVersion === 1 &&
            checkLowestVersion.length > 0
          ) {
            message.error('请选择依赖的车端最低版本');
          } else {
            if (values.upgradeType === upgradeType.FORCE) {
              setModalShow(true);
            } else {
              throttle(values);
            }
          }
        } else {
          throttle(values);
        }
      })
      .catch((errorInfo) => {
        console.log(errorInfo);
      });
  };
  const throttle = _.throttle(function (values) {
    createIssueTask(values);
  }, 500);
  /**
   * 创建发布计划
   * @param {Object} value 表单数据
   */
  const createIssueTask = (values: any) => {
    if (selectedVehicle.size > 16) {
      setSpinning(true);
    }
    const vehicleInfoList: any[] = [];
    selectedVehicle.forEach((value: any, key: any) => {
      vehicleInfoList.push({
        vehicleName: key,
        upgradeType: value.upgradeType,
      });
    });
    const canUpgrade = () => {
      const hasOffline = vehicleInfoList.find((vehicleInfoListItem: any) => {
        return (
          vehicleInfoListItem.upgradeType.toString() ===
          UpgradeTypeMap.get('OFFLINE')!.key.toString()
        );
      })
        ? true
        : false;
      const hasOtherModule =
        values.upgradeAppInfoList.length >= 2 ||
        (values.upgradeAppInfoList.length === 1 &&
          values.upgradeAppInfoList[0].appName !== 'map');
      if (!hasOffline) {
        return true;
      } else {
        if (hasOtherModule) {
          return false;
        } else {
          return true;
        }
      }
    };
    const requestParam: RequestOptions = {
      method: 'POST',
      path: api.createIssueTask,
      body: {
        productType: selectedVehicle?.values()?.next()?.value?.productType,
        type: values.type,
        productPackageNumber: values.productPackageNumber,
        vehicleInfoList: vehicleInfoList,
        isImmediately: values.isImmediately,
        issueDescription: values.issueDescription,
        issueType: 0,
        upgradeAppInfoList:
          releaseType === 'app' ? values.upgradeAppInfoList : [],
        issueTime: formatTimeToSecond(values.issueTime),
        lowestAppVersionInfoList: values.lowestAppVersionInfoList ?? [],
      },
    };

    const ifCanUpgrade = canUpgrade();
    if (ifCanUpgrade) {
      request(requestParam, false, 35000)
        .then((res: any) => {
          if (
            res &&
            (res.code === HttpStatusCode.Success ||
              res.code === HttpStatusCode.Timeout)
          ) {
            setSpinning(false);
            Modal.info({
              content: (
                <div>
                  {res.code === HttpStatusCode.Success
                    ? '操作成功！'
                    : '系统响应超时，请返回列表页查看结果！'}
                </div>
              ),
              onOk() {
                navigator('/vehicleConfigAndRelease');
              },
            });
          }
        })
        .catch((err: any) => {
          setSpinning(false);
          Modal.info({
            width: '600px',
            content: <div>{err.message}</div>,
            onOk() {},
          });
        });
    } else {
      message.error('仅地图应用支持离线升级，请检查后重试！');
      setSpinning(false);
    }
  };
  /**
   * 强制升级弹窗关闭时调用
   */
  const onConfirmClick = () => {
    const data = form.getFieldsValue();
    throttle(data);
    setModalShow(false);
  };

  const getPackageVersionList = () => {
    const productType = selectedVehicle?.values()?.next()?.value?.productType;
    request({
      path: '/ota/web/get_product_package_version_list',
      method: 'POST',
      body: {
        productType,
        enable: 1,
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          const data = res?.data?.map((item: any) => ({
            label: item.version,
            value: item.productPackageNumber,
          }));
          setProductPackageList(data);
        }
      })
      .catch((e) => {});
  };
  return (
    <div className="release-conf-software">
      <Spin
        tip={
          <p style={{ fontSize: '20px' }}>{`大约需要等待${
            selectedVehicle.size * 0.06
          }秒`}</p>
        }
        spinning={spinning}
        className={'spin'}
      >
        <BreadCrumb items={breadCrumbItems} />
        <div className="content">
          <FormTitle title={'配置及软件发布'} />
          <Form
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 19 }}
            form={form}
            initialValues={{
              type: 'app',
            }}
            onFieldsChange={(changeField: any) => {
              const changedFieldName =
                changeField && changeField[0]?.name && changeField[0]?.name[0];
              const value = changeField && changeField[0]?.value;
              if (changedFieldName === 'type') {
                setReleaseType(value);
                if (value === 'productPackage') {
                  getPackageVersionList();
                }
              }
            }}
          >
            <Form.Item name={'releaseNumber'} label={'发布计划编号'}>
              {'系统生成'}
            </Form.Item>
            <Form.Item name={'type'} label={'发布内容'}>
              <Radio.Group>
                <Radio value={'app'}>应用或配置文件</Radio>
                <Radio value={'productPackage'}>产品包</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name={'upgradeAppInfoList'}
              label={'发布模块'}
              rules={[{ required: true, message: '请选择发布模块' }]}
            >
              {releaseType === 'app' ? (
                <UpdateSetting
                  form={form}
                  enable={1}
                  productType={
                    selectedVehicle?.values()?.next()?.value?.productType
                  }
                />
              ) : (
                <ProductPackage
                  form={form}
                  versionList={productPackageList}
                  productType={
                    selectedVehicle?.values()?.next()?.value?.productType
                  }
                />
              )}
            </Form.Item>
            <Form.Item
              name={'isImmediately'}
              label={'发布时间设置'}
              rules={[{ required: true, message: '请设置发布时间' }]}
            >
              <Radio.Group
                value={changeTimeVal}
                onChange={(e) => {
                  setChangeTimeVal(e.target.value);
                  form.resetFields(['issueTime']);
                }}
              >
                <Radio value={0}>定时发布</Radio>
                <Radio value={1}>立即发布</Radio>
              </Radio.Group>
            </Form.Item>
            {changeTimeVal === 0 && (
              <Form.Item
                name={'issueTime'}
                label={'发布时间'}
                extra={'说明：请注意避开“运营时间段”发布功能。'}
                rules={[{ required: true, message: '请输入发布时间' }]}
                wrapperCol={{ span: 18 }}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  allowClear
                  locale={locale}
                  showTime={{ defaultValue: moment('00:00:00', 'HH:mm:ss') }}
                />
              </Form.Item>
            )}
            <Form.Item
              initialValue={changeLowVersion}
              name={'ishasLowestAppVersion'}
              label={'依赖于车端最低版本'}
              rules={[{ required: true, message: '请选择依赖于车端最低版本' }]}
            >
              <Radio.Group
                onChange={(e) => setChangeLowVersion(e.target.value)}
                value={changeLowVersion}
              >
                <Radio value={0}>没有</Radio>
                <Radio value={1}>有</Radio>
              </Radio.Group>
            </Form.Item>
            {changeLowVersion === 1 && (
              <Form.Item
                name={'lowestAppVersionInfoList'}
                label={'        '}
                colon={false}
              >
                <LowVersionInfo
                  form={form}
                  productType={
                    selectedVehicle?.values()?.next()?.value?.productType
                  }
                  enable={1}
                />
              </Form.Item>
            )}
            <Form.Item
              name={'issueDescription'}
              label={'发版描述'}
              rules={[{ required: true, message: '请输入发版描述' }]}
            >
              <Input.TextArea
                rows={4}
                placeholder={'请输入发版描述'}
                maxLength={200}
                showCount
              />
            </Form.Item>
            <Form.Item
              name={'vehicleNumber'}
              label={`已选车辆(${selectedVehicle ? selectedVehicle.size : 0})`}
            >
              <SelectedVehcile
                selectedVehicle={selectedVehicle}
                handleDelVehicle={(vehicleName: any) => {
                  dispatch(removeSelectedVehicle(vehicleName));
                }}
                editable={true}
              />
            </Form.Item>
            <Form.Item hidden name={'productPackageNumber'} />
          </Form>
          <div className="submit-btns">
            <CustomButton onSubmitClick={onSubmitClick} title={'确定'} />
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              otherCSSProperties={{ marginLeft: '20px' }}
              onSubmitClick={() => navigator('/vehicleConfigAndRelease')}
              title={'取消'}
            />
          </div>
        </div>
      </Spin>

      {modalShow && (
        <Modal
          visible={modalShow}
          onCancel={() => {
            setModalShow(false);
          }}
          onOk={onConfirmClick}
          title={'提示'}
        >
          <div>
            {'请确认车端开机时强制升级，只有升级成功之后，才能开机成功吗？'}
          </div>
        </Modal>
      )}
    </div>
  );
};

export default React.memo(ReleaseConfSoftware);
