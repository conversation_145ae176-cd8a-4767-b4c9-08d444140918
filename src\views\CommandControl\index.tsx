import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { TableConfig, SearchConfig } from './utils/column';
import { CustomButton } from '@/components/CustomButton';
import CommonForm from '@/components/CommonForm';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';
import { HttpStatusCode } from '@/fetch/core/constant';
import { CommandControlFetch, Device } from '@/fetch/bussiness';
import { RootState } from '@/redux/store';
import { CommandTaskStatus } from './utils/constant';
import CommonConfirmBtn from '@/components/CommonConfirmBtn';
import { FormConfig } from '@/components/CommonForm/formConfig';
import { formatDateToSecond } from '@/utils/formatTime';
import CommonTable from '@/components/CommonTable';

const CommandControl = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const fetchApi = new CommandControlFetch();
  const deviceApi = new Device();
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );
  const initSearchCondition = useRef<any>({
    searchForm: {
      productKey: null,
      productModelNo: null,
      blockNo: null,
      identifier: null,
      taskNo: null,
      commandContentLike: null,
      taskStatus: null,
      createUser: null,
      createTime: null,
      createTimeStart: null,
      createTimeEnd: null,
    },
    pageNum: 1,
    pageSize: 10,
  });
  const defaultSearchVals = useRef<any>(
    historySearchValue?.searchValues
      ? historySearchValue?.searchValues
      : initSearchCondition.current,
  );
  const [searchCondition, setSearchCondition] = useState<any>(
    defaultSearchVals.current,
  );
  const [formatSearchConfig, setFormatSearchConfig] =
    useState<FormConfig>(SearchConfig);
  const [tableData, setTableData] = useState<any>({
    pages: 0,
    total: 0,
    list: [],
  });
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    formatSearch();
  }, []);

  const fetchTableInfo = async (val: any) => {
    setLoading(true);
    try {
      const res = await fetchApi.getCommandTaskList({
        ...val.searchForm,
        pageNum: val.pageNum,
        pageSize: val.pageSize,
      });
      if (res.code === HttpStatusCode.Success) {
        setTableData({
          list: res?.data?.list || [],
          pages: res?.data?.pages || 0,
          total: res?.data?.total || 0,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const formatSearch = () => {
    Promise.all([deviceApi.queryProductList(), fetchApi.getTaskStatus()]).then(
      ([res1, res2]) => {
        let productOptions: any[] = [];
        let taskStatusOptions: any[] = [];
        if (res1.code === HttpStatusCode.Success) {
          productOptions = res1.data?.map((v: any) => ({
            label: v.productName,
            value: v.productKey,
          }));
          initSearchCondition.current = {
            ...initSearchCondition.current,
            searchForm: {
              ...initSearchCondition.current.searchForm,
              productKey: productOptions[0],
            },
          };
          defaultSearchVals.current = historySearchValue?.searchValues
            ? historySearchValue?.searchValues
            : initSearchCondition.current;
          setSearchCondition(defaultSearchVals.current);
          fetchTableInfo(defaultSearchVals.current);
        }
        if (res2.code === HttpStatusCode.Success) {
          taskStatusOptions = res2.data?.map((v: any) => ({
            label: v.name,
            value: v.value,
          }));
        }
        const val = {
          ...SearchConfig,
          fields: formatSearchConfig.fields.map((v) => {
            if (v.fieldName === 'productKey') {
              v = {
                ...v,
                options: productOptions,
              };
            } else if (v.fieldName === 'taskStatus') {
              v = {
                ...v,
                options: taskStatusOptions,
              };
            }
            return v;
          }),
        };
        setFormatSearchConfig(val);
      },
    );
  };

  const handleCancelTask = async (taskNo: string) => {
    const res = await fetchApi.cancelTask({
      taskNo,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success('任务取消成功');
      fetchTableInfo(searchCondition);
    } else {
      message.error(res.message || '任务取消失败');
    }
  };

  const formatColumns = () => {
    return TableConfig?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'result':
          col.render = (text: any, record: any, index: number) =>
            `${record.successTotal}/${record.nonSuccessTotal}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                {![
                  CommandTaskStatus.creating,
                  CommandTaskStatus.create_fail,
                ].includes(record.taskStatus) && (
                  <a
                    onClick={() => {
                      navigator(
                        '/commandControl/CheckTask?taskNo=' +
                          record.taskNo +
                          '&productKey=' +
                          searchCondition.searchForm.productKey.value,
                      );
                    }}
                  >
                    任务详情
                  </a>
                )}
                {record.taskStatus === CommandTaskStatus.to_be_effective && (
                  <CommonConfirmBtn
                    message="确认取消该任务么?"
                    btnText="取消"
                    onConfirm={() => handleCancelTask(record.taskNo)}
                  />
                )}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onSearchClick = (val: any) => {
    const formateTime = formatDateToSecond(val.createTime);
    const newValue = {
      searchForm: {
        ...val,
        createTimeStart: formateTime.startTime,
        createTimeEnd: formateTime.endTime,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(newValue);
    fetchTableInfo(newValue);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newValue,
      }),
    );
  };
  const onResetClick = () => {
    defaultSearchVals.current = initSearchCondition.current;
    fetchTableInfo(initSearchCondition.current);
    setSearchCondition(initSearchCondition.current);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition.current,
      }),
    );
  };

  return (
    <div className="config-manage">
      <div className="searchform">
        <CommonForm
          formConfig={formatSearchConfig}
          layout={'inline'}
          defaultValue={defaultSearchVals.current.searchForm}
          formType="search"
          colon={false}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>
      <div className="table-container">
        <div className="middle-btns">
          <CustomButton
            onSubmitClick={() => {
              navigator('/commandControl/AddTask');
            }}
            title="新建任务"
            otherCSSProperties={{ height: 35, marginBottom: 10, marginTop: 10 }}
          />
        </div>

        <CommonTable
          searchCondition={searchCondition}
          loading={loading}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          columns={formatColumns()}
          rowKey={'taskNo'}
          onPageChange={(paginationData: any) => {
            const val = {
              ...searchCondition,
              pageNum: paginationData.pageNum,
              pageSize: paginationData.pageSize,
            };
            setSearchCondition(val);
            fetchTableInfo(val);
          }}
        ></CommonTable>
      </div>
    </div>
  );
};

export default React.memo(CommandControl);
