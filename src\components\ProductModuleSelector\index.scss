.product-module-selector {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .section-content {
    .ant-form-item {
      margin-bottom: 24px;
    }

    .ant-form-item-label > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .module-tabs {
    .module-tabs-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .label {
        font-weight: 500;
        color: #262626;
        margin-right: 8px;
      }

      .tip {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    .module-tabs-content {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;

      .module-tab {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background: #fafafa;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        color: #595959;
        user-select: none;

        &:hover {
          border-color: #1677ff;
          color: #1677ff;
        }

        &.selected {
          background: #e6f4ff;
          border-color: #1677ff;
          color: #1677ff;
          font-weight: 500;
        }
      }
    }

    .selected-info {
      font-size: 12px;
      color: #1677ff;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
      padding: 4px 8px;
      display: inline-block;
    }
  }

  .no-modules {
    color: #8c8c8c;
    font-style: italic;
    text-align: center;
    padding: 40px 0;
    background: #fafafa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
  }
}
