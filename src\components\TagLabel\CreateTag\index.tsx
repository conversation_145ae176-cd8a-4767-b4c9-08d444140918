import React, { forwardRef, useImperativeHandle, useState } from 'react';
import './index.scss';
import { Input, Table, Tooltip, message } from 'antd';
import ProductManageFetch from '@/fetch/bussiness/productManage';
import { HttpStatusCode } from '@/fetch/core/constant';

const columns: any[] = [
  {
    title: '标签名称',
    dataIndex: 'name',
    ellipsis: true,
  },
  {
    title: '标签值',
    dataIndex: 'value',
    ellipsis: true,
  },
  {
    title: '',
    dataIndex: 'operate',
    ellipsis: true,
  },
];

export interface TagItem {
  name: string;
  value: string | number;
}
export interface CreateTagProps {
  tagList?: TagItem[];
  maxSize?: number;
  maxLength?: number;
  columnsList?: any[];
  addBtn?: string;
  checkDevice?: boolean;
  maxKeyLen?: number;
  maxValueLen?: number;
}
const CreateTag = forwardRef((props: CreateTagProps, ref) => {
  const fetchApi = new ProductManageFetch();
  const {
    tagList,
    addBtn,
    columnsList = columns,
    maxSize = 10,
    maxLength = 30,
    checkDevice = false,
    maxKeyLen = 30,
    maxValueLen = 128,
  } = props;

  const [tempTagList, setTempTagList] = useState<
    {
      id: any;
      name: string;
      value: string | number;
      nameErrMessage?: string;
      valueErrMessage?: string;
    }[]
  >(
    tagList?.map((v, index: number) => ({
      id: v.value + v.name + index,
      name: v.name,
      value: v.value,
    })) || [],
  );
  const addTag = () => {
    if (tempTagList.length > maxSize) {
      return;
    }
    setTempTagList([
      ...tempTagList,
      { id: new Date().getTime(), name: '', value: '' },
    ]);
  };

  const getTagList = () => {
    const checkList: any[] = [];
    let flag = false;
    tempTagList.forEach((v) => {
      if (
        columnsList &&
        columnsList.find((v) => v.dataIndex === 'name') &&
        typeof v.name === 'string' &&
        v.name.length <= 0
      ) {
        v.nameErrMessage = '名称不能为空';
      }
      if (
        columnsList &&
        columnsList.find((v) => v.dataIndex === 'value') &&
        typeof v.value === 'string' &&
        v.value.length <= 0
      ) {
        v.valueErrMessage = '值不能为空';
      }
      if (v.nameErrMessage || v.valueErrMessage) {
        flag = true;
      }
      checkList.push(v);
    });
    if (flag) {
      setTempTagList(checkList);
      return false;
    }
    return tempTagList.map((v) => ({ name: v.name, value: v.value }));
  };
  useImperativeHandle(ref, () => {
    return {
      getTagList,
    };
  });

  const handleDel = async (val: any, id: any) => {
    if (checkDevice) {
      const res = await fetchApi.checkHasDevice({ productModelNo: val });
      if (res.code === HttpStatusCode.Success && !res.data) {
        message.error(res.message);
        return;
      }
    }
    setTempTagList(tempTagList.filter((v: any) => v.id !== id));
  };

  const formatColumns = (columns: any[]) => {
    return columns.map((col: any) => {
      switch (col.dataIndex) {
        case 'name':
          col.width = 100;
          col.render = (text: string, record: any, index: number) => {
            return (
              <>
                <Input
                  placeholder={col.placeholder ?? '请输入标签名称'}
                  value={record.name}
                  style={{ width: '90%' }}
                  maxLength={maxKeyLen}
                  allowClear
                  onChange={(e) => {
                    const value = e?.target?.value?.trim();
                    record.name = value;
                    const regex = /^[\u4e00-\u9fa5a-zA-Z0-9_\-.:]+$/;
                    if (value && !regex.test(value)) {
                      record.nameErrMessage =
                        '支持中文、英文字母、数字、下划线（_）、中划线（-）、点号（.）以及半角冒号（:）';
                      setTempTagList([...tempTagList]);
                      return;
                    }
                    const hasRepeat = tempTagList?.filter(
                      (item, i: number) =>
                        i != index && item.name && item.name == value,
                    );
                    record.nameErrMessage =
                      hasRepeat?.length > 0 ? '名称不能重复！' : null;
                    setTempTagList([...tempTagList]);
                  }}
                />
                {record.nameErrMessage && (
                  <span
                    className="err-tip"
                    style={{ display: 'block', color: '#ff4d4f' }}
                  >
                    {record.nameErrMessage}
                  </span>
                )}
              </>
            );
          };
          break;
        case 'value':
          col.width = 100;
          col.render = (text: string, record: any) => {
            return (
              <>
                <Input
                  style={{ width: '90%' }}
                  placeholder={col.placeholder ?? '请输入标签值'}
                  value={record.value}
                  allowClear
                  maxLength={maxValueLen}
                  onChange={(e) => {
                    const value = e.target.value;
                    record.value = value;
                    const regex = /^[a-zA-Z0-9.]+$/;
                    record.valueErrMessage =
                      value && !regex.test(value)
                        ? '支持英文字母、数字和点号（.）'
                        : null;
                    setTempTagList([...tempTagList]);
                  }}
                />
                {record.valueErrMessage && (
                  <Tooltip title={record.valueErrMessage}>
                    <span
                      className="err-tip"
                      style={{
                        display: 'block',
                        color: '#ff4d4f',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                        overflow: 'hidden',
                      }}
                    >
                      {record.valueErrMessage}
                    </span>
                  </Tooltip>
                )}
              </>
            );
          };
          break;
        case 'operate':
          col.width = 30;
          col.render = (text: string, record: any) => {
            return (
              <a onClick={() => handleDel(record.value, record.id)}>删除</a>
            );
          };
          break;
      }
      return col;
    });
  };
  return (
    <>
      <Table
        columns={formatColumns(columnsList)}
        dataSource={tempTagList}
        key={'name'}
        pagination={false}
        scroll={{
          y: 600,
        }}
      />
      {tempTagList.length < maxSize && (
        <a onClick={addTag}>{addBtn ?? '+添加标签'}</a>
      )}
    </>
  );
});

export default CreateTag;
