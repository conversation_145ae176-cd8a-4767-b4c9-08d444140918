import { FormConfig } from '@/components/CommonForm/formConfig';
import { ProductType, ProductTypeName } from '@/utils/constant';
import { dropDownKey, dropDownListKey } from '@/utils/searchFormEnum';

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '车辆配置模板',
      placeholder: '请输入车辆配置模板名称',
      type: 'input',
      xxl: 8,
      xl: 10,
      labelCol: { span: 6 },
      wrapperCol: { span: 19 },
    },
    {
      fieldName: 'vehicleType',
      label: '车型名称',
      showSearch: true,
      placeholder: '请输入车型名称，支持关键字联想全称',
      type: 'select',
      xxl: 12,
      xl: 14,
      labelCol: { span: 4 },
      wrapperCol: { span: 19 },
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请输入关键字',
      type: 'select',
      dropDownKey: dropDownKey.productTypeList,
      dropDownListKey: dropDownListKey.productTypeList,
      specialFetch: 'commonDown',
    },
  ],
};

export const TableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '模板编号',
    width: 210,
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属产品',
    width: 150,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆配置模板名称',
    width: 380,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车型名称',
    width: 380,
    dataIndex: 'vehicleTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配置文件数量',
    width: 120,
    dataIndex: 'configurationFileCount',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 130,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 190,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 140,
    fixed: 'right',
  },
];

export const ConfTemplateForm: FormConfig = {
  fields: [
    {
      fieldName: 'number',
      label: '设备配置模板编号',
      placeholder: '请输入设备配置模板编号',
      type: 'input',
      disabled: true,
      validatorRules: [
        {
          required: true,
          message: '请输入设备配置模板编号',
        },
      ],
    },
    {
      fieldName: 'name',
      label: '设备配置模板名称',
      placeholder: '请输入设备配置模板名称',
      type: 'input',
      validatorRules: [{ required: true, message: '请输入车辆配置模板名称' }],
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      type: 'radioGroup',
      validatorRules: [{ required: true, message: '请选择所属产品' }],
      options: [
        {
          label: ProductTypeName.VEHICLE,
          value: ProductType.VEHICLE,
        },
        {
          label: ProductTypeName.ROBOT,
          value: ProductType.ROBOT,
        },
      ],
    },
    {
      fieldName: 'canOverwrite',
      label: '被引用的模板属性',
      type: 'radioGroup',
      options: [
        {
          label: '配置内容不可以被覆盖',
          value: 0,
        },
        {
          label: '配置内容可以被覆盖',
          value: 1,
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择被引用的模板属性',
        },
      ],
    },
    {
      fieldName: 'vehicleTypeId',
      label: '所属车型名称',
      type: 'select',
      placeholder: '请输入车型名称，支持关键字联想全称',
      validatorRules: [
        {
          required: true,
          message: '请选择车型名称',
        },
      ],
    },
  ],
};
