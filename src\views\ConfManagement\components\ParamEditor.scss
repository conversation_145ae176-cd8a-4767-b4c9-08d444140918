.param-editor {
  .param-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .no-params {
    text-align: center;
    color: #999;
    padding: 20px 0;
  }

  // 蓝色参数条样式
  .param-bar {
    margin-bottom: 12px;

    .param-bar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 6px;

      .param-name {
        color: #1890ff;
        font-weight: 500;
        font-size: 14px;
      }

      .param-actions {
        display: flex;
        gap: 8px;

        .ant-btn-link {
          padding: 0;
          height: auto;
          font-size: 12px;
        }
      }
    }
  }

  .param-item {
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &.editing {
      background-color: #fff;
      border: 1px solid #1890ff;
    }

    .param-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .param-type-fields {
      padding: 10px;
      margin-top: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;

      .object-properties {
        .object-properties-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .no-properties {
          text-align: center;
          color: #999;
          padding: 10px 0;
        }

        .property-item {
          margin-bottom: 10px;
          padding: 10px;
          border-radius: 4px;
          background-color: #e6e6e6;

          .property-actions {
            display: flex;
            justify-content: flex-end;
            align-items: center;
          }
        }
      }

      .array-item {
        h4 {
          margin-bottom: 10px;
        }
      }
    }
  }

  .ant-form-item-required::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
