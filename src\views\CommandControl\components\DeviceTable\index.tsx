import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import CommonForm from '@/components/CommonForm';
import { useTableData } from '@/components/CommonTable/useTableData';
import CommonTable from '@/components/CommonTable';
import { CommandControlFetch } from '@/fetch/bussiness';
import './index.scss';
import { FormConfig } from '@/components/CommonForm/formConfig';

const DeviceTable = forwardRef(
  (
    {
      formConfig,
      tableConfig,
      initSearchCondition,
      rowKey,
      middleBtns,
      showSelect,
      fetchApi,
    }: {
      formConfig: FormConfig;
      tableConfig: any[];
      initSearchCondition: any;
      rowKey: string;
      middleBtns?: any[];
      showSelect?: boolean;
      fetchApi: AnyFunc;
    },
    ref,
  ) => {
    const [searchCondition, setSearchCondition] =
      useState<any>(initSearchCondition);
    const [refreshTable, setRefreshTable] = useState(1);
    const [selectedDevice, setSelectedDevice] = useState<string[]>([]);
    const [selectedDeviceInfo, setSelectedDeviceInfo] = useState<any[]>([]);
    const searchFormSelected = useRef<any>(null);

    const { tableData, loading } = useTableData<any, any>(
      searchCondition
        ? {
            ...searchCondition?.searchForm,
            pageNum: searchCondition?.pageNum,
            pageSize: searchCondition?.pageSize,
          }
        : null,
      fetchApi,
      refreshTable,
    );
    searchFormSelected.current = tableData?.total;

    useEffect(() => {
      if (initSearchCondition) {
        setSearchCondition({ ...initSearchCondition });
      }
    }, [JSON.stringify(initSearchCondition)]);

    useImperativeHandle(
      ref,
      () => {
        return {
          checkData,
          reloadTable,
        };
      },
      [selectedDevice, selectedDeviceInfo, searchCondition?.searchForm],
    );

    const reloadTable = () => {
      setRefreshTable(refreshTable + 1);
    };

    const checkData = () => {
      return {
        selectedDeviceInfo,
        selectedDevice,
        searchForm: searchCondition.searchForm,
        searchFormSelected: searchFormSelected.current,
      };
    };

    const onSearchClick = (val: any) => {
      const newValue = {
        searchForm: { ...initSearchCondition.searchForm, ...val },
        pageNum: 1,
        pageSize: 10,
      };
      setRefreshTable(refreshTable + 1);
      setSearchCondition(newValue);
    };

    const onResetClick = () => {
      setRefreshTable(refreshTable + 1);
      setSearchCondition(initSearchCondition);
      setSelectedDevice([]);
      setSelectedDeviceInfo([]);
    };

    const formatColumns = () => {
      return tableConfig?.map((col: any) => {
        switch (col.dataIndex) {
          case 'order':
            col.render = (text: any, record: any, index: number) =>
              `${
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              }`;
            break;
          default:
            col.render = (text: any) => `${text || '-'}`;
            break;
        }
        return col;
      });
    };

    const rowSelection = {
      selectedRowKeys: selectedDevice,
      onSelect: (record: any, selected: boolean) => {
        if (selected) {
          setSelectedDevice(selectedDevice.concat([record[rowKey]]));
          setSelectedDeviceInfo(selectedDeviceInfo.concat([record]));
        } else {
          setSelectedDevice(selectedDevice.filter((v) => v !== record[rowKey]));
          setSelectedDeviceInfo(
            selectedDeviceInfo.filter((v) => v[rowKey] !== record[rowKey]),
          );
        }
      },
      onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
        if (selected) {
          const set1 = new Set(selectedDevice);
          const set2 = new Set(selectedDeviceInfo);
          changeRows.forEach((v: any) => {
            set1.add(v[rowKey]);
            set2.add(v);
          });
          setSelectedDevice([...set1]);
          setSelectedDeviceInfo([...set2]);
        } else {
          const arr1 = selectedDeviceInfo.filter((v) => {
            return !changeRows.some((i: any) => i[rowKey] === v[rowKey]);
          });
          const arr2 = selectedDevice.filter((v) => {
            return !changeRows.some((i: any) => i[rowKey] === v);
          });
          setSelectedDevice([...arr2]);
          setSelectedDeviceInfo([...arr1]);
        }
      },
    };

    return (
      <div className="select-device-table">
        <CommonForm
          name="select-device-searchForm"
          formConfig={formConfig}
          layout={'inline'}
          defaultValue={initSearchCondition?.searchForm}
          formType="search"
          colon={false}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />

        <CommonTable
          searchCondition={searchCondition}
          loading={loading}
          rowSelection={
            showSelect ? { type: 'checkbox', ...rowSelection } : null
          }
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          middleBtns={middleBtns ? middleBtns : []}
          columns={formatColumns()}
          rowKey={rowKey}
          selectedNum={showSelect ? selectedDevice.length : null}
          onPageChange={(paginationData: any) => {
            setSearchCondition({
              ...searchCondition,
              pageNum: paginationData.pageNum,
              pageSize: paginationData.pageSize,
            });
          }}
        ></CommonTable>
      </div>
    );
  },
);

export default React.memo(DeviceTable);
