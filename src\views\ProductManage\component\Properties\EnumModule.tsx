import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useState,
} from 'react';
import { Form, Input, Flex, Row, Col } from 'antd';
import { cloneDeep } from 'lodash';
import { sendGlobalEvent } from '@/utils/emit';
import { DataType } from '../../utils/constant';
import './EnumModule.scss';
interface DataItem {
  dataType: DataType;
  name: string | null;
  value: string | null;
  key: string | number;
  [key: string]: any; // 添加索引签名
}
const EnumModule = ({
  initEnumData,
  disabled,
  type,
  formInstance,
}: {
  initEnumData?: any;
  disabled: boolean;
  type: 'number' | 'string';
  formInstance: any;
}) => {
  const [enumEditFormRef] = Form.useForm();
  const [editedData, setEditedData] = useState<DataItem[]>([]);
  const [curEditKey, setCurEditKey] = useState<any>(null);

  useEffect(() => {
    const targetDataType =
      type === 'number' ? DataType.INT_ENUM : DataType.ENUM;
    if (!initEnumData.enumList || initEnumData.dataType !== targetDataType) {
      initEnumData.enumList = [
        {
          dataType: targetDataType,
          name: null,
          value: null,
          key: Date.now(),
        },
      ];
    }
    const checkData = initEnumData.enumList.filter(
      (v: any) => v.name == undefined || v.value == undefined,
    );
    formInstance.setFieldsValue({
      enumList: checkData.length > 0 ? null : initEnumData.enumList,
    });
    setEditedData(initEnumData.enumList);
  }, []);

  const handleAdd = () => {
    const newDataItem = {
      dataType: editedData[0].dataType,
      name: null,
      value: null,
      key: Date.now(),
    };
    setEditedData(editedData.concat([newDataItem]));
  };

  const handleDel = (key: string) => {
    const changedEnumList = editedData.filter((v: any) => v.key !== key);
    formInstance.setFieldsValue({ enumList: changedEnumList });
    setEditedData(changedEnumList);
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    const changedKey = Object.keys(changedValues)[0].split('-'); // 当前表单改变的哪项
    const changedValue = Object.values(changedValues)[0]; // 该项更改的值
    const changedType = changedKey[0];
    const allData = enumEditFormRef.getFieldsValue();
    if (Object.values(allData).includes(null)) {
      enumEditFormRef.validateFields(); // 用表单校验样式
      sendGlobalEvent('checkRes', false);
      return;
    }
    if (
      changedType === 'value' &&
      type === 'number' &&
      !isValidInteger(changedValue)
    ) {
      sendGlobalEvent('checkRes', false);
      return;
    }
    sendGlobalEvent('checkRes', true);
  };

  const editEnum = (val: any, type: 'name' | 'value', key: any) => {
    const data = cloneDeep(editedData);
    for (let i = 0; i < data.length; i++) {
      if (data[i].key == key) {
        data[i][type] = val;
        break;
      }
    }
    setEditedData(data);
    formInstance.setFieldsValue({ enumList: data });
  };

  function isValidInteger(str: any) {
    const num = parseInt(str, 10);
    return Number.isInteger(num) && num >= -2147483648 && num <= 2147483647;
  }

  const renderOneEnum = (val: any, index: number) => {
    return (
      <Flex
        gap="middle"
        align="start"
        justify="center"
        className="one-enum"
        key={val.key}
      >
        <Form.Item
          name={`value-${val.key}`}
          rules={
            type === 'number'
              ? [
                  { required: true, message: '该参数值不能为空' },
                  {
                    validator: (_, value) => {
                      const regex = /^-?\d+$/;
                      if (!regex.test(value)) {
                        return Promise.reject(
                          new Error(
                            '支持整型，取值范围：-2147483648 ~ 2147483647',
                          ),
                        );
                      }
                      const check = isValidInteger(value);
                      if (value && !check) {
                        return Promise.reject(
                          new Error(
                            '支持整型，取值范围：-2147483648 ~ 2147483647',
                          ),
                        );
                      }
                      const hasRepeat =
                        editedData.filter((v) => String(v.value) === value)
                          .length > 0 && val.key === curEditKey;
                      if (hasRepeat) {
                        return Promise.reject(new Error('参数值不允许重复'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]
              : [{ required: true, message: '该参数值不能为空' }]
          }
        >
          <Input
            allowClear
            placeholder="请输入参数值"
            disabled={disabled}
            onBlur={(e) => editEnum(e.target.value, 'value', val.key)}
            onFocus={() => setCurEditKey(val.key)}
          />
        </Form.Item>
        <div className="divider">~</div>
        <Form.Item
          name={`name-${val.key}`}
          rules={[
            { required: true, message: '参数描述不能为空' },
            {
              pattern:
                /^[\u4e00-\u9fa5a-zA-Z0-9][\u4e00-\u9fa5a-zA-Z0-9\-_\/.]*$/g,
              message:
                '支持中文、英文大小写、数字、下划线和短划线，必须以中文、英文或数字开头，不超过20个字数',
            },
            {
              validator: (_, value) => {
                const hasRepeat =
                  editedData.filter(
                    (v) => v.name === value && v.key !== val.key,
                  ).length > 0 && val.key === curEditKey;
                if (hasRepeat) {
                  return Promise.reject(new Error('参数描述不允许重复'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            allowClear
            placeholder="请输入参数描述"
            disabled={disabled}
            maxLength={20}
            onBlur={(e) => editEnum(e.target.value, 'name', val.key)}
            onFocus={() => setCurEditKey(val.key)}
          />
        </Form.Item>
        {editedData.length > 1 && !disabled && (
          <span className="del-btn" onClick={() => handleDel(val.key)}>
            删除
          </span>
        )}
      </Flex>
    );
  };

  return (
    <>
      <Form.Item label="枚举项" name="enumList"></Form.Item>
      <div className="enum-edit-container">
        <Row className="title">
          <Col span={12}>
            参数值<span style={{ color: 'red' }}>*</span>
          </Col>
          <Col span={12}>
            参数描述<span style={{ color: 'red' }}>*</span>
          </Col>
        </Row>
        <Form
          form={enumEditFormRef}
          wrapperCol={{ span: 24 }}
          onValuesChange={onValuesChange}
        >
          {editedData.length > 0 &&
            editedData.map((v: any, i: number) => {
              enumEditFormRef.setFieldsValue({
                [`name-${v.key}`]: v.name,
                [`value-${v.key}`]: v.value,
              });
              return renderOneEnum(v, i);
            })}
        </Form>

        {editedData.length < 20 && (
          <div className="add-btn">
            <span onClick={handleAdd}>+添加枚举项</span>
          </div>
        )}
      </div>
    </>
  );
};

export default React.memo(EnumModule);
