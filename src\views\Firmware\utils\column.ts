import { FormConfig } from '@/components/CommonForm/formConfig';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  TSLModelFetch,
  Device,
  CommandControlFetch,
  FirmwareFetch,
} from '@/fetch/bussiness';
import { FormInstance, message } from 'antd';
import { formatTreeData, NULL_GROUP } from '@/utils/utils';
import {
  ActivePush,
  AfterUpgradeOptions,
  batchInterval,
  BatchUpgrade,
  ExecuteType,
  Priority,
  PushType,
  UpgradeType,
} from '@/views/ReleasePlan/utils/constant';
const deviceApi = new Device();
const tslFetch = new TSLModelFetch();
const commandApi = new CommandControlFetch();
const firmwareApi = new FirmwareFetch();
export const TableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '固件ID',
    width: 100,
    dataIndex: 'appId',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '固件名称',
    width: 90,
    dataIndex: 'alias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '英文名',
    width: 120,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    width: 90,
    dataIndex: 'enableName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '负责人',
    width: 90,
    dataIndex: 'developer',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    width: 130,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    width: 90,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];
export const AppTableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '应用ID',
    width: 100,
    dataIndex: 'appId',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '应用名称',
    width: 90,
    dataIndex: 'alias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '英文名',
    width: 120,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    width: 90,
    dataIndex: 'enableName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '负责人',
    width: 90,
    dataIndex: 'developer',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    width: 130,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    width: 90,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];

export const FirmwareInfoTableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '版本号',
    width: 100,
    dataIndex: 'appVersion',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品型号',
    width: 100,
    dataIndex: 'productModelNames',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    width: 70,
    dataIndex: 'enableName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更新日志',
    width: 120,
    dataIndex: 'updateInfo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '缺陷',
    width: 90,
    dataIndex: 'defect',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    width: 170,
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    width: 120,
    dataIndex: 'createUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更新时间',
    width: 170,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 120,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 290,
    fixed: 'right',
  },
];

export const AppInfoTableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '版本号',
    width: 100,
    dataIndex: 'appVersion',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品型号',
    width: 100,
    dataIndex: 'productModelNames',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    width: 70,
    dataIndex: 'enableName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更新日志',
    width: 120,
    dataIndex: 'updateInfo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '缺陷',
    width: 90,
    dataIndex: 'defect',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    width: 170,
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    width: 120,
    dataIndex: 'createUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更新时间',
    width: 170,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 120,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '版本状态',
    width: 90,
    dataIndex: 'availableName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 330,
    fixed: 'right',
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择产品',
      type: 'select',
      options: [],
      allowClear: false,
      showSearch: true,
    },
  ],
};
export const CreateAppFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '所属产品',
      placeholder: '请选择产品',
      type: 'select',
      options: [],
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'blockNo',
      label: '模块',
      placeholder: '请选择模块',
      type: 'select',
      options: [],
      showSearch: true,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择模块',
        },
      ],
    },
    {
      fieldName: 'alias',
      label: '应用名称',
      placeholder: '请输入应用名称',
      type: 'input',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      maxLength: 20,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/;
            if (!value) {
              return Promise.reject(`请输入应用名称`);
            }
            if (regex.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(
              `仅支持中文、大小写字母、数字、中划线-、下划线_ `,
            );
          },
        },
      ],
    },
    {
      fieldName: 'name',
      label: '英文名',
      placeholder: '请输入应用英文名',
      type: 'input',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      maxLength: 50,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[A-Za-z0-9_.-]+$/;
            if (!value) {
              return Promise.reject(`请输入应用英文名`);
            }
            if (regex.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(`仅支持大小写字母、数字、中划线-、下划线_和英文点号`);
          },
        },
      ],
    },
    {
      fieldName: 'developer',
      label: '负责人',
      type: 'input',
      placeholder: '请输入负责人',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      maxLength: 20,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[\u4e00-\u9fa5]+$/;
            if (!value) {
              return Promise.reject(`请输入负责人`);
            }
            if (regex.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(`仅支持中文`);
          },
        },
      ],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'blockNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'blockNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await tslFetch.getThingModelBlocks({
            productKey: val,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.blockName,
              value: item.blockNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    operateType: [
      {
        linkFieldName: 'productKey',
        rule: 'fieldItemDisable',
        dependenceData: ['edit'],
      },
      {
        linkFieldName: 'name',
        rule: 'fieldItemDisable',
        dependenceData: ['edit'],
      },
    ],
  },
};
export const CreateFirmwareFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '所属产品',
      placeholder: '请选择产品',
      type: 'select',
      options: [],
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'blockNo',
      label: '模块',
      placeholder: '请选择模块',
      type: 'select',
      options: [],
      showSearch: true,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择模块',
        },
      ],
    },
    {
      fieldName: 'alias',
      label: '固件名称',
      placeholder: '请输入固件名称',
      type: 'input',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      maxLength: 20,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/;
            if (!value) {
              return Promise.reject(`请输入固件名称`);
            }
            if (regex.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(
              `仅支持中文、大小写字母、数字、中划线-、下划线_ `,
            );
          },
        },
      ],
    },
    {
      fieldName: 'name',
      label: '英文名',
      placeholder: '请输入固件英文名',
      type: 'input',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      maxLength: 50,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[A-Za-z0-9_.-]+$/;
            if (!value) {
              return Promise.reject(`请输入固件英文名`);
            }
            if (regex.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(`仅支持大小写字母、数字、中划线-、下划线_和英文点号`);
          },
        },
      ],
    },
    {
      fieldName: 'developer',
      label: '负责人',
      type: 'input',
      placeholder: '请输入负责人',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      maxLength: 20,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            const regex = /^[\u4e00-\u9fa5]+$/;
            if (!value) {
              return Promise.reject(`请输入负责人`);
            }
            if (regex.test(value)) {
              return Promise.resolve();
            }
            return Promise.reject(`仅支持中文`);
          },
        },
      ],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'blockNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'blockNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await tslFetch.getThingModelBlocks({
            productKey: val,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.blockName,
              value: item.blockNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    operateType: [
      {
        linkFieldName: 'productKey',
        rule: 'fieldItemDisable',
        dependenceData: ['edit'],
      },
      {
        linkFieldName: 'name',
        rule: 'fieldItemDisable',
        dependenceData: ['edit'],
      },
    ],
  },
};

export const FirmwareInfoForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择',
      type: 'select',
      options: [],
      disabled: true,
    },
    {
      fieldName: 'appName',
      label: '固件',
      placeholder: '请选择固件',
      type: 'select',
      options: [],
      disabled: true,
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      placeholder: '请选择型号',
      type: 'select',
      options: [],
      allowClear: false,
    },
    {
      fieldName: 'appVersionNumber',
      label: '版本号',
      placeholder: '请输入版本号',
      type: 'select',
      options: [],
      allowClear: false,
      showSearch: true,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val.value);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    productModelNo: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const appName = formInstance.getFieldValue('appName');
          const productKey = formInstance.getFieldValue('productKey');
          const res = await firmwareApi.getVersionList({
            productKey: productKey.value,
            type: 'firmware',
            appName: appName.value,
            enable: null,
            productModelNoList: val.value ? [val.value] : [],
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const productModelNoList = formInstance
            .getFieldValue('productModelNo')
            ?.map((v: any) => v.value);
          const productKey = formInstance.getFieldValue('productKey');
          const res = await firmwareApi.getVersionList({
            productKey: productKey.value,
            type: 'firmware',
            appName: val.value,
            enable: null,
            productModelNoList: productModelNoList,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};
export const AppInfoForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择',
      type: 'select',
      options: [],
      disabled: true,
    },
    {
      fieldName: 'appName',
      label: '应用',
      placeholder: '请选择应用',
      type: 'select',
      options: [],
      disabled: true,
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      placeholder: '请选择型号',
      type: 'select',
      options: [],
      allowClear: false,
    },
    {
      fieldName: 'appVersionNumber',
      label: '版本号',
      placeholder: '请输入版本号',
      type: 'select',
      showSearch: true,
      options: [],
      allowClear: false,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val.value);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    productModelNo: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const appName = formInstance.getFieldValue('appName');
          const productKey = formInstance.getFieldValue('productKey');
          const res = await firmwareApi.getVersionList({
            productKey: productKey.value,
            type: 'app',
            appName: appName.value,
            enable: null,
            productModelNoList: val.value ? [val.value] : [],
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const productModelNoList = formInstance
            .getFieldValue('productModelNo')
            ?.map((v: any) => v.value);
          const productKey = formInstance.getFieldValue('productKey');
          const res = await firmwareApi.getVersionList({
            productKey: productKey.value,
            type: 'app',
            appName: val.value,
            enable: null,
            productModelNoList: productModelNoList,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const getAddPackageForm = (type: 'firmware' | 'app') => {
  const AddPackageForm: FormConfig = {
    fields: [
      {
        fieldName: 'productKey',
        label: '产品',
        placeholder: '请选择',
        type: 'select',
        options: [],
        disabled: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
      },
      {
        fieldName: 'appName',
        label: type === 'firmware' ? '固件' : '应用',
        placeholder: type === 'firmware' ? '请选择固件' : '请选择应用',
        type: 'select',
        options: [],
        disabled: true,
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
      },
      {
        fieldName: 'productModelNoList',
        label: '产品型号',
        placeholder: '请选择产品型号',
        multiple: true,
        type: 'select',
        options: [],
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
        validatorRules: [
          {
            required: true,
            message: '请选择产品型号',
          },
        ],
      },
      {
        fieldName: 'appVersion',
        label: '版本号',
        placeholder: '请输入版本号',
        type: 'input',
        allowClear: false,
        showSearch: true,
        maxLength: 50,
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
        validatorRules: [
          {
            required: true,
            message: '请输入版本号',
          },
          {
            pattern: /^[a-zA-Z0-9._-]+$/g,
            message: '只支持英文、数字、.、-、_',
          },
        ],
      },
      {
        fieldName: 'updateInfo',
        label: '升级日志',
        placeholder: '请输入升级日志',
        type: 'textarea',
        showCount: true,
        allowClear: false,
        maxLength: 500,
        autoSize: { minRows: 2, maxRows: 6 },
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
      },
      {
        fieldName: 'defect',
        label: '缺陷',
        placeholder: '请输入缺陷',
        type: 'textarea',
        showCount: true,
        allowClear: false,
        maxLength: 500,
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
      },
      {
        fieldName: 'description',
        label: '备注',
        placeholder: '请输入备注',
        type: 'textarea',
        showCount: true,
        allowClear: false,
        maxLength: 150,
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
      },
      {
        fieldName: 'packageType',
        label: '升级包格式',
        placeholder: '请选择升级包格式',
        type: 'select',
        options: [],
        labelCol: { span: 4 },
        wrapperCol: { span: 19 },
        labelInValue: false,
        validatorRules: [
          {
            required: true,
            message: '请选择升级包格式',
          },
        ],
      },
    ],
    linkRules: {
      productKey: [
        {
          linkFieldName: 'packageType',
          rule: 'fetchData',
          fetchFunc: async (val: any) => {
            if (!val) {
              return [];
            }
            const res = await firmwareApi.getPackageType();
            if (res.code === HttpStatusCode.Success) {
              return res.data.map((item: any) => ({
                label: item,
                value: item,
              }));
            } else {
              return [];
            }
          },
        },
        {
          linkFieldName: 'productModelNoList',
          rule: 'fetchData',
          fetchFunc: async (val: any) => {
            if (!val) {
              return [];
            }
            const res = await deviceApi.queryModelList(val.value);
            if (res.code === HttpStatusCode.Success) {
              return res.data.map((item: any) => ({
                label: item.modelName,
                value: item.modelNo,
              }));
            } else {
              return [];
            }
          },
        },
      ],
      packageTypeDisable: [
        {
          linkFieldName: 'packageType',
          rule: 'fieldItemDisable',
          dependenceData: [true],
        },
      ],
      appVersionDisable: [
        {
          linkFieldName: 'appVersion',
          rule: 'fieldItemDisable',
          dependenceData: [true],
        },
      ],
      productModelNoListDisable: [
        {
          linkFieldName: 'productModelNoList',
          rule: 'fieldItemDisable',
          dependenceData: [true],
        },
      ],
    },
  };
  return AddPackageForm;
};
const updateLinkedFields = (productKey: any, groupList: any[]) => {
  const groupData = formatTreeData({
    origin: groupList,
    type: 'Cascader',
    level: 0,
    productKey,
  });
  groupData.unshift(NULL_GROUP);
  return groupData;
};
export const PushDetailForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      disabled: true,
      type: 'select',
      options: [],
    },
    {
      fieldName: 'groupNoList',
      label: '分组',
      placeholder: '请选择分组',
      type: 'cascader',
      options: [],
    },
    {
      fieldName: 'online',
      label: '在线状态',
      placeholder: '请选择设备状态',
      type: 'select',
      options: [],
    },
    {
      fieldName: 'issueDeviceStatusList',
      label: '升级状态',
      placeholder: '请选择升级状态',
      multiple: true,
      type: 'select',
      options: [],
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      placeholder: '请输入设备名称',
      type: 'input',
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'groupNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.getAllGroupList({
            productKey: val.value,
          });
          if (res.code === HttpStatusCode.Success) {
            const allGroupList = res?.data?.groupNoList || [];
            return updateLinkedFields(val.value, allGroupList);
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'issueDeviceStatusList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await firmwareApi.getDeviceUpgradeStatus();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((v: any) => ({
              label: v.name,
              value: v.value,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'online',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await firmwareApi.getDeviceOnlineStatus();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((v: any) => ({
              label: v.name,
              value: v.value,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const PushDetailTableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '发布计划编码',
    width: 100,
    dataIndex: 'issueTaskNumber',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分组',
    width: 90,
    dataIndex: 'groupLevelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备名称',
    width: 90,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '在线状态',
    width: 120,
    dataIndex: 'onlineName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '升级状态',
    width: 290,
    dataIndex: 'issueDeviceStatus',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    width: 130,
    dataIndex: 'issueTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后更新时间',
    width: 130,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人',
    width: 90,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '备注',
    width: 130,
    dataIndex: 'remark',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否终止',
    width: 90,
    dataIndex: 'isStopName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];
export const CheckTaskInfoFormConfig1: FormConfig = {
  fields: [
    {
      fieldName: 'issueTaskNumber',
      label: '发布计划编号',
      type: 'input',
      disabled: true,
    },
    {
      label: '产品型号',
      childrenList: ['productName', 'productModelNoMap'],
    },
    {
      fieldName: 'productName',
      type: 'input',
      marginLeft: 0,
      marginRight: 0,
      width: '40%',
      isChild: true,
    },
    {
      fieldName: 'productModelNoMap',
      type: 'textarea',
      marginLeft: 0,
      marginRight: 0,
      width: '60%',
      isChild: true,
    },
    {
      label: '发布内容',
      childrenList: ['appTypeName', 'appAlias', 'appVersion'],
      validatorRules: [
        {
          required: true,
          message: '请选择型号',
        },
      ],
    },
    {
      fieldName: 'appTypeName',
      type: 'input',
      marginLeft: 0,
      marginRight: 0,
      width: '25%',
      isChild: true,
    },
    {
      fieldName: 'appAlias',
      type: 'input',
      placeholder: '请选择包名称',
      marginLeft: 0,
      marginRight: 0,
      width: '35%',
      isChild: true,
    },
    {
      fieldName: 'appVersion',
      type: 'input',
      placeholder: '请选择版本号',
      marginLeft: 0,
      marginRight: 0,
      width: '39%',
      isChild: true,
    },
    {
      fieldName: 'description',
      label: '备注',
      type: 'textarea',
    },
  ],
};

export const CheckTaskInfoFormConfig2: FormConfig = {
  fields: [
    {
      fieldName: 'packagePriority',
      label: '升级包规则',
      type: 'radioGroup',
      options: [
        {
          label: '仅全量包',
          value: Priority.FULL,
        },
        {
          label: '差分包优先',
          value: Priority.DIFF,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'activePush',
      label: '是否云端主动推送',
      type: 'radioGroup',
      options: [
        {
          label: '是',
          value: ActivePush.YES,
        },
        {
          label: '否',
          value: ActivePush.NO,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'executeType',
      label: '任务执行策略',
      type: 'radioGroup',
      options: [
        {
          label: '提示执行',
          value: ExecuteType.REMIND,
        },
        {
          label: '直接执行',
          value: ExecuteType.DIRECT,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'downloadType',
      label: '移动网络下载',
      type: 'radioGroup',
      options: [
        {
          label: '提示下载',
          value: ExecuteType.REMIND,
        },
        {
          label: '无提示强制下载',
          value: ExecuteType.DIRECT,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'upgradeType',
      label: '安装类型',
      type: 'radioGroup',
      options: [
        {
          label: '提示安装',
          value: UpgradeType.REMIND,
        },
        {
          label: '无提示强制安装',
          value: UpgradeType.FORCE,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      label: '推送时间',
      fieldName: 'timeContainer',
      childrenList: ['immediately', 'issueTime'],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'immediately',
      type: 'radioGroup',
      marginLeft: 0,
      marginRight: 0,
      width: '50%',
      isChild: true,
      options: [
        {
          label: '立即推送',
          value: PushType.IMMEDIATELEY,
        },
        {
          label: '定时推送',
          value: PushType.TIME,
        },
      ],
    },
    {
      fieldName: 'issueTime',
      type: 'input',
      marginLeft: 0,
      marginRight: 0,
      width: '50%',
      isChild: true,
    },
    {
      label: '是否分批执行',
      fieldName: 'batchContainer',
      childrenList: ['batch', 'batchInterval', 'batchCount'],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'batch',
      label: '',
      marginLeft: 0,
      marginRight: 0,
      width: '25%',
      isChild: true,
      disabled: true,
      type: 'radioGroup',
      options: [
        {
          label: '否',
          value: BatchUpgrade.NO,
        },
        {
          label: '是',
          value: BatchUpgrade.YES,
        },
      ],
    },
    {
      fieldName: 'batchInterval',
      label: '批次间隔',
      type: 'select',
      options: batchInterval(24),
      labelInValue: false,
      placeholder: '请选择',
      marginLeft: 0,
      marginRight: 0,
      width: '35%',
      labelCol: { span: 10 },
      wrapperCol: { span: 10 },
      isChild: true,
    },
    {
      fieldName: 'batchCount',
      label: '单次推送数量',
      type: 'inputNumber',
      marginLeft: 0,
      marginRight: 0,
      width: '40%',
      isChild: true,
      labelCol: { span: 10 },
      wrapperCol: { span: 10 },
      placeholder: '请输入',
    },

    {
      label: '升级后完成操作',
      childrenList: ['afterUpgradeName', 'afterUpgradeApps'],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'afterUpgradeName',
      label: '',
      type: 'input',
      marginLeft: 0,
      marginRight: 0,
      width: '50%',
      isChild: true,
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'afterUpgradeApps',
      type: 'input',
      placeholder: '请输入应用包名，英文逗号分隔',
      marginLeft: 0,
      marginRight: 0,
      width: '50%',
      isChild: true,
      validatorRules: [
        {
          required: true,
          message: '请输入应用包名，英文逗号分隔',
        },
      ],
    },
  ],
  linkRules: {
    batch: [
      {
        linkFieldName: 'batchInterval',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
      {
        linkFieldName: 'batchCount',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
    ],
    afterUpgrade: [
      {
        linkFieldName: 'afterUpgradeApps',
        rule: 'visible',
        dependenceData: [
          AfterUpgradeOptions.CLEAR_APP_DATA,
          AfterUpgradeOptions.UNINSTALL,
        ],
      },
    ],
  },
};
