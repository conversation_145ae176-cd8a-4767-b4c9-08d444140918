// 列表页面可选页数
export const pageSizeOptions: string[] = ['10', '20', '30', '40', '100'];

export interface SearchCondition {
  searchForm: any;
  current: number;
  pageSize: number;
}

export interface TableListType {
  list: any[];
  totalNumber: number;
  totalPage: number;
}

export const UpgradeTypeMap = new Map([
  ['SILENT', { key: 0, name: '静默升级' }],
  ['OFFLINE', { key: 2, name: '离线升级' }],
  ['0', { key: 0, name: '静默升级' }],
  ['2', { key: 2, name: '离线升级' }],
]);

export const UpgradeTypeOptions = [
  { value: 0, label: '静默升级' },
  { value: 2, label: '离线升级' },
];

export enum ProductType {
  VEHICLE = 'vehicle',
  ROBOT = 'robot',
}

export enum ProductTypeName {
  VEHICLE = '无人车',
  ROBOT = '机器人',
}

export enum TSLFunctionType {
  Properties = 1,
  Server = 2,
  Event = 3,
}
