import React, {
  useRef,
  useImperativeHandle,
  forwardRef,
  useState,
  useEffect,
} from 'react';
import { message, Tabs, Card } from 'antd';
import './index.scss';
import ProductModuleSelector from '@/components/ProductModuleSelector';
import StructuredConfigEditor from '@/components/StructuredConfigEditor';
import ConfigFileSelector from '@/components/ConfigFileSelector';
import { SelectedData } from '../Configuration';
import { ConfigTemplateApi, Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';

interface EditConfigurationProps {
  selectedConfig: SelectedData;
}

// 第二步：模板信息表单配置
const SecondStepFormConfig = {
  fields: [
    {
      fieldName: 'templateName',
      label: '模板名称',
      type: 'input',
      placeholder: '请输入模板名称',
      validatorRules: [
        { required: true, message: '请输入模板名称' },
        { max: 50, message: '模板名称不能超过50个字符' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
          message: '只支持中文、字母、数字、中划线、下划线',
        },
      ],
      maxLength: 50,
      showCount: true,
    },
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      disabled: true,
      labelInValue: false,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      placeholder: '请选择型号',
      multiple: true,
      labelInValue: false,
      validatorRules: [{ required: true, message: '请选择型号' }],
    },
    {
      fieldName: 'remark',
      label: '备注',
      type: 'textArea',
      placeholder: '请输入备注',
      maxLength: 150,
      showCount: true,
      autoSize: { minRows: 3, maxRows: 5 },
      validatorRules: [{ max: 150, message: '备注不能超过150个字符' }],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) return [];
          const deviceApi = new Device();
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          }
          return [];
        },
      },
    ],
  },
};

const EditConfiguration = forwardRef<
  {
    getTemplateData: () => Promise<any>;
  },
  EditConfigurationProps
>(({ selectedConfig }, ref) => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const productModuleSelectorRef = useRef<any>(null);
  const configFileSelectorRef = useRef<any>(null);

  const [configData, setConfigData] = useState<any[]>([]);
  const [activeBlockNo, setActiveBlockNo] = useState<string>();

  // 存储每个模块的结构化配置编辑器引用
  const structuredConfigEditorsRef = useRef<Record<string, any>>({});

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getTemplateData: async () => {
      try {
        // 1. 获取基本信息和产品模块数据
        const productModuleData =
          await productModuleSelectorRef.current?.getSelectedData();

        // 2. 获取配置文件数据
        const configFileData =
          await configFileSelectorRef.current?.getSelectedData();

        // 3. 构造API参数
        const templateData = {
          templateName: productModuleData.templateName,
          productKey: productModuleData.productKey,
          productModelNoList: productModuleData.productModelNoList,
          remark: productModuleData.remark,
          blockConfigList: configData.map((block) => ({
            blockNo: block.blockNo,
            itemList: Object.keys(
              structuredConfigEditorsRef.current[
                block.blockNo
              ]?.getConfigData() || {},
            ).map((itemNo) => ({
              itemNo,
              content: JSON.stringify(
                structuredConfigEditorsRef.current[
                  block.blockNo
                ]?.getConfigData()?.[itemNo] || {},
              ),
            })),
            fileList: Object.keys(configFileData?.[block.blockNo] || {}).map(
              (fileNo) => ({
                fileNo,
                content: configFileData[block.blockNo][fileNo] || '{}',
              }),
            ),
          })),
        };

        return templateData;
      } catch (error: any) {
        throw new Error(error.message || '请完善必填信息');
      }
    },
  }));

  // 当选中数据变化时，获取配置数据
  useEffect(() => {
    if (selectedConfig?.product?.value) {
      fetchConfigData();
    }
  }, [selectedConfig]);

  const fetchConfigData = async () => {
    try {
      const res = await configTemplateApi.current.getEnableConfigDetailList({
        productKey: selectedConfig.product.value,
        blockConfigList: selectedConfig.blockData.map((v: any) => ({
          blockNo: v.blockNo,
          itemNoList: v.selectedItems,
          fileNoList: v.selectedFiles,
        })),
      });

      if (res.code === HttpStatusCode.Success) {
        setConfigData(res.data || []);
        if (res.data && res.data.length > 0) {
          setActiveBlockNo(res.data[0].blockNo);
        }
      } else {
        message.error(res.message || '获取配置详情失败');
      }
    } catch (error) {
      message.error('获取配置数据失败');
    }
  };

  return (
    <div className="edit-configuration">
      {/* 基本信息 */}
      <Card title="基本信息" className="basic-info-card">
        <ProductModuleSelector
          ref={productModuleSelectorRef}
          selectedData={{
            productKey: selectedConfig?.product?.value,
            selectedModules: selectedConfig?.product?.selectedModules || [],
            templateName: '',
            productModelNoList: [],
            remark: '',
          }}
          needFormConfig={true}
          formConfig={SecondStepFormConfig}
          formType="edit"
          title="模板基本信息"
        />
      </Card>

      {/* 配置编辑 */}
      <Card title="配置编辑" className="config-edit-card">
        {configData.length > 0 ? (
          <Tabs
            activeKey={activeBlockNo}
            onChange={setActiveBlockNo}
            items={configData.map((block) => ({
              key: block.blockNo,
              label: block.blockName,
              children: (
                <div className="block-content">
                  {/* 结构化配置项 */}
                  {block.itemsTypeList && block.itemsTypeList.length > 0 && (
                    <div className="structured-config-section">
                      <h4>结构化配置项</h4>
                      <StructuredConfigEditor
                        ref={(ref) => {
                          if (ref) {
                            structuredConfigEditorsRef.current[block.blockNo] =
                              ref;
                          }
                        }}
                        configTypes={block.itemsTypeList}
                        blockNo={block.blockNo}
                      />
                    </div>
                  )}

                  {/* 如果没有配置项 */}
                  {(!block.itemsTypeList ||
                    block.itemsTypeList.length === 0) && (
                    <div className="empty-content">
                      该模块下暂无选中的结构化配置项
                    </div>
                  )}
                </div>
              ),
            }))}
          />
        ) : (
          <div className="no-data">暂无配置数据</div>
        )}
      </Card>

      {/* 配置文件选择 */}
      <Card title="配置文件" className="config-file-card">
        <ConfigFileSelector
          ref={configFileSelectorRef}
          selectedData={{
            productKey: selectedConfig?.product?.value,
          }}
        />
      </Card>
    </div>
  );
});

EditConfiguration.displayName = 'EditConfiguration';

export default EditConfiguration;
