import React, { useState, useEffect, useRef } from 'react';
import CommonTable from '@/components/CommonTable';
import CommonForm from '@/components/CommonForm';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useTableData } from '@/components/CommonTable/useTableData';
import { ProductTableData, SearchForm } from './utils/column';
import showModal from '@/components/commonModal';
import ProductManageFetch from '@/fetch/bussiness/productManage';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import TagLabel from '@/components/TagLabel';

const ProductManage = () => {
  const [searchConf, setSearchConf] = useState<FormConfig>(SearchForm);
  const [update, setUpdate] = useState(1);
  const fetchApi = new ProductManageFetch();
  const TSLFetchApi = new TSLModelFetch();
  const navigator = useNavigate();
  const searchFormRef = useRef<any>(null);
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );
  const initSearchCondition = {
    productKey: null,
    tagList: null,
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValue.searchValues
      ? historySearchValue.searchValues
      : initSearchCondition;
  });
  const { tableData, loading } = useTableData(
    searchCondition,
    fetchApi.getProduct,
    update,
  );
  useEffect(() => {
    const custormField = searchConf.fields.find(
      (item: FieldItem) => item.fieldName === 'tagList',
    );
    custormField!.renderFunc = () => (
      <TagLabel
        modalTitle="添加产品标签"
        placeholder="请选择产品标签"
        onChange={(val) => {
          // console.log(val);
        }}
      />
    );
    setSearchConf({
      ...searchConf,
    });
    getProductOptions();
  }, []);

  const getProductOptions = async () => {
    const list = await TSLFetchApi.getProductList();
    const custormField = searchConf.fields.find(
      (item: FieldItem) => item.fieldName === 'productKey',
    );
    custormField!.options = list;
    setSearchConf({
      ...searchConf,
    });
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '创建产品',
      key: 'addProduct',
      onClick: () => navigator('/product/AddProduct'),
    },
  ];

  const formatColumns = () => {
    return ProductTableData?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    navigator(
                      `/product/ProductDetail?productKey=${record.productKey}`,
                    );
                  }}
                >
                  详情信息
                </a>
                <a
                  onClick={() => {
                    navigator(
                      `/product/TSLModel?productKey=${record.productKey}`,
                    );
                  }}
                >
                  物模型
                </a>
                <a
                  onClick={() => {
                    navigator(
                      `/device?productKey=${record.productKey}`,
                    );
                  }}
                >
                  关联设备
                </a>
                <a
                  onClick={() => {
                    delProduct(record.productKey);
                  }}
                >
                  删除
                </a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const delProduct = (productKey: any) => {
    showModal({
      title: '',
      content: '确认删除产品',
      type: 'confirm',
      onOk: (cb: any) => {
        fetchApi.delProduct({ productKey }).then((res: any) => {
          if (res.code === HttpStatusCode.Success) {
            message.success(res.message);
            setUpdate(Date.now());
          } else {
            message.warning(res.missage);
          }
          cb();
        });
      },
      onCancel: () => {},
    });
  };

  const onSearchClick = () => {
    const val = searchFormRef.current?.getFieldsValue();
    val.tagList = val.tagList?.map((v: any) => `${v.name}/${v.value}`);
    setUpdate(Date.now());
    setSearchCondition({
      ...searchCondition,
      ...val,
    });
  };
  const onResetClick = () => {
    searchFormRef.current?.resetFields();
    setUpdate(Date.now());
    setSearchCondition(initSearchCondition);
  };

  return (
    <div className="product-manage">
      <CommonForm
        formConfig={searchConf}
        formType="search"
        layout={'inline'}
        onResetClick={onResetClick}
        onSearchClick={onSearchClick}
        getFormInstance={(formRef: any) => {
          searchFormRef.current = formRef;
        }}
      />

      <CommonTable
        tableKey={'product-tableKey'}
        searchCondition={searchCondition}
        loading={false}
        scrollY={500}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        columns={formatColumns()}
        middleBtns={middleBtns}
        rowKey={'productKey'}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      ></CommonTable>
    </div>
  );
};

export default React.memo(ProductManage);
