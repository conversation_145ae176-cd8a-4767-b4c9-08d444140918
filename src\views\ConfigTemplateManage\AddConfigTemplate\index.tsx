import React, { useRef, useState } from 'react';
import { message } from 'antd';
import './index.scss';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import CommonSteps from '@/components/CommonSteps';
import BreadCrumb from '@/components/BreadCrumb';
import ProductModuleSelector from '@/components/ProductModuleSelector';
import StructuredConfigSelector from '@/components/StructuredConfigSelector';
import ConfigFileSelector from '@/components/ConfigFileSelector';
import ResultFeedback, { ButtonConfig } from '@/components/ResultFeedback';

export interface SelectedData {
  productKey?: string;
  productModelNoList?: string[];
  selectedConfigs?: {
    blockNo: string;
    selectedItems: string[];
    selectedFiles: string[];
  }[];
}

const AddConfigTemplate: React.FC = () => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const productModuleSelectorRef = useRef<any>(null);
  const structuredConfigSelectorRef = useRef<any>(null);
  const configFileSelectorRef = useRef<any>(null);

  const [selectedData, setSelectedData] = useState<SelectedData>();
  const [isSubmitSuccess, setIsSubmitSuccess] = useState<boolean>(false);

  // 第一步：选择产品及模块
  const checkStepOne = async () => {
    try {
      const productModuleData =
        await productModuleSelectorRef.current?.getSelectedData();
      if (!productModuleData?.productKey) {
        message.error('请选择产品');
        return false;
      }
      if (!productModuleData?.selectedModules?.length) {
        message.error('请选择至少一个模块');
        return false;
      }

      // 更新选中数据
      setSelectedData((prev) => ({
        ...prev,
        productKey: productModuleData.productKey,
        productModelNoList: productModuleData.selectedModules,
      }));

      return true;
    } catch (error: any) {
      message.error(error.message || '请完善产品和模块选择');
      return false;
    }
  };

  // 第二步：选择配置项和配置文件
  const checkStepTwo = async () => {
    try {
      const structuredConfigs =
        await structuredConfigSelectorRef.current?.getSelectedData();
      const configFiles =
        await configFileSelectorRef.current?.getSelectedData();

      // 合并配置项和配置文件数据
      const allBlockNos = new Set([
        ...Object.keys(structuredConfigs || {}),
        ...Object.keys(configFiles || {}),
      ]);

      if (allBlockNos.size === 0) {
        message.error('请至少选择一个配置项或配置文件');
        return false;
      }

      const selectedConfigs = Array.from(allBlockNos).map((blockNo) => ({
        blockNo,
        selectedItems: structuredConfigs?.[blockNo] || [],
        selectedFiles: configFiles?.[blockNo] || [],
      }));

      setSelectedData((prev) => ({
        ...prev!,
        selectedConfigs,
      }));

      return true;
    } catch (error: any) {
      message.error(error.message || '请选择配置项或配置文件');
      return false;
    }
  };

  // 提交创建配置模板
  const handleSubmit = async () => {
    try {
      if (
        !selectedData ||
        !selectedData.productKey ||
        !selectedData.productModelNoList ||
        !selectedData.selectedConfigs
      ) {
        message.error('数据异常，请重新操作');
        return false;
      }

      // 构造API参数
      const params = {
        productKey: selectedData.productKey,
        productModelNoList: selectedData.productModelNoList,
        templateNo: `TPL_${Date.now()}`, // 临时生成模板编号
        templateName: `配置模板_${new Date().toLocaleString()}`, // 临时生成模板名称
        remark: '通过向导创建的配置模板',
        blockConfigList: selectedData.selectedConfigs.map((config) => ({
          blockNo: config.blockNo,
          itemList: config.selectedItems.map((itemNo) => ({
            itemNo,
            content: '{}', // 第一步只是选择，内容在第二步编辑
          })),
          fileList: config.selectedFiles.map((fileNo) => ({
            fileNo,
            content: '{}', // 第一步只是选择，内容在第二步编辑
          })),
        })),
      };

      const res = await configTemplateApi.current.addConfigTemplate(params);
      if (res.code === HttpStatusCode.Success) {
        setIsSubmitSuccess(true);
        message.success('配置模板创建成功');
        return true;
      } else {
        setIsSubmitSuccess(false);
        message.error(res.message || '创建失败');
        return false;
      }
    } catch (error: any) {
      setIsSubmitSuccess(false);
      message.error(error.message || '创建失败');
      return false;
    }
  };

  const resultButton: ButtonConfig[] = [
    {
      text: '返回列表',
      clickFunc: () => {
        window.location.href = '/ota/configTemplate';
      },
      showScope: ['success', 'fail'],
    },
    {
      text: '继续创建',
      clickFunc: () => {
        window.location.reload();
      },
      showScope: ['success', 'fail'],
    },
  ];

  return (
    <>
      <BreadCrumb
        items={[
          { title: '通用设备管理', route: '' },
          { title: '配置模板管理', route: '/ota/configTemplate' },
          { title: '创建配置模板', route: '' },
        ]}
      />
      <div className="add-config-template-content">
        <CommonSteps
          stepTipList={[
            '分模块选择配置项和配置文件',
            '分模块编辑配置模板',
            '完成创建',
          ]}
          children={[
            <div className="step-container">
              <ProductModuleSelector
                ref={productModuleSelectorRef}
                selectedData={selectedData}
              />
              <StructuredConfigSelector
                ref={structuredConfigSelectorRef}
                selectedData={selectedData}
              />
              <ConfigFileSelector
                ref={configFileSelectorRef}
                selectedData={selectedData}
              />
            </div>,
            <div className="step-container">
              <div className="edit-placeholder">
                <h3>配置模板编辑</h3>
                <p>
                  此步骤将在后续版本中实现，用于编辑配置项参数内容和配置文件参数
                </p>
                <p>当前选中的数据：</p>
                <pre>{JSON.stringify(selectedData, null, 2)}</pre>
              </div>
            </div>,
            <ResultFeedback
              result={isSubmitSuccess}
              buttonConfig={resultButton}
              resultMessage={{
                success: '配置模板创建成功！',
                fail: '配置模板创建失败，请重试',
              }}
            />,
          ]}
          onNextCheckList={[checkStepOne, checkStepTwo]}
          onSubmit={handleSubmit}
        />
      </div>
    </>
  );
};

export default AddConfigTemplate;
