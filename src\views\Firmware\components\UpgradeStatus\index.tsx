import React from 'react';
import './index.scss';
export enum Status {
  stop = -2,
  cancel = -1,
  to_be_effective = 0,
  received = 1,
  downloading = 2,
  download_success = 3,
  installing = 4,
  install_success = 5,
  install_fail = 6,
  creating = 7,
  create_fail = 8,
  already_effective = 9,
  download_fail = 10,
}

const UpgradeStatus = ({ status }: { status: Status }) => {
  const styleMap = new Map([
    [
      Status.cancel,
      [
        { color: '#8A8B8B', text: '取消推送' },
        { color: '#8A8B8B', text: '未接收' },
        { color: '#8A8B8B', text: '未下载' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.to_be_effective,
      [
        { color: '#43A1FF', text: '待生效' },
        { color: '#8A8B8B', text: '未接收' },
        { color: '#8A8B8B', text: '未下载' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.already_effective,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#8A8B8B', text: '未接收' },
        { color: '#8A8B8B', text: '未下载' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.received,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#8A8B8B', text: '未下载' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.downloading,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#F78E20', text: '下载中' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.download_success,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#42CF7D', text: '下载完成' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.download_fail,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#D53436', text: '下载失败' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.installing,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#42CF7D', text: '下载完成' },
        { color: '#F78E20', text: '安装中' },
      ],
    ],
    [
      Status.install_success,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#42CF7D', text: '下载完成' },
        { color: '#42CF7D', text: '安装完成' },
      ],
    ],
    [
      Status.install_fail,
      [
        { color: '#42CF7D', text: '已生效' },
        { color: '#42CF7D', text: '已接收' },
        { color: '#42CF7D', text: '下载完成' },
        { color: '#D53436', text: '安装失败' },
      ],
    ],
    [
      Status.creating,
      [
        { color: '#F78E20', text: '创建中' },
        { color: '#8A8B8B', text: '未接收' },
        { color: '#8A8B8B', text: '未下载' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
    [
      Status.create_fail,
      [
        { color: '#D53436', text: '创建失败' },
        { color: '#8A8B8B', text: '未接收' },
        { color: '#8A8B8B', text: '未下载' },
        { color: '#8A8B8B', text: '未安装' },
      ],
    ],
  ]);

  return (
    <div className="device-status-container">
      {status === Status.stop ? (
        <span style={{ color: 'red' }}>已终止</span>
      ) : (
        <>
          {styleMap.get(status)?.map((v: any, i: number) => {
            return (
              <div className="line-container" key={i}>
                <div
                  className="line"
                  style={{ backgroundColor: v.color }}
                ></div>
                <span>{v.text}</span>
              </div>
            );
          })}
        </>
      )}
    </div>
  );
};
export default React.memo(UpgradeStatus);
