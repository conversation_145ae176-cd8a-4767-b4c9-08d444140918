import React, { useEffect, useState } from 'react';
import {
  Button,
  Col,
  Form,
  Input,
  Row,
  Select,
  Upload,
  UploadProps,
  message,
} from 'antd';
import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/util';
import { HttpStatusCode } from '@/fetch/core/constant';
import dayjs from 'dayjs';
import Device from '@/fetch/bussiness/device';
const fetchApi = new Device();
const UploadDeviceFile = (props: {
  saveFile: (file: any) => void;
  getFormInstance: (formRef: any) => void;
}) => {
  const [form] = Form.useForm();
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [productList, setProductList] = useState<
    { label: string; value: string }[]
  >([]);
  const uploadProps: UploadProps = {
    maxCount: 1,
    progress: {
      strokeColor: {
        '0%': '#108ee9',
        '100%': '#87d068',
      },
      strokeWidth: 3,
      format: (percent) => percent && `${parseFloat(percent.toFixed(2))}%`,
    },
    beforeUpload: (file: any) => {
      const fileSize = file.size / 1024 / 1024 < 200;
      if (!fileSize) {
        message.warning('文件大小不能超过200MB');
      }

      return fileSize || Upload.LIST_IGNORE;
    },
    customRequest: async (option: any) => {
      const { file, onSuccess } = option;
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = async () => {
        request({
          path: '/k2/oss/upload',
          method: 'POST',
          body: {
            fileKey: `导入模板_${Date.now()}.xlsx`,
            bucketName: 'rover-operation',
          },
          newGeteway: true,
        }).then((res: any) => {
          const { uploadUrl, bucketName, fileKey } = res.data;
          request({
            absoluteURL: uploadUrl,
            contentType: 'multipart/form-data',
            method: 'PUT',
            body: file,
            timeout: 60000,
            newGeteway: true,
          }).then((res) => {
            props.saveFile({
              bucketName,
              fileKey,
            });
            onSuccess(res, file);
          });
        });
      };
    },
  };

  const getProductList = () => {
    fetchApi.queryProductList().then((res: any) => {
      if (res.code === HttpStatusCode.Success) {
        const pList =
          res?.data?.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
        setProductList(pList);
      }
    });
  };
  useEffect(() => {
    request({
      path: '/intelligent/device/web/device/get_batch_add_device_file',
      method: Method.POST,
      newGeteway: true,
    }).then((res: any) => {
      if (res?.code === HttpStatusCode.Success) {
        setDownloadUrl(res?.data?.deviceBatchAddExcelDownloadUrl);
      }
    });
    props.getFormInstance(form);
    getProductList();
  }, []);
  return (
    <div className="upload-file-wrapper">
      <Form form={form}>
        <Form.Item name="templete" label="下载模板">
          <a
            href={downloadUrl}
            download={`导入模板_${dayjs().format('YYYY-MM-DD HH-mm-ss')}.xlsx`}
            onClick={() => {
              window.location.href = downloadUrl;
            }}
          >
            导入模板
          </a>
        </Form.Item>
        <Form.Item
          name="productKey"
          label="产品"
          rules={[{ required: true, message: '请选择产品' }]}
        >
          <Select
            placeholder={'请选择产品'}
            options={productList}
            showSearch={true}
            labelInValue={false}
            allowClear={true}
            filterOption={(
              input: string,
              option?: { label: string; value: string },
            ) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>
        <Form.Item label="上传文件">
          <Row>
            <Col>
              <Form.Item name="file" label="">
                <Input disabled placeholder="选择文件" />
              </Form.Item>
            </Col>
            <Col>
              <Upload
                {...uploadProps}
                accept=".xlsx,.xls"
                name="file"
                // showUploadList={false}
                // beforeUpload={(file) => {
                //   setChoosedFile(file);
                //   form.setFieldsValue({
                //     file: file.name,
                //   });
                //   props.saveFile(file);
                //   return false;
                // }}
              >
                <Button style={{ marginLeft: '8px' }}>添加文件</Button>
              </Upload>
            </Col>
          </Row>
        </Form.Item>
      </Form>
    </div>
  );
};

export default UploadDeviceFile;
