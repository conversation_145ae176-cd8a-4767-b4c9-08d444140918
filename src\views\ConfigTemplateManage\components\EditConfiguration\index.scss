.configuration-page {
  background: #fff;
  padding: 24px;
  min-height: 100%;

  .search-item {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;

    .label {
      font-weight: 500;
    }
  }

  .content-container {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;

    .structured-config {
      .ant-collapse {
        background: transparent;

        // Override default Ant Design collapse styles
        .ant-collapse-expand-icon {
          font-size: 16px;
          color: #666;

          // Ensure the icon is properly aligned
          > span {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        // Ensure the header content takes full width
        .ant-collapse-header-text {
          flex: 1;
        }

        .ant-collapse-item {
          margin-bottom: 16px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #f0f0f0;

          &:last-child {
            margin-bottom: 0;
          }

          .ant-collapse-header {
            padding: 0 !important;
            position: relative;

            .ant-collapse-expand-icon {
              position: absolute !important;
              right: 16px;
              top: 50%;
              transform: translateY(-50%);
              z-index: 1;
            }

            .type-header {
              padding: 16px;
              background: #fafafa;
              border-radius: 4px 4px 0 0;
              display: flex;
              align-items: center;

              h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                color: #333;
              }
            }
          }

          .ant-collapse-content {
            .ant-collapse-content-box {
              padding: 0;
            }
          }

          .config-items-list {
            .config-item-section {
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .item-header {
                padding: 12px 16px;
                background: #f5f5f5;
                border-bottom: 1px solid #f0f0f0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .item-name {
                  font-size: 14px;
                  font-weight: 500;
                  color: #666;
                }

                .expand-icon {
                  font-size: 12px;
                  color: #666;

                  .icon-down,
                  .icon-right {
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    text-align: center;
                    font-size: 10px;
                  }
                }
              }

              .item-content {
                padding: 16px;

                .ant-form-item {
                  margin-bottom: 16px;

                  &:last-child {
                    margin-bottom: 0;
                  }
                }

                .config-item-label {
                  display: flex;
                  align-items: center;
                  gap: 4px;



                  .help-icon {
                    color: #8c8c8c;
                    cursor: pointer;
                    font-size: 12px;
                    margin-left: 4px;
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    text-align: center;
                    border-radius: 50%;
                    border: 1px solid #d9d9d9;
                  }
                }

                .config-item-content {
                  display: flex;
                  align-items: flex-start;
                  gap: 8px;
                }
              }
            }
          }
        }
      }
    }

    .file-section {
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      .section-header {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
      }

      .search-bar {
        margin-bottom: 16px;
      }

      .table-info {
        margin-bottom: 16px;
        color: #666;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-tabs-nav {
    margin-bottom: 0;
  }

  // 数组相关样式
  .array-container {
    .array-row {
      display: flex !important; /* 强制使用flex布局 */
      flex-direction: row !important; /* 强制水平排列 */
      align-items: center !important; /* 垂直居中 */
      justify-content: flex-start !important; /* 从左到右排列 */
      gap: 8px;
      margin-bottom: 12px;
      width: 100%;
      flex-wrap: nowrap !important; /* 禁止换行 */
    }

    .array-sequence {
      width: 24px;
      height: 24px;
      min-width: 24px;
      background-color: #f0f0f0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
      flex-shrink: 0;
      margin-right: 4px;
    }

    .array-input {
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
      overflow: hidden; /* 防止溢出 */

      /* 确保输入框内容正确显示 */
      .ant-input,
      .ant-input-number,
      .ant-select,
      .ant-picker {
        width: 100%;
      }

      /* 确保输入框组件垂直居中 */
      display: flex;
      align-items: center;
    }

    .array-delete-btn {
      padding: 0 8px;
      height: 32px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      margin-left: 4px;
      white-space: nowrap;
      min-width: 50px; /* 确保按钮有最小宽度 */
      justify-content: center; /* 文字居中 */

      &:hover {
        color: #ff4d4f;
        background-color: rgba(255, 77, 79, 0.1);
      }
    }

    .array-add-btn-container {
      display: flex;
      align-items: center;
      margin-top: 12px;
      padding-left: 28px; // 与序号对齐

      .array-add-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        border-color: #d9d9d9;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        span {
          margin-right: 4px;
          font-weight: bold;
        }
      }

      .array-limit-hint {
        margin-left: 8px;
        color: #999;
        font-size: 12px;
        flex-shrink: 0;
      }
    }
  }
}
