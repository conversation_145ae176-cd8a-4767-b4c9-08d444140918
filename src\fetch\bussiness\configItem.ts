import { request } from '@/fetch/core';

class ConfigItemRequest {
  /**
   * 获取配置项分页列表
   * @param {object} params 查询参数
   * @return {Promise}
   */
  public getConfigItemPage = (params: any): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_item/get_config_item_page`,
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 获取配置项详情
   * @param {string} itemNo 配置项编号
   * @return {Promise}
   */
  public getConfigItemDetail = (itemNo: string): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: `/intelligent/device/web/config_item/get_config_item_detail`,
      urlParams: {
        itemNo,
      },
    };
    return request(requestOptions);
  };

  /**
   * 新增配置项
   * @param {object} params 配置项信息
   * @return {Promise}
   */
  public addConfigItem = (params: any): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_item/add_config_item`,
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 更新配置项
   * @param {object} params 配置项信息
   * @return {Promise}
   */
  public updateConfigItem = (params: any): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_item/update_config_item`,
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 删除配置项
   * @param {string} itemNo 配置项编号
   * @return {Promise}
   */
  public deleteConfigItem = (itemNo: string): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'DELETE',
      path: `/intelligent/device/web/config_item/delete_config_item`,
      urlParams: {
        itemNo,
      },
    };
    return request(requestOptions);
  };

  /**
   * 启用/禁用配置项
   * @param {string} itemNo 配置项编号
   * @param {number} enable 状态：0-禁用，1-启用
   * @return {Promise}
   */
  public updateConfigItemStatus = (itemNo: string, enable: number): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'PUT',
      path: `/intelligent/device/web/config_item/update_config_item_status`,
      body: {
        itemNo,
        enable,
      },
    };
    return request(requestOptions);
  };
}

export default ConfigItemRequest;
