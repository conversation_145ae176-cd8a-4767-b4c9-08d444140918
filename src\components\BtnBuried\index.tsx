import React from 'react';
import { Button } from 'antd';

interface Props {
  show: boolean;
  handleClick: Function;
  title: string;
  clstagKey?: string;
}
export const TableBtnBuried = ({
  show,
  handleClick,
  title,
  clstagKey,
}: {
  show: boolean;
  handleClick: Function;
  title: string;
  clstagKey?: string;
}) => {
  return (
    <>
      {show && (
        <a
          clstag={`h|keycount|${clstagKey}`}
          style={{ marginRight: '5px', marginLeft: '5px' }}
          onClick={() => {
            handleClick && handleClick();
          }}
        >
          {title}
        </a>
      )}
    </>
  );
};

export enum ButtonType {
  PrimaryButton = 'primaryButton', // primary background color + text white color + 4px radius
  DefaultButton = 'defaultButton', //  white background color + text "text-normal-color" color + 4px radius
  WarnningButton = 'warningButton', //  red background color + text white color + 4px radius
}

const textColorConfig = {
  primaryButton: 'white',
  defaultButton: '#333',
  warningButton: 'white',
};

const backgroundColorConfig = {
  primaryButton: 'rgb(60, 110, 240)',
  defaultButton: 'white',
  warningButton: '#D9001B',
};

export const BtnBuried = ({
  title,
  buttonType = ButtonType.PrimaryButton,
  disable,
  loading,
  onSubmitClick,
  height = 40,
  otherCSSProperties, // possible override {height、textColor、backgroundColor}
  clstagKey,
  show = true,
}: {
  title: string;
  buttonType?: ButtonType;
  disable?: boolean;
  loading?: boolean;
  onSubmitClick?: Function;
  height?: number;
  otherCSSProperties?: React.CSSProperties;
  clstagKey?: string;
  show?: boolean;
}) => {
  const primaryBtnStyle = (height: number): React.CSSProperties => {
    return {
      backgroundColor: backgroundColorConfig[buttonType],
      color: textColorConfig[buttonType],
      height: height,
      minWidth: 80,
      borderRadius: 4,
      ...otherCSSProperties,
    };
  };
  return (
    <>
      {show && (
        <Button
          className="my_button"
          loading={loading}
          disabled={disable}
          style={primaryBtnStyle(height)}
          onClick={() => {
            onSubmitClick ? onSubmitClick() : null;
          }}
          clstag={`h|keycount|${clstagKey}`}
        >
          {title}
        </Button>
      )}
    </>
  );
};
