import { DataType } from './constant';

interface DataTypeFormat {
  [DataType.INT]: {
    dataType: any;
    max: any;
    min: any;
    step: any;
    unit: any;
    unitName: any;
  };
  [DataType.DOUBLE]: {
    dataType: any;
    max: any;
    min: any;
    step: any;
    unit: any;
    unitName: any;
  };
  [DataType.LONG]: {
    dataType: any;
    max: any;
    min: any;
    step: any;
    unit: any;
    unitName: any;
  };
  [DataType.DATE]: {
    dataType: any;
    length: number;
  };
  [DataType.TEXT]: {
    dataType: any;
    length: number;
  };
  [DataType.ENUM]: {
    dataType: any;
    name: string;
    value: string | number;
  }[];
  [DataType.INT_ENUM]: {
    dataType: any;
    name: string;
    value: string | number;
  }[];
  [DataType.BOOL]: {
    dataType: any;
    name: string;
    value: string | number;
  }[];
  [DataType.ARRAY]: {
    childDataType:
      | DataType.INT
      | DataType.DOUBLE
      | DataType.LONG
      | DataType.TEXT
      | DataType.STRUCT;
    dataType: DataType.ARRAY;
    size: number;
    dataSpecsList?: any[];
  };
}

interface OriginalVal {
  dataType: keyof DataTypeFormat;
}
export const formatTSLData = (originalVal: OriginalVal) => {
  const dataTypeFormat = {
    [DataType.INT]: formatNumber(originalVal),
    [DataType.LONG]: formatNumber(originalVal),
    [DataType.DOUBLE]: formatNumber(originalVal),
    [DataType.DATE]: formatTextDate(originalVal),
    [DataType.TEXT]: formatTextDate(originalVal),
    [DataType.ENUM]: formatEnum(originalVal),
    [DataType.INT_ENUM]: formatIntEnum(originalVal),
    [DataType.BOOL]: formatBool(originalVal),
    [DataType.ARRAY]: formatArray(originalVal),
  };
  return dataTypeFormat[originalVal.dataType];
};

export const formatStruct = (originalVal: any) => {
  const nodeMap = new Map();
  for (let i = 0; i < originalVal.length; i++) {
    const eachLevelData = Object.values(originalVal[i]);
    const eachLevelKeys = Object.keys(originalVal[i]);
    for (let j = 0; j < eachLevelData.length; j++) {
      let node: any = eachLevelData[j];
      let formatVal: any;
      if (node.dataType === DataType.STRUCT) {
        if (node.JSONObject) {
          const json = JSON.parse(node.JSONObject);
          nodeMap.set(eachLevelKeys[j], {
            dataType: node.dataType,
            identifier: node.identifier,
            name: node.name,
            childDataType: node.childDataType ?? DataType.STRUCT,
            flag: 'JSONObject',
            dataSpecsList: json,
          });
        } else {
          formatVal =
            i === 0
              ? {
                  dataType: node.dataType,
                  identifier: node.identifier,
                  name: node.name,
                  dataSpecsList: node.dataSpecsList,
                }
              : {
                  dataType: node.dataType,
                  identifier: node.identifier,
                  name: node.name,
                  childDataType: node.childDataType ?? DataType.STRUCT,
                  dataSpecsList: node.dataSpecsList,
                };
          nodeMap.set(eachLevelKeys[j], formatVal);
        }
      } else if (
        node.dataType === DataType.ARRAY &&
        node.childDataType === DataType.STRUCT
      ) {
        formatVal =
          i - 1 < 0
            ? {
                dataType: node.dataType,
                identifier: node.identifier,
                name: node.name,
                dataSpecs: {
                  size: node.size,
                  dataType: node.dataType,
                  childDataType: node.childDataType,
                },
              }
            : {
                dataType: DataType.STRUCT,
                identifier: node.identifier,
                name: node.name,
                childDataType: node.dataType,
                dataSpecs: {
                  size: node.size,
                  dataType: node.dataType,
                  childDataType: node.childDataType,
                },
              };
        node.childDataType === DataType.STRUCT
          ? (formatVal.dataSpecs.dataSpecsList = node.dataSpecsList)
          : (formatVal.dataSpecs = {
              ...formatVal.dataSpecs,
              ...node,
            });
        nodeMap.set(eachLevelKeys[j], formatVal);
      } else {
        formatVal = [DataType.ENUM, DataType.INT_ENUM, DataType.BOOL].includes(
          node.dataType,
        )
          ? {
              dataType: 'STRUCT',
              identifier: node.identifier,
              name: node.name,
              childDataType: node.dataType,
              dataSpecsList: formatTSLData(node),
            }
          : {
              dataType: 'STRUCT',
              identifier: node.identifier,
              name: node.name,
              childDataType: node.dataType,
              dataSpecs: formatTSLData(node),
            };
        nodeMap.set(eachLevelKeys[j], formatVal);
      }
    }
  }

  nodeMap.forEach((node) => {
    if (
      (node.childDataType &&
        node.childDataType === DataType.ARRAY &&
        node.dataSpecs.dataSpecsList) ||
      (!node.childDataType &&
        node.dataType === DataType.ARRAY &&
        node.dataSpecs.dataSpecsList)
    ) {
      // 当前项的数据类型是数组
      node.dataSpecs.dataSpecsList = node.dataSpecs.dataSpecsList.map(
        (key: any) => nodeMap.get(key),
      );
    } else if (
      (node.dataSpecsList &&
        node.childDataType &&
        node.childDataType === DataType.STRUCT) ||
      (!node.childDataType && node.dataType === DataType.STRUCT)
    ) {
      // 当前项的数据类型是结构体
      if (node.flag === 'JSONObject') {
        delete node.flag;
      } else {
        node.dataSpecsList = node.dataSpecsList?.map((key: any) => {
          return nodeMap.get(key);
        });
      }
    }
  });

  return Object.keys(originalVal[0]).map((v: any) => {
    return nodeMap.get(v);
  });
};

const formatArray = (originalVal: any) => {
  if (originalVal.childDataType !== DataType.STRUCT) {
    return {
      dataType: originalVal.dataType,
      childDataType: originalVal.childDataType,
      size: originalVal.size,
    };
  } else {
    return {
      dataType: originalVal.dataType,
      childDataType: originalVal.childDataType,
      size: originalVal.size,
      dataSpecsList: originalVal.dataSpecsList,
    };
  }
};

const formatNumber = (originalVal: any) => {
  return {
    dataType: originalVal.dataType,
    max: originalVal.max ?? '',
    min: originalVal.min ?? '',
    step: originalVal.step ?? '',
    unit: originalVal.unit ?? '',
    unitName: originalVal.unitName ?? '',
  };
};
const formatTextDate = (originalVal: any) => {
  return {
    dataType: originalVal.dataType,
    length: originalVal.dataType === DataType.DATE ? 23 : originalVal.length,
  };
};

const formatEnum = (originalVal: any) => {
  return originalVal?.enumList?.map((v: any) => {
    return {
      dataType: v.dataType,
      name: v.name,
      value: v.value,
    };
  });
};

const formatIntEnum = (originalVal: any) => {
  return originalVal?.enumList?.map((v: any) => {
    return {
      dataType: v.dataType,
      name: v.name,
      value: Number(v.value),
    };
  });
};
const formatBool = (originalVal: any) => {
  return [
    {
      dataType: originalVal.dataType,
      name: originalVal['name-0'],
      value: 0,
    },
    {
      dataType: originalVal.dataType,
      name: originalVal['name-1'],
      value: 1,
    },
  ];
};

const formatDataSpacsList = (data: any) => {
  if (data.dataType === DataType.BOOL) {
    return {
      name: data.name,
      identifier: data.identifier,
      dataType: data.dataType,
      ['name-0']: data.dataSpecsList[0].name,
      ['name-1']: data.dataSpecsList[1].name,
    };
  } else {
    return {
      name: data.name,
      identifier: data.identifier,
      dataType: data.dataType,
      enumList: data.dataSpecsList.map((v: any, i: number) => ({
        ...v,
        key: `${Date.now()}${i}`,
      })),
    };
  }
};

export const formatThingModel = (data: any, result: any[] = []) => {
  if (!data) {
    return result;
  }
  if (
    data.dataType !== DataType.STRUCT &&
    data.childDataType !== DataType.STRUCT &&
    data.dataType !== DataType.ARRAY
  ) {
    return [
      {
        [data.identifier]: data.dataSpecs
          ? {
              ...data.dataSpecs,
              name: data.name,
              identifier: data.identifier,
              dataType: data.dataType,
            }
          : formatDataSpacsList(data),
      },
    ];
  }

  const queue = [data];
  while (queue.length > 0) {
    const levelSize = queue.length;
    const levelNodes: AnyObj = {};
    for (let i = 0; i < levelSize; i++) {
      const node = queue.shift();
      let formatData = null;
      if (
        (node.dataSpecsList &&
          node.childDataType &&
          node.childDataType === DataType.STRUCT) ||
        (!node.childDataType && node.dataType === DataType.STRUCT)
      ) {
        if (result.length < 5) {
          formatData = {
            name: node.name,
            identifier: node.identifier,
            dataType: node.dataType,
            dataSpecsList: node.dataSpecsList.map((v: any) => v.identifier),
          };
        } else {
          formatData = {
            name: node.name,
            identifier: node.identifier,
            dataType: node.dataType,
            JSONObject: JSON.stringify(node.dataSpecsList),
          };
          levelNodes[node.identifier] = formatData;
          break;
        }
      } else if (
        node.dataSpecs?.dataSpecsList &&
        node.dataSpecs.childDataType === DataType.STRUCT
      ) {
        formatData = {
          name: node.name,
          identifier: node.identifier,
          dataType: node.dataSpecs.dataType,
          size: node.dataSpecs.size,
          childDataType: node.dataSpecs.childDataType,
          dataSpecsList: node.dataSpecs.dataSpecsList.map(
            (v: any) => v.identifier,
          ),
        };
      } else if (node.dataSpecsList && node.childDataType === DataType.BOOL) {
        formatData = {
          name: node.name,
          identifier: node.identifier,
          dataType: node.childDataType,
          'name-0': node.dataSpecsList.find((v: any) => v.value === 0).name,
          'name-1': node.dataSpecsList.find((v: any) => v.value === 1).name,
        };
      } else if (
        node.childDataType === DataType.ENUM ||
        node.childDataType === DataType.INT_ENUM
      ) {
        formatData = {
          name: node.name,
          identifier: node.identifier,
          dataType: node.childDataType,
          enumList: node.dataSpecsList.map((v: any, i: number) => ({
            ...v,
            key: `${Date.now()}${i}`,
          })),
        };
      } else {
        formatData = node.dataSpecsList
          ? {
              name: node.name,
              identifier: node.identifier,
              dataType: node.childDataType,
              dataSpecsList: node.dataSpecsList,
            }
          : {
              name: node.name,
              identifier: node.identifier,
              dataType: node.childDataType,
              ...node.dataSpecs,
            };
      }
      levelNodes[node.identifier] = formatData;
      if (
        (node?.dataSpecsList &&
          node.childDataType &&
          node.childDataType === DataType.STRUCT) ||
        (!node.childDataType && node.dataType === DataType.STRUCT)
      ) {
        queue.push(...node.dataSpecsList);
      } else if (
        node.dataSpecs?.dataSpecsList &&
        node.dataSpecs.childDataType === DataType.STRUCT
      ) {
        queue.push(...node.dataSpecs.dataSpecsList);
      }
    }
    result.push(levelNodes);
  }
  return result;
};
