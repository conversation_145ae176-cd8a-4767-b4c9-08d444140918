import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { request } from '@/fetch/core';
import { Button, Form, Upload, UploadProps, message } from 'antd';
import { CommandControlFetch, checkUploadDevice } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isEmpty } from '@/utils/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import './index.scss';
const SparkMD5 = require('spark-md5');
const fetchApi = new CommandControlFetch();
const FileUpload = (props: {
  productKey: string;
  modelList: string[];
  uploadChange: (uploadResult: any) => void;
}) => {
  const [fileKey, setFileKey] = useState<{
    deviceNameFileS3Key: string;
    deviceNameFileS3BucketName: string;
    deviceNameFileMd5: string;
  }>({
    deviceNameFileS3Key: '',
    deviceNameFileS3BucketName: '',
    deviceNameFileMd5: '',
  });
  const [uploadRes, setUploadRes] = useState<any>({
    success: false,
    failUrl: null,
    total: 0,
  });

  const checkDevice = async (params: {
    fileS3BucketName: string;
    fileS3Key: string;
    fileS3Md5: string;
    successCb: AnyFunc;
    failCb: AnyFunc;
  }) => {
    const res = await checkUploadDevice({
      productKey: props.productKey,
      productModelNoList: props.modelList,
      fileS3BucketName: params.fileS3BucketName,
      fileS3Key: params.fileS3Key,
      fileS3Md5: params.fileS3Md5,
    });
    if (res.code === HttpStatusCode.Success) {
      if (res.data?.result) {
        params.successCb();
      } else {
        params.failCb();
      }
      const result = {
        success: res.data?.result,
        failUrl: res.data.url || null,
        total: res.data?.total || 0,
      };
      setUploadRes(result);
      props.uploadChange({
        ...result,
        deviceNameFileS3Key: params.fileS3Key,
        deviceNameFileS3BucketName: params.fileS3BucketName,
        deviceNameFileMd5: params.fileS3Md5,
      });
    } else {
      params.failCb();
      const result = {
        success: false,
        failUrl: null,
        total: 0,
      };
      setUploadRes(result);
      props.uploadChange({
        ...result,
        deviceNameFileS3Key: params.fileS3Key,
        deviceNameFileS3BucketName: params.fileS3BucketName,
        deviceNameFileMd5: params.fileS3Md5,
      });
      message.error(res.message);
    }
  };

  const uploadProps: UploadProps = {
    maxCount: 1,
    accept: '.xlsx',
    progress: {
      strokeColor: {
        '0%': '#108ee9',
        '100%': '#87d068',
      },
      strokeWidth: 3,
      format: (percent) => percent && `${parseFloat(percent.toFixed(2))}%`,
    },
    beforeUpload: (file: any) => {
      return true;
    },
    customRequest: async (option: any) => {
      const { file, onSuccess, onError } = option;
      const reader = new FileReader();
      let fileS3Md5 = '';

      let chunkSize = 2097152,
        blobSlice = File.prototype.slice,
        chunks = Math.ceil(file.size / chunkSize),
        currentChunk = 0,
        spark = new SparkMD5.ArrayBuffer();

      reader.onload = function (e) {
        spark.append(e?.target?.result);
        currentChunk++;

        if (currentChunk < chunks) {
          loadNext();
        } else {
          fileS3Md5 = spark.end();
        }
      };

      reader.onerror = function () {
        console.warn('oops, something went wrong.');
      };

      function loadNext() {
        var start = currentChunk * chunkSize,
          end = start + chunkSize >= file.size ? file.size : start + chunkSize;

        reader.readAsArrayBuffer(blobSlice.call(file, start, end));
      }

      loadNext();

      reader.onloadend = async () => {
        request({
          path: '/k2/oss/upload',
          method: 'POST',
          body: {
            fileKey: `导入模板_${Date.now()}.xlsx`,
            bucketName: 'rover-operation',
          },
          newGeteway: true,
        }).then((res: any) => {
          const { uploadUrl, bucketName, fileKey } = res.data;
          request({
            absoluteURL: uploadUrl,
            contentType: 'multipart/form-data',
            method: 'PUT',
            body: file,
            timeout: 60000,
            newGeteway: true,
          }).then((res) => {
            setFileKey({
              deviceNameFileS3Key: fileKey,
              deviceNameFileS3BucketName: bucketName,
              deviceNameFileMd5: fileS3Md5,
            });
            onSuccess(res, file);
            checkDevice({
              fileS3BucketName: bucketName,
              fileS3Key: fileKey,
              fileS3Md5,
              successCb: onSuccess,
              failCb: onError,
            });
          });
        });
      };
    },
    onRemove: () => {
      const result = {
        success: false,
        failUrl: null,
        total: 0,
      };
      setUploadRes(result);
      props.uploadChange({
        ...result,
        deviceNameFileS3Key: '',
        deviceNameFileS3BucketName: '',
        deviceNameFileMd5: '',
      });
    },
  };

  const handleDownLoad = async () => {
    const res = await fetchApi.getTemplateFile({
      templateType: 'device_choice_file',
    });
    if (res.code === HttpStatusCode.Success && res.data?.templateUrl) {
      window.open(res.data?.templateUrl, '_self');
    }
  };

  const downLoadFail = () => {
    window.open(uploadRes.failUrl, '_self');
  };

  const handleValidateProps = (e: React.MouseEvent) => {
    if (!props?.productKey || isEmpty(props?.modelList)) {
      message.error('请先选择产品和型号');
      e.stopPropagation();
    }
  };

  return (
    <div className="file-upload">
      <Form>
        <Form.Item label="下载模版">
          <Button type="primary" onClick={handleDownLoad}>
            导入模版
          </Button>
        </Form.Item>
        <Form.Item label="上传文件">
          <div className="upload-device">
            <Upload {...uploadProps}>
              <Button onClick={handleValidateProps}>点击上传</Button>
            </Upload>
            {uploadRes.failUrl && (
              <div className="upload-fail">
                <ExclamationCircleOutlined style={{ color: 'red' }} />
                <div className="fail-tip">校验失败</div>
                <Button type="primary" onClick={downLoadFail}>
                  下载失败日志
                </Button>
              </div>
            )}
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default FileUpload;
