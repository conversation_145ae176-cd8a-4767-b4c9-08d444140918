import React, { useEffect, useRef, useState } from 'react';
import { AddDeviceForm, Colunms, SearchFormConfig } from './utils/colunms';
import CommonForm from '@/components/CommonForm';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import { Button, Cascader, Form, Input, Modal, Table, message } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import './index.scss';
import showModal from '@/components/commonModal';
import UploadDeviceFile from '@/components/UploadDeviceFile';
import Device from '@/fetch/bussiness/device';
import { NULL_GROUP, formatTreeData, isEmpty } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';
import RecordTable from '@/components/RecordTable';
import CommonTable from '@/components/CommonTable';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useTableData } from '@/components/CommonTable/useTableData';
import { cloneDeep } from 'lodash';
import { sendGlobalEvent } from '@/utils/emit';
import { formatLocation } from '@/utils/formatLocation';
import { useNavigate } from 'react-router-dom';
const deviceApi = new Device();

const DeviceManage = () => {
  const navigator = useNavigate();
  const { productKey } = formatLocation(window.location.search);
  const [groupForm] = Form.useForm();
  const [batchGroupForm] = Form.useForm();
  const searchFormRef = useRef<any>(null);
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const [formConfig, setFormConfig] = useState<FormConfig>(SearchFormConfig);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [groupList, setGroupList] = useState<any[]>([]);
  const [productList, setProductList] = useState<any[]>([]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );

  const initSearchCondition = {
    productKey: productKey,
    productModelNo: null,
    groupNoList: null,
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValue.searchValues
      ? historySearchValue.searchValues
      : initSearchCondition;
  });
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    deviceApi.queryDeviceList,
    'init',
    autoFetch,
  );
  const addDevice = () => {
    let formInstance: any = null;
    const addFormConf = cloneDeep(AddDeviceForm);
    const productField = formConfig.fields.find(
      (field: FieldItem) => field.fieldName === 'productKey',
    );
    const field = addFormConf.fields.find(
      (field: FieldItem) => field.fieldName === 'productKey',
    );
    field!.options = productField?.options;
    showModal({
      title: '新增设备',
      content: (
        <CommonForm
          name="addDevice"
          formConfig={addFormConf}
          layout="vertical"
          getFormInstance={(form: any) => {
            formInstance = form;
          }}
          onValueChange={(formValues: any, changedFieldName: string) => {
            if (changedFieldName === 'productKey' && formValues?.productKey) {
              const productModelNo = addFormConf.fields.find(
                (field: FieldItem) => field.fieldName === 'productModelNo',
              );
              const groupNo = addFormConf.fields.find(
                (field: FieldItem) => field.fieldName === 'groupNo',
              );
              const groupData = formatTreeData({
                origin: groupList,
                type: 'Cascader',
                level: 0,
                productKey: formValues?.productKey,
                disabledLevel: 2,
              });
              groupData.unshift(NULL_GROUP);
              groupNo!.options = groupData;
              // 产品型号
              getModalList(formValues?.productKey).then((res) => {
                productModelNo!.options = res;
                sendGlobalEvent('FORCE_UPDATE_CONFIG', {
                  name: 'addDevice',
                  config: addFormConf,
                });
              });
            }
          }}
        />
      ),
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: () => {},
        },
        {
          text: '保存',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: (cb: AnyFunc) => {
            formInstance.validateFields().then((values: any) => {
              const groupNo = values?.groupNo?.pop();
              deviceApi
                .addDevice({
                  ...values,
                  groupNo: groupNo || 'NULL',
                  productKey: values?.productKey,
                  productModelNo: values?.productModelNo,
                })
                .then((res) => {
                  if (res?.code === HttpStatusCode.Success) {
                    message.success('新增设备成功');
                    reloadTable();
                  } else {
                    message.error(res?.message);
                  }
                })
                .finally(() => {
                  cb();
                });
            });
          },
        },
      ],
    });
  };

  const batchAddDevice = () => {
    let selectFile: any = null;
    let formInstance: any = null;
    showModal({
      title: '批量新增',
      width: 600,
      content: (
        <UploadDeviceFile
          saveFile={(file: any) => {
            selectFile = file;
          }}
          getFormInstance={(form: any) => {
            formInstance = form;
          }}
        />
      ),
      footer: [
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: (cb: AnyFunc) => {
            formInstance?.validateFields().then((values: any) => {
              if (!selectFile) {
                message.error('请上传文件！');
                return;
              }
              deviceApi
                .batchAddDevice({
                  fileBucketName: selectFile.bucketName,
                  fileKey: selectFile.fileKey,
                  productKey: values.productKey,
                })
                .then((res: any) => {
                  if (res?.code === HttpStatusCode.Success) {
                    message.success('上传完成，请到[导入日志]中查看结果');
                    reloadTable();
                  } else {
                    message.error(res?.message);
                  }
                })
                .finally(() => {
                  cb();
                });
            });
          },
        },
      ],
    });
  };

  const handleReGroup = () => {
    if (isEmpty(selectedRowKeys)) {
      message.warning('请选择要重新分组的设备');
      return;
    }
    const groupData = formatTreeData({
      origin: groupList,
      type: 'Cascader',
      level: 0,
      productKey: searchCondition.productKey,
      disabledLevel: 2,
    });
    groupData.unshift(NULL_GROUP);
    showModal({
      title: '重新分组',
      content: (
        <Form form={groupForm}>
          <Form.Item
            label="分组"
            name="group"
            rules={[{ required: true, message: '请选择分组' }]}
          >
            <Cascader
              options={groupData}
              changeOnSelect={true}
              placeholder="请选择分组"
            />
          </Form.Item>
        </Form>
      ),
      footer: [
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: (cb: AnyFunc) => {
            groupForm
              .validateFields()
              .then((values) => {
                const groupValue = values?.group?.pop();
                deviceApi
                  .batchModifyGroup(
                    groupValue,
                    selectedRowKeys?.join(','),
                    searchCondition.productKey,
                  )
                  .then((res) => {
                    if (res?.code === HttpStatusCode.Success) {
                      message.success('重新分组成功');
                      reloadTable();
                      setSelectedRowKeys([]);
                    } else {
                      res?.message && message.error(res?.message);
                    }
                    cb();
                  })
                  .catch((e) => {});
              })
              .catch((e) => {});
          },
        },
        {
          text: '取消',
          type: 'cancelBtn',
        },
      ],
    });
  };
  const handleBatchReGroup = () => {
    const groupData = formatTreeData({
      origin: groupList,
      type: 'Cascader',
      level: 0,
      productKey: searchCondition.productKey,
      disabledLevel: 2,
    });
    groupData.unshift(NULL_GROUP);
    showModal({
      title: '批量分组',
      content: (
        <Form form={batchGroupForm}>
          <Form.Item
            label="分组"
            name="group"
            rules={[{ required: true, message: '请选择分组' }]}
          >
            <Cascader
              options={groupData}
              changeOnSelect={true}
              showSearch={true}
              placeholder="请选择分组"
            />
          </Form.Item>
          <Form.Item
            label="设备名称"
            name="deviceNo"
            rules={[{ required: true, message: '请输入设备名称' }]}
          >
            <Input.TextArea placeholder="请输入，支持输入多个，用“,”隔开" />
          </Form.Item>
        </Form>
      ),
      footer: [
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: (cb: AnyFunc) => {
            batchGroupForm
              .validateFields()
              .then((values) => {
                const groupValue = values?.group?.pop();
                deviceApi
                  .batchModifyGroup(
                    groupValue,
                    values.deviceNo,
                    searchCondition.productKey,
                  )
                  .then((res) => {
                    if (res?.code === HttpStatusCode.Success) {
                      message.success('批量分组成功');
                      reloadTable();
                    } else {
                      res?.message && message.error(res?.message);
                    }
                    cb();
                  })
                  .catch((e) => {});
              })
              .catch((e) => {});
          },
        },
        {
          text: '取消',
          type: 'cancelBtn',
        },
      ],
    });
  };

  const handleDelete = () => {
    if (isEmpty(selectedRowKeys)) {
      message.warning('请选择要删除的设备');
      return;
    }
    Modal.confirm({
      content: `是否删除勾选的${selectedRowKeys.length}台设备？`,
      okText: '删除',
      onOk: () => {
        const seletRowData: any = [];
        const selectSet = new Set(selectedRowKeys);
        tableData?.list?.forEach((item: any) => {
          if (selectSet.has(item.deviceName)) {
            seletRowData.push(item);
          }
        });
        deviceApi
          .deleteDeviceList(
            seletRowData?.map((i: any) => i.deviceNo)?.join(','),
            seletRowData[0]?.productKey,
          )
          .then((res) => {
            if (res.code === HttpStatusCode.Success) {
              message.success('删除成功');
              reloadTable();
            }
          });
      },
    });
  };

  const loadLog = () => {
    showModal({
      width: 1200,
      title: '导入日志',
      content: <RecordTable />,
      footer: [],
    });
  };

  const handleBatchChangeConfig = () => {};

  const getProductList = async () => {
    const productData = await deviceApi.queryProductList();
    if (productData?.code === HttpStatusCode.Success) {
      const productField = formConfig.fields.find(
        (field: FieldItem) => field.fieldName === 'productKey',
      );
      const productList =
        productData?.data?.map((item: any) => ({
          label: item.productName,
          value: item.productKey,
        })) || [];
      productField!.options = productList;
      const prodKey =
        searchCondition.productKey ||
        (productData?.data && productData?.data[0]?.productKey);
      setFormConfig({ ...formConfig });
      setProductList(productList);
      if (searchCondition.productKey) {
        searchFormRef.current?.setFieldValue(
          'productKey',
          searchCondition.productKey,
        );
      } else {
        searchFormRef.current?.setFieldValue('productKey', prodKey);
        setSearchCondition({
          ...searchCondition,
          productKey: prodKey,
        });
      }
      setAutoFetch(true);
      getGroupList(prodKey);
    }
  };

  const getGroupList = async (prodKey: string) => {
    const groupData = await deviceApi.getAllGroupList({
      productKey: prodKey,
    });
    if (groupData?.code === HttpStatusCode.Success) {
      setGroupList(groupData?.data?.groupNoList || []);
      updateLinkedFields(prodKey, groupData?.data?.groupNoList || []);
    }
  };

  const getModalList = async (productKey: string) => {
    if (!productKey) {
      message.warning('请先选择产品');
      return [];
    }

    try {
      const res = await deviceApi.queryModelList(productKey);
      if (res.code === HttpStatusCode.Success) {
        const data = res?.data?.map(
          (item: {
            modelName: string;
            modelNo: string;
            productKey: string;
          }) => ({
            label: item.modelName,
            value: item.modelNo,
          }),
        );
        return data;
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  };

  const updateLinkedFields = (productKey: any, groupList: any[]) => {
    const groupNoListField = formConfig.fields.find(
      (field: FieldItem) => field.fieldName === 'groupNoList',
    );
    const modelNo = formConfig.fields.find(
      (field: any) => field.fieldName === 'productModelNo',
    );
    const groupData = formatTreeData({
      origin: groupList,
      type: 'Cascader',
      level: 0,
      productKey,
    });
    groupData.unshift(NULL_GROUP);
    groupNoListField!.options = groupData;
    getModalList(productKey).then((res) => {
      modelNo!.options = res;
      setFormConfig({ ...formConfig });
    });
  };

  const formatColumns = () => {
    return Colunms?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    const {
                      deviceName = '',
                      productKey = '',
                      productModelNo = '',
                    } = record;
                    navigator(
                      `/device/deviceConfigChange?deviceName=${deviceName}&productKey=${productKey}&productModelNo=${productModelNo}`,
                    );
                  }}
                >
                  查看配置变更
                </a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const onSearchClick = () => {
    const values = searchFormRef.current?.getFieldsValue();
    const groupNoList = cloneDeep(values)?.groupNoList?.pop();
    setSearchCondition({
      ...searchCondition,
      ...values,
      productKey: values?.productKey,
      productModelNo: values?.productModelNo,
      groupNoList: groupNoList && [groupNoList],
    });
  };

  const onResetClick = () => {
    searchFormRef.current?.resetFields();
    setSearchCondition({
      ...initSearchCondition,
      productKey: productList && productList[0]?.value,
    });
    searchFormRef.current?.setFieldValue(
      'productKey',
      productList && productList[0]?.value,
    );
    setSelectedRowKeys([]);
    updateLinkedFields(productList && productList[0]?.value, groupList);
  };

  useEffect(() => {
    getProductList();
  }, []);

  return (
    <div className="device-management-container">
      <CommonForm
        formConfig={formConfig}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        formType="search"
        layout={'inline'}
        getFormInstance={(form: any) => {
          searchFormRef.current = form;
        }}
        onValueChange={(formValues: any, changedFieldName: string) => {
          if (changedFieldName === 'productKey') {
            getGroupList(formValues?.productKey);
            searchFormRef.current?.setFieldsValue({
              productModelNo: null,
              groupNoList: null,
            });
          }
        }}
      />
      <div className="btn-group">
        <Button type="primary" onClick={addDevice}>
          新增设备
        </Button>
        <Button type="primary" onClick={batchAddDevice}>
          批量新增
        </Button>
        <Button type="primary" onClick={handleReGroup}>
          重新分组
        </Button>
        <Button type="primary" onClick={handleBatchReGroup}>
          批量分组
        </Button>
        <Button onClick={handleDelete}>删除</Button>
        <Button type="primary" onClick={loadLog}>
          导入日志
        </Button>
        <Button type="primary" onClick={handleBatchChangeConfig}>
          批量修改配置
        </Button>
      </div>

      <CommonTable
        searchCondition={searchCondition}
        loading={false}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        scrollY={450}
        columns={formatColumns()}
        rowKey={'deviceName'}
        rowSelection={rowSelection}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      ></CommonTable>
    </div>
  );
};

export default React.memo(DeviceManage);
