import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Table, Input, Tabs, Space, message, Form } from 'antd';
import type { TableRowSelection } from 'antd/es/table/interface';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import showModal from '@/components/commonModal';
import './index.scss';

interface FileItem {
  fileNo: string;
  fileName: string;
  blockNo: string;
  blockName: string;
  description?: string;
  updateTime?: string;
}

interface BlockData {
  blockNo: string;
  blockName: string;
  fileList: FileItem[];
}

interface ConfigFileSelectorProps {
  selectedData?: any;
}

export interface ConfigFileSelectorRef {
  getSelectedData: () => Promise<Record<string, string[]>>;
}

const ViewConfigFileModal: React.FC<{ data: any }> = ({ data }) => {
  const [form] = Form.useForm();

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={data}
      className="view-config-file-form"
    >
      <Form.Item label="配置文件编号" name="fileNo">
        <Input disabled />
      </Form.Item>
      <Form.Item label="配置文件名称" name="fileName">
        <Input disabled />
      </Form.Item>
      <Form.Item label="配置路径" name="filePath">
        <Input disabled />
      </Form.Item>
      <Form.Item label="描述" name="description">
        <Input.TextArea disabled autoSize={{ minRows: 3, maxRows: 5 }} />
      </Form.Item>
      <Form.Item label="模板内容" name="content">
        <Input.TextArea disabled autoSize={{ minRows: 6, maxRows: 10 }} />
      </Form.Item>
    </Form>
  );
};

const ConfigFileSelector = forwardRef<ConfigFileSelectorRef, ConfigFileSelectorProps>(
  ({ selectedData }, ref) => {
    const configTemplateApi = new ConfigTemplateApi();
    
    const [configData, setConfigData] = useState<BlockData[]>([]);
    const [activeBlockNo, setActiveBlockNo] = useState<string>();
    const [tabStates, setTabStates] = useState<Record<string, {
      selectedFileRows: string[];
      searchText: string;
      filteredFileList: FileItem[];
    }>>({});
    const [loading, setLoading] = useState(false);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getSelectedData: async () => {
        const result: Record<string, string[]> = {};
        Object.keys(tabStates).forEach(blockNo => {
          if (tabStates[blockNo].selectedFileRows.length > 0) {
            result[blockNo] = tabStates[blockNo].selectedFileRows;
          }
        });
        return result;
      },
    }));

    // 当选中数据变化时，获取配置数据
    useEffect(() => {
      if (selectedData?.productKey) {
        fetchConfigData(selectedData.productKey);
      }
    }, [selectedData?.productKey]);

    const fetchConfigData = async (productKey: string) => {
      setLoading(true);
      try {
        const res = await configTemplateApi.getAllEnableConfigBaseInfoList({
          productKey,
        });

        if (res && res.code === HttpStatusCode.Success) {
          setConfigData(res.data || []);
          
          // 设置默认选中第一个tab
          if (res.data && res.data.length > 0) {
            setActiveBlockNo(res.data[0].blockNo);
            
            // 初始化每个tab的状态
            const initialStates: Record<string, any> = {};
            res.data.forEach((block: BlockData) => {
              initialStates[block.blockNo] = {
                selectedFileRows: [],
                searchText: '',
                filteredFileList: block.fileList,
              };
            });
            setTabStates(initialStates);
          }
        } else {
          message.error(res?.message || '获取配置列表失败');
        }
      } catch (error) {
        message.error('获取配置列表失败');
      } finally {
        setLoading(false);
      }
    };

    // 获取当前tab的状态
    const getCurrentTabState = () => activeBlockNo && tabStates[activeBlockNo];

    // 更新当前tab的状态
    const updateCurrentTabState = (updates: any) => {
      if (activeBlockNo) {
        setTabStates(prev => ({
          ...prev,
          [activeBlockNo]: {
            ...prev[activeBlockNo],
            ...updates,
          },
        }));
      }
    };

    // 获取当前模块数据
    const getCurrentBlock = () => {
      return configData.find((block) => block.blockNo === activeBlockNo);
    };

    // 处理搜索
    const handleSearch = (value: string) => {
      const currentBlock = getCurrentBlock();
      if (!currentBlock) return;

      let filteredList = currentBlock.fileList;

      // 如果有搜索内容，进行过滤
      if (value.trim()) {
        const searchText = value.toLowerCase();
        filteredList = currentBlock.fileList.filter(
          (file: any) =>
            file.fileName.toLowerCase().includes(searchText) ||
            file.fileNo.toLowerCase().includes(searchText)
        );
      }

      updateCurrentTabState({
        searchText: value,
        filteredFileList: filteredList,
      });
    };

    // 查看配置文件详情
    const handleViewFile = async (fileNo: string) => {
      try {
        const res = await configTemplateApi.getConfigFile({
          fileNo,
          productKey: selectedData?.productKey!,
        });
        if (res.code === HttpStatusCode.Success) {
          showModal({
            title: '查看配置文件',
            content: <ViewConfigFileModal data={res.data} />,
            width: '800px',
            footer: [
              {
                text: '返回',
                type: 'cancelBtn',
                onClick: (cb: () => void) => {
                  cb();
                },
              },
            ],
          });
        } else {
          message.error(res.message);
        }
      } catch (error) {
        message.error('获取配置文件详情失败');
      }
    };

    // 表格列定义
    const columns = [
      {
        title: '序号',
        key: 'index',
        width: 80,
        render: (_: any, __: any, index: number) => index + 1,
      },
      {
        title: '模块',
        dataIndex: 'blockName',
        key: 'blockName',
      },
      {
        title: '配置文件编码',
        dataIndex: 'fileNo',
        key: 'fileNo',
      },
      {
        title: '配置文件名称',
        dataIndex: 'fileName',
        key: 'fileName',
      },
      {
        title: '操作',
        key: 'action',
        render: (record: FileItem) => (
          <Space size="middle">
            <a onClick={() => handleViewFile(record.fileNo)}>查看</a>
          </Space>
        ),
      },
    ];

    // 表格选择配置
    const rowSelection: TableRowSelection<FileItem> = {
      selectedRowKeys: getCurrentTabState()?.selectedFileRows ?? [],
      onChange: (selectedRowKeys) => {
        updateCurrentTabState({
          selectedFileRows: selectedRowKeys as string[],
        });
      },
      preserveSelectedRowKeys: true,
    };

    if (!selectedData?.productKey) {
      return (
        <div className="config-file-selector">
          <div className="section-title">选择配置文件</div>
          <div className="no-product">请先选择产品</div>
        </div>
      );
    }

    return (
      <div className="config-file-selector">
        <div className="section-title">选择配置文件</div>
        <div className="section-content">
          {configData.length > 0 ? (
            <>
              <Tabs
                activeKey={activeBlockNo}
                onChange={setActiveBlockNo}
                items={configData.map((block) => ({
                  key: block.blockNo,
                  label: block.blockName,
                }))}
              />
              
              <div className="file-table-container">
                <div className="search-bar">
                  <Input.Search
                    placeholder="请输入配置文件编码或名称"
                    enterButton="查询"
                    value={getCurrentTabState()?.searchText || ''}
                    allowClear
                    onChange={(e) =>
                      updateCurrentTabState({
                        searchText: e.target.value,
                        filteredFileList: e.target.value
                          ? getCurrentTabState()?.filteredFileList
                          : getCurrentBlock()?.fileList,
                      })
                    }
                    onSearch={handleSearch}
                    style={{ width: 400, marginBottom: 16 }}
                  />
                </div>
                
                <div className="table-info">
                  <span>已选择 {getCurrentTabState()?.selectedFileRows?.length || 0} 项</span>
                </div>
                
                <Table
                  rowSelection={rowSelection}
                  columns={columns}
                  dataSource={getCurrentTabState()?.filteredFileList || []}
                  rowKey="fileNo"
                  pagination={false}
                  loading={loading}
                />
              </div>
            </>
          ) : (
            <div className="no-data">暂无配置数据</div>
          )}
        </div>
      </div>
    );
  }
);

ConfigFileSelector.displayName = 'ConfigFileSelector';

export default ConfigFileSelector;
