import BreadCrumb from '@/components/BreadCrumb';
import { CommandControlFetch, Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/formatLocation';
import { But<PERSON>, Col, message, Modal, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/mode-json';
import { createRoot } from 'react-dom/client';
import DeviceTable from '../components/DeviceTable';
import { CommandTaskStatus, DeviceStatus } from '../utils/constant';
import {
  CheckTaskDeviceTableConfig,
  CheckTaskSelectDevice,
} from '../utils/column';
import showModal from '@/components/commonModal';
import { FormConfig } from '@/components/CommonForm/formConfig';

const row1 = new Map([
  ['指令编码', 'serviceIdentifier'],
  ['创建时间', 'createTime'],
  ['生效时间', 'effectiveTime'],
  ['创建人', 'createUser'],
  ['任务状态', 'taskStatusName'],
]);
const row2 = new Map([
  ['指令名称', 'serviceName'],
  ['指令内容', 'commandArgs'],
]);
const row3 = new Map([
  ['推送时间', 'immediatelyName'],
  ['定时推送日期', 'executionTime'],
  ['是否分批执行', 'batchName'],
  ['批次间隔', 'batchInterval'],
  ['单次推送数量', 'batchCount'],
]);
const row4 = new Map([
  ['是否保留任务', 'retainName'],
  ['保留时间', 'retainTimeStr'],
]);

const CheckTask = () => {
  const fetchApi = new CommandControlFetch();
  const deviceApi = new Device();
  const deviceRef = useRef<any>(null);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const editorRef = useRef<any>(null);
  const { taskNo, productKey } = formatLocation(window.location.search);
  const [taskInfo, setTaskInfo] = useState<any>(null);
  const [formatSearchConfig, setFormatSearchConfig] = useState<FormConfig>(
    CheckTaskSelectDevice,
  );
  const deviceSearchFormInit = useRef<any>({
    searchForm: {
      productKey: productKey,
      taskNo: taskNo,
      productModelNoList: [],
      executeStatus: null,
      deviceName: null,
    },
    pageNum: 1,
    pageSize: 10,
  });

  useEffect(() => {
    getTaskInfo();
    formatSearch();
  }, []);

  useEffect(() => {
    deviceSearchFormInit.current = {
      ...deviceSearchFormInit.current,
      searchForm: {
        ...deviceSearchFormInit.current.searchForm,
        productKey: productKey,
      },
    };
  }, [productKey]);

  const getTaskInfo = async () => {
    const res = await fetchApi.getTaskInfo({ taskNo });
    if (res.code === HttpStatusCode.Success) {
      setTaskInfo(res.data);
    }
  };

  const formatSearch = () => {
    Promise.all([
      deviceApi.queryProductList(),
      fetchApi.getDeviceStatus(),
    ]).then(([res1, res2]) => {
      let productOptions: any[] = [];
      let statusOptions: any[] = [];
      if (res1.code === HttpStatusCode.Success) {
        productOptions = res1.data?.map((v: any) => ({
          label: v.productName,
          value: v.productKey,
        }));
      }
      if (res2.code === HttpStatusCode.Success) {
        statusOptions = res2.data?.map((v: any) => ({
          label: v.name,
          value: v.value,
        }));
      }
      const val = {
        ...CheckTaskSelectDevice,
        fields: formatSearchConfig.fields.map((v) => {
          if (v.fieldName === 'productKey') {
            v = {
              ...v,
              options: productOptions,
            };
          } else if (v.fieldName === 'executeStatus') {
            v = {
              ...v,
              options: statusOptions,
            };
          }
          return v;
        }),
      };
      setFormatSearchConfig(val);
    });
  };

  const checkDetail = () => {
    setModalShow(true);
    setTimeout(() => {
      editorRef.current = createRoot(document.getElementById('editor')!);
      handleAceEditor(taskInfo['commandArgs']);
    }, 10);
  };

  const handleAceEditor = (val: string) => {
    editorRef.current.render(
      <AceEditor
        key={new Date().getTime()}
        mode="json"
        value={val}
        maxLines={18}
        minLines={18}
        setOptions={{
          useWorker: false,
          readOnly: true,
          wrap: true,
        }}
        editorProps={{ $blockScrolling: true }}
      />,
    );
  };

  const handlePushAgain = async () => {
    const { selectedDeviceInfo, selectedDevice } =
      deviceRef.current?.checkData();
    if (selectedDevice.length <= 0) {
      return;
    }
    const keyArr: string[] = [];
    selectedDeviceInfo.forEach((v: any) => {
      if (
        [
          DeviceStatus.not_received,
          DeviceStatus.received,
          DeviceStatus.success,
          DeviceStatus.failed,
          DeviceStatus.expired,
        ].includes(v.executeStatus)
      ) {
        keyArr.push(v.detailNo);
      }
    });
    if (keyArr.length !== selectedDevice.length) {
      showModal({
        title: '仅未接收/已接收/成功/失败/任务过期状态可重新推送',
        width: '600px',
        content: '',
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
      return;
    }
    showModal({
      title: `确定重新推送${keyArr.length}台设备的指令下发？`,
      width: '600px',
      content: '',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: any) => {
            cb();
          },
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            const res = await fetchApi.pushDeviceAgain({
              detailNoList: keyArr,
              taskNo,
            });
            if (res.code === HttpStatusCode.Success) {
              message.success(`重推成功${keyArr.length}台设备`);
              deviceRef.current?.reloadTable();
            } else {
              message.error(res.message);
            }
            cb();
          },
        },
      ],
    });
  };

  const handleStop = () => {
    const { selectedDeviceInfo, selectedDevice } =
      deviceRef.current?.checkData();
    if (selectedDevice.length <= 0) {
      return;
    }
    const keyArr: string[] = [];
    selectedDeviceInfo.forEach((v: any) => {
      if (
        [DeviceStatus.to_be_effective, DeviceStatus.not_received].includes(
          v.executeStatus,
        )
      ) {
        keyArr.push(v.detailNo);
      }
    });
    if (keyArr.length !== selectedDevice.length) {
      showModal({
        title: '仅待生效和未接收可以终止',
        width: '600px',
        content: '',
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
      return;
    }
    showModal({
      title: `确定终止${keyArr.length}台设备的指令下发？若设备端已接收则无法终止成功`,
      width: '600px',
      content: '',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: any) => {
            cb();
          },
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            const res = await fetchApi.stopDevice({ detailNoList: keyArr });
            if (res.code === HttpStatusCode.Success) {
              message.success('终止成功');
              deviceRef.current?.reloadTable();
            } else {
              message.error(res.message);
            }
            cb();
          },
        },
      ],
    });
  };

  const middleBtns: any[] = [CommandTaskStatus.canceled].includes(
    taskInfo?.taskStatus,
  )
    ? []
    : [
        {
          show: true,
          title: '重新推送',
          key: 'pushAgain',
          onClick: () => handlePushAgain(),
        },
        {
          show: true,
          title: '终止',
          key: 'stop',
          onClick: () => handleStop(),
        },
      ];

  return (
    <>
      <BreadCrumb
        items={[
          { title: '指令控制', route: '/commandControl' },
          { title: '任务详情', route: '' },
        ]}
      />
      <div className="task-info-page">
        <div className="task-info">
          {taskInfo && (
            <>
              <Row>
                {Array.from(row1.keys()).map((label: string) => {
                  const name = row1.get(label);
                  const v = taskInfo[name!];
                  return v != null ? (
                    <div className="col" key={name}>
                      <span> {label}:</span>
                      <div>{v || '-'}</div>
                    </div>
                  ) : (
                    ''
                  );
                })}
              </Row>
              <Row>
                {Array.from(row2.keys()).map((label: string) => {
                  const name = row2.get(label);
                  const v = taskInfo[name!];
                  return v != null ? (
                    <div className="col" key={name}>
                      <span> {label}:</span>
                      <div>
                        {name === 'commandArgs' ? (
                          <Button type="primary" onClick={checkDetail}>
                            查看详情
                          </Button>
                        ) : (
                          v || '-'
                        )}
                      </div>
                    </div>
                  ) : (
                    ''
                  );
                })}
              </Row>
              <Row>
                {Array.from(row3.keys()).map((label: string) => {
                  const name = row3.get(label);
                  const v = taskInfo[name!];
                  return (
                    <div className="col" key={name}>
                      <span> {label}:</span>
                      <div>
                        {v
                          ? name === 'batchInterval'
                            ? `${v / 60}小时`
                            : v
                          : '/'}
                      </div>
                    </div>
                  );
                })}
              </Row>
              <Row>
                {Array.from(row4.keys()).map((label: string) => {
                  const name = row4.get(label);
                  const v = taskInfo[name!];
                  return (
                    <div className="col" key={name}>
                      <span> {label}:</span>
                      <div>{v || '/'}</div>
                    </div>
                  );
                })}
              </Row>
            </>
          )}
        </div>
      </div>
      <DeviceTable
        ref={deviceRef}
        tableConfig={CheckTaskDeviceTableConfig}
        formConfig={formatSearchConfig}
        initSearchCondition={deviceSearchFormInit.current}
        rowKey="detailNo"
        showSelect={true}
        middleBtns={middleBtns}
        fetchApi={fetchApi.getTaskDevice}
      />
      <Modal
        title="查看指令内容"
        open={modalShow}
        width={550}
        className="editor-modal"
        onCancel={() => setModalShow(false)}
        footer={[
          <Button type="primary" onClick={() => setModalShow(false)}>
            关闭
          </Button>,
        ]}
      >
        <div id="editor"></div>
      </Modal>
    </>
  );
};

export default React.memo(CheckTask);
