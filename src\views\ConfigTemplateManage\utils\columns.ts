import { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  ConfigTemplateApi,
  ProductManageFetch,
  Device,
} from '@/fetch/bussiness';

const deviceApi = new Device();

export enum EnableMap {
  ENABLE = 1,
  DISABLE = 0,
}

export const TableConfig = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    align: 'center',
  },
  { title: '模板编号', dataIndex: 'templateNo', width: 120, align: 'center' },
  { title: '模板名称', dataIndex: 'templateName', width: 120, align: 'center' },
  {
    title: '适用型号',
    dataIndex: 'productModelList',
    width: 120,
    align: 'center',
  },
  { title: '备注', dataIndex: 'remark', width: 160, align: 'center' },
  { title: '创建人', dataIndex: 'createUser', width: 100, align: 'center' },
  { title: '创建时间', dataIndex: 'createTime', width: 160, align: 'center' },
  { title: '更新时间', dataIndex: 'modifyTime', width: 160, align: 'center' },
  { title: '更新人', dataIndex: 'modifyUser', width: 100, align: 'center' },
  { title: '状态', dataIndex: 'enableName', width: 100, align: 'center' },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 300,
    fixed: 'right',
    align: 'center',
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      placeholder: '请选择产品',
      options: [],
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      placeholder: '请选择型号',
      options: [],
    },
    {
      fieldName: 'templateNo',
      label: '配置模板编号',
      type: 'input',
      placeholder: '请输入配置模板编号',
    },
    {
      fieldName: 'templateName',
      label: '配置模板名称',
      type: 'input',
      placeholder: '请输入配置模板名称',
    },
    {
      fieldName: 'createUser',
      label: '创建人',
      type: 'input',
      placeholder: '请输入创建人账号',
    },
    {
      fieldName: 'enable',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      type: 'rangeTime',
      xxl: 8,
      xl: 12,
      lg: 16,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.productName,
              value: item.productKey,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    productKey: [
      {
        linkFieldName: 'productModelNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) return [];
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          }
          return [];
        },
      },
    ],
  },
};

export const InitRecordColumns = [
  {
    title: '序号',
    dataIndex: 'order',
    width: 80,
    align: 'center',
  },
  { title: '模板编号', dataIndex: 'templateNo', width: 120, align: 'center' },
  { title: '模板名称', dataIndex: 'templateName', width: 120, align: 'center' },
  {
    title: '推送时间',
    dataIndex: 'createTime',
    width: 120,
    align: 'center',
  },
  { title: '推送人', dataIndex: 'createUser', width: 160, align: 'center' },
  {
    title: '操作',
    dataIndex: 'operate',
    width: 120,
    align: 'center',
    fixed: 'right',
  },
];

export const InitRecordDetailColumns = [
  {
    title: '序号',
    dataIndex: 'order',
    width: 80,
    align: 'center',
  },
  { title: '产品', dataIndex: 'productName', width: 120, align: 'center' },
  { title: '型号', dataIndex: 'productModelName', width: 120, align: 'center' },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    width: 120,
    align: 'center',
  },
  {
    title: '分组',
    dataIndex: 'groupFullName',
    width: 120,
    align: 'center',
  },
  {
    title: '操作状态',
    dataIndex: 'resultName',
    width: 120,
    align: 'center',
  },
];

export const InitDeviceConfigFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'templateNo',
      label: '模板编码',
      type: 'text',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 24,
      xxl: 24,
    },
    {
      fieldName: 'templateName',
      label: '模板名称',
      type: 'text',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 24,
      xxl: 24,
    },
  ],
};

export const ProductSelectorFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      labelInValue: false,
      validatorRules: [{ required: true, message: '请选择产品' }],
      allowClear: true,
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const deviceApi = new Device();
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return (
              res.data?.map((item: any) => ({
                label: item.productName,
                value: item.productKey,
              })) || []
            );
          }
          return [];
        },
      },
    ],
  },
};
