import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import { CommonForm } from '@jd/x-coreui';
import { RulesConfigurationFormConfig } from '@/views/ConfigTaskManage/utils/constants';

const RulesConfiguration = forwardRef((props, ref) => {
  const formRef = useRef<any>(null);
  const validateFormValues = () => {
    return formRef.current.validateFields();
  };
  const getFormValues = () => {
    return formRef.current.getFieldsValue();
  };
  useImperativeHandle(ref, () => {
    return {
      validateFormValues,
      getFormValues,
    };
  });
  return (
    <div className="rules-configuration">
      <CommonForm
        formConfig={RulesConfigurationFormConfig}
        layout="inline"
        getFormInstance={(ref: any) => {
          formRef.current = ref;
        }}
      />
    </div>
  );
});

export default RulesConfiguration;
