import React, { useContext, useEffect, useRef, useState } from 'react';
import { Table, Select, Tag, Button, Popconfirm, Form } from 'antd';
import './index.scss';
import { UpgradeTypeMap, UpgradeTypeOptions } from '@/utils/constant';
import type { FormInstance, RefSelectProps } from 'antd';
import { saveSelectedVehicle } from '@/redux/reducers/selectedVehicle';
import { useDispatch } from 'react-redux';
const EditableContext = React.createContext<FormInstance<any> | null>(null);
interface VehicleInfo {
  vehicleName: string;
  stationId: number;
  stationName: string;
  upgradeType: number;
  upgradeTypeName: string;
}
interface EditableRowProps {
  index: number;
}
interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  dataIndex: keyof VehicleInfo;
  record: VehicleInfo;
  handleSave: (record: VehicleInfo, values: any) => void;
  options: any;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};
const EditableCell = (props: EditableCellProps) => {
  const {
    title,
    editable,
    children,
    dataIndex,
    record,
    options,
    handleSave,
    ...restProps
  } = props;
  const selectRef = useRef<RefSelectProps>(null);
  const [editing, setEditing] = useState(false);
  const form = useContext(EditableContext)!;
  useEffect(() => {
    if (editing) {
      selectRef.current!.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({ [dataIndex]: record[dataIndex] });
  };
  const save = async () => {
    try {
      const values = await form.validateFields();
      toggleEdit();
      handleSave(record, values);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };
  let childNode = children;

  if (editable) {
    childNode = editing ? (
      <Form.Item style={{ margin: 0 }} name={dataIndex}>
        <Select
          ref={selectRef}
          onChange={save}
          onBlur={toggleEdit}
          options={options}
        />
      </Form.Item>
    ) : (
      <div onClick={toggleEdit}>
        <Tag
          color={
            record.upgradeTypeName === UpgradeTypeMap.get('SILENT')?.name
              ? '#108ee9'
              : '#f50'
          }
        >
          {children}
        </Tag>
      </div>
    );
  }

  return (
    <td {...restProps} style={{ cursor: 'pointer' }}>
      {childNode}
    </td>
  );
};
const SelectedVehicle = ({
  selectedVehicle,
  handleDelVehicle,
  delBtnDisable,
  editable,
}: {
  selectedVehicle: Map<string, any>;
  handleDelVehicle?: Function;
  delBtnDisable?: boolean;
  editable?: boolean;
}) => {
  const dispatch = useDispatch();
  const [tableData, setTableData] = useState<VehicleInfo[]>([]);
  useEffect(() => {
    const tempArr: any = [];
    selectedVehicle.forEach((value: any) => {
      tempArr.push({
        ...value,
        vehicleName: value.vehicleName,
        stationId: value.stationId,
        stationName: value.stationName,
        upgradeType: value.upgradeType,
        upgradeTypeName: value.upgradeTypeName,
      });
    });
    tempArr.sort((a: any, b: any) => a.stationId - b.stationId);
    setTableData(tempArr);
  }, [selectedVehicle]);

  const columns: any[] = [
    {
      dataIndex: 'index',
      title: '序号',
      render: (value: any, record: any, rowIndex: number) => {
        return <>{rowIndex + 1}</>;
      },
    },
    {
      title: '产品类型',
      width: 150,
      dataIndex: 'productTypeName',
      ellipsis: true,
    },
    {
      title: '设备类型',
      fieldName: 'vehicleBusinessTypeName',
      width: 150,
      dataIndex: 'vehicleBusinessTypeName',
      ellipsis: true,
    },
    {
      dataIndex: 'stationName',
      title: '站点名称',
      render: (value: any, record: any, rowIndex: number) => {
        if (!record.stationName) {
          return <>—</>;
        } else {
          return <>{record.stationName}</>;
        }
      },
    },
    {
      title: '箱体格口模板',
      width: 150,
      dataIndex: 'boxTemplateName',
      ellipsis: true,
    },
    {
      dataIndex: 'vehicleName',
      title: '车牌号',
    },
    {
      dataIndex: 'upgradeTypeName',
      title: '升级方式',
      editable: editable,
    },
    {
      dataIndex: 'operation',
      title: '操作',
      render: (value: any, record: any, rowIndex: number) => {
        return (
          <Popconfirm
            title="是否确定要删除此车辆？"
            okText="确定"
            cancelText="取消"
            onConfirm={() => {
              handleDelVehicle && handleDelVehicle(record.vehicleName);
            }}
            onCancel={() => {}}
            disabled={delBtnDisable}
          >
            <Button type="link" danger disabled={delBtnDisable}>
              删除
            </Button>
          </Popconfirm>
        );
      },
    },
  ];
  const handleChangeUpgradeType = (
    upgradeType: number,
    record?: VehicleInfo,
  ) => {
    const newSelectedVehicleMap = new Map();
    // 修改单条数据
    if (record) {
      selectedVehicle.forEach((value, key, map) => {
        if (record.vehicleName === value.vehicleName) {
          newSelectedVehicleMap.set(key, {
            ...value,
            upgradeType: upgradeType,
            upgradeTypeName: UpgradeTypeMap.get(upgradeType.toString())?.name,
          });
        } else {
          newSelectedVehicleMap.set(key, value);
        }
      });
    } else {
      // 修改全量数据
      selectedVehicle.forEach((value, key, map) => {
        newSelectedVehicleMap.set(key, {
          ...value,
          upgradeType: upgradeType,
          upgradeTypeName: UpgradeTypeMap.get(upgradeType.toString())?.name,
        });
      });
    }

    dispatch(saveSelectedVehicle(newSelectedVehicleMap));
  };
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };
  const recordColumns = columns?.map((col) => {
    if (!col.editable) {
      if (col.dataIndex === 'upgradeTypeName') {
        col.render = (value: any, record: any) => (
          <>
            <Tag
              color={
                record.upgradeTypeName === UpgradeTypeMap.get('SILENT')?.name
                  ? '#108ee9'
                  : '#f50'
              }
            >
              {record.upgradeTypeName}
            </Tag>
          </>
        );
      }
      return col;
    }
    return {
      ...col,
      onCell: (record: VehicleInfo) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        options: UpgradeTypeOptions,
        handleSave: (record: VehicleInfo, formValue: any) => {
          handleChangeUpgradeType(formValue.upgradeTypeName.toString(), record);
        },
      }),
    };
  });
  return (
    <div className="selected-vehicle-container">
      <div
        className="operate-btns"
        style={{ display: `${!editable ? 'none' : 'block'}` }}
      >
        <Button
          onClick={() =>
            handleChangeUpgradeType(UpgradeTypeMap.get('SILENT')!.key)
          }
          style={{ marginRight: '10px' }}
        >
          全部静默升级
        </Button>
        <Button
          onClick={() =>
            handleChangeUpgradeType(UpgradeTypeMap.get('OFFLINE')!.key)
          }
        >
          全部离线升级
        </Button>
      </div>
      <Table
        bordered
        components={components}
        dataSource={tableData}
        columns={recordColumns}
        pagination={false}
        scroll={{ y: 300 }}
        rowKey={'vehicleName'}
      ></Table>
    </div>
  );
};

export default React.memo(SelectedVehicle);
