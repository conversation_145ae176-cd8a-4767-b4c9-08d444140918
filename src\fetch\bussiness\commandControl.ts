import { request } from '@/fetch/core';
import { CommandTaskStatus } from '@/views/CommandControl/utils/constant';
class CommandControlFetch {
  getCommandTaskList = (params: {
    pageNum: number;
    pageSize: number;
    productKey: any;
    productModelNo: any;
    blockNo: any;
    identifier: any;
    taskNo: string;
    commandContentLike: string;
    taskStatus: any;
    createUser: string;
    createTimeStart: string;
    createTimeEnd: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/queryTaskList',
      body: {
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        productKey: params.productKey?.value,
        productModelNo: params.productModelNo?.value,
        blockNo: params.blockNo?.value,
        identifier: params.identifier?.value,
        taskNo: params.taskNo,
        commandContentLike: params.commandContentLike,
        taskStatus: params.taskStatus?.value,
        createUser: params.createUser,
        createTimeStart: params.createTimeStart,
        createTimeEnd: params.createTimeEnd,
      },
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getTaskStatus = (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/getTaskStatusList',
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getDeviceStatus = (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/getTaskDetailStatusList',
      newGeteway: true,
    };
    return request(requestOptions);
  };

  cancelTask = (params: { taskNo: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/cancelTask',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getServerList = (params: {
    productKey: string;
    blockNo: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/thingModel/getThingModelServiceList',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getServerTSL = (params: {
    productKey: string;
    blockNo: string;
    identifier: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/thingModel/getThingModelAbility',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  createTask = (params: Object): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/createTask',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getDeviceList = (params: Object): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/device/get_issue_device_page_list',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  checkDevice = (params: {
    productKey: string;
    fileS3BucketName: string;
    fileS3Key: string;
    fileS3Md5: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/checkCommandTaskDeviceFile',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getTaskInfo = (params: { taskNo: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/getTaskDetail',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  getTaskDevice = (params: {
    pageNum: number;
    pageSize: number;
    productKey: any;
    executeStatus: string;

    productModels?: any[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/queryTaskDeviceDetails',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  pushDeviceAgain = (params: {
    taskNo: string;
    detailNoList: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/rePushDevice',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  stopDevice = (params: { detailNoList: string[] }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/commandTask/stopDevicesExecute',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
  getTemplateFile = (params: {
    templateType: 'device_batch_add_file' | 'device_choice_file';
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/device/get_s3_template_file',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
}

export default CommandControlFetch;
