export enum DropDownType {
  INPUT = 'INPUT',
  SELECT = 'SELECT',
  DATEPICKER = 'DATEPICKER',
  MULTIPLESELECT = 'MULTIPLESELECT',
  CASCADER = 'CASCADER',
}

export enum dropDownKey {
  // 配置模板管理
  positionList = 'positionList', // 所在位置
  enableEnumList = 'enableEnumList', // 模板状态
  productTypeList = 'productTypeList', // 所属产品
  // 车型配置管理
  vehicleTypeConfInfoStateList = 'vehicleTypeConfInfoStateList', // 车型配置状态

  // 车辆配置与发布
  vehicleOwnerUseCaseList = 'vehicleOwnerUseCaseList', // 车辆归属方
  confInfoStatusList = 'confInfoStatusList', // 配置发布状态
  issueTaskVehicleStatusList = 'issueTaskVehicleStatusList', // 最近一次升级结果
  pushStreamDeviceList = 'pushStreamDeviceList', // 推流设备
  videoCameraList = 'videoCameraList', // 推流相机
  vehicleBusinessTypeList = 'vehicleBusinessTypeList', // 车辆类型

  // 发布计划管理
  appNameList = 'appNameList',
  issueTaskStatusList = 'issueTaskStatusList', // 发布计划状态
}

export enum dropDownListKey {
  // 配置模板管理
  positionList = 'positionList', // 所在位置
  enableEnumList = 'enableEnumList', // 模板状态
  productTypeList = 'productTypeList', // 所属产品
  // 车型配置管理
  vehicleTypeConfInfoStateList = 'vehicleTypeConfInfoStateList', // 车型配置状态

  // 车辆配置与发布
  vehicleConfTemplateList = 'vehicleConfTemplateList',
  vehicleOwnerUseCaseList = 'vehicleOwnerUseCaseList', // 车辆归属方
  confInfoStatusList = 'confInfoStatusList', // 配置发布状态
  issueTaskVehicleStatusList = 'issueTaskVehicleStatusList', // 最近一次升级结果
  pushStreamDeviceList = 'pushStreamDeviceList', // 推流设备
  videoCameraList = 'videoCameraList', // 推流相机
  vehicleBusinessTypeList = 'vehicleBusinessTypeList', //  车辆类型

  // 发布计划管理
  appNameList = 'appNameList',
  issueTaskStatusList = 'issueTaskStatusList', // 发布计划状态
}

export enum ClstagKey {
  // 车辆配置与发布查询条件
  release_station = 'release_station', // 站点名称
  release_vehiclename = 'release_vehiclename', // 车牌号
  release_ownerUseCase = 'release_ownerUseCase', // 车辆归属方
  release_vehicleConfTemplate = 'release_vehicleConfTemplate', // 车辆配置模板
  release_vehicleConfIssueStatus = 'release_vehicleConfIssueStatus', // 配置发布状态
  release_latestIssueTaskResult = 'release_latestIssueTaskResult', // 最近一次升级结果
  release_pushStreamDevice = 'release_pushStreamDevice', // 推流设备
  release_videoCamera = 'release_videoCamera', // 推流相机
  release_vehicleType = ' release_vehicleType', // 车型名称
  release_vehicleClassification = ' release_vehicleClassification', // 车辆类型
  release_appName = 'release_appName', // 模块
  release_version = 'release_version', // 版本号
  release_province = 'release_province', // 省份
  release_city = 'release_city', // 城市

  // 车辆配置与发布操作按钮
  release_batchChangeConfig = 'release_batchChangeConfig', // 批量更新车辆配置
  release_configAndRelease = 'release_configAndRelease', // 配置及软件发布
  release_vehicleConfig = 'release_vehicleConfig', // 车辆配置
  release_configOperationRecord = 'release_configOperationRecord', // 配置操作记录
  release_releaseRecord = 'release_releaseRecord', // 发布记录
  release_viewReleasePlan = ' release_viewReleasePlan', // 查看发布计划
}
