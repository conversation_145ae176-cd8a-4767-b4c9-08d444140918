import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { CommonForm } from '@jd/x-coreui';
import { message } from 'antd';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

interface ModuleOption {
  productModelNo: string;
  productModelName: string;
}

interface ProductModuleSelectorProps {
  selectedData?: any;
  onChange?: (data: any) => void;
}

export interface ProductModuleSelectorRef {
  getSelectedData: () => Promise<{
    productKey: string;
    selectedModules: string[];
  }>;
}

// 产品选择表单配置
const ProductFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      labelInValue: false,
      validatorRules: [
        { required: true, message: '请选择产品' },
      ],
      style: { width: 300 },
      allowClear: true,
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const deviceApi = new Device();
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.productName,
              value: item.productKey,
            })) || [];
          }
          return [];
        },
      },
    ],
  },
};

const ProductModuleSelector = forwardRef<ProductModuleSelectorRef, ProductModuleSelectorProps>(
  ({ selectedData, onChange }, ref) => {
    const deviceApi = new Device();
    const formRef = React.useRef<any>(null);

    const [moduleOptions, setModuleOptions] = useState<ModuleOption[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<string>();
    const [selectedModules, setSelectedModules] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getSelectedData: async () => {
        if (!selectedProduct) {
          throw new Error('请选择产品');
        }
        if (selectedModules.length === 0) {
          throw new Error('请选择至少一个模块');
        }
        return {
          productKey: selectedProduct,
          selectedModules,
        };
      },
    }));

    // 当选中数据变化时，同步表单
    useEffect(() => {
      if (selectedData?.productKey) {
        setSelectedProduct(selectedData.productKey);
        formRef.current?.setFieldsValue({ productKey: selectedData.productKey });
        fetchModuleList(selectedData.productKey);
      }
      if (selectedData?.productModelNoList) {
        setSelectedModules(selectedData.productModelNoList);
      }
    }, [selectedData]);

    const fetchModuleList = async (productKey: string) => {
      setLoading(true);
      try {
        const res = await deviceApi.queryModelList(productKey);
        if (res.code === HttpStatusCode.Success) {
          setModuleOptions(res.data || []);
        } else {
          message.error(res.message || '获取模块列表失败');
          setModuleOptions([]);
        }
      } catch (error) {
        message.error('获取模块列表失败');
        setModuleOptions([]);
      } finally {
        setLoading(false);
      }
    };

    const handleFormChange = (values: any) => {
      if (values.productKey !== selectedProduct) {
        setSelectedProduct(values.productKey);
        setSelectedModules([]);
        setModuleOptions([]);
        if (values.productKey) {
          fetchModuleList(values.productKey);
        }
        
        // 通知父组件数据变化
        if (onChange) {
          onChange({
            productKey: values.productKey,
            selectedModules: [],
          });
        }
      }
    };

    const handleModuleClick = (moduleNo: string) => {
      const newSelectedModules = selectedModules.includes(moduleNo)
        ? selectedModules.filter(item => item !== moduleNo)
        : [...selectedModules, moduleNo];
      
      setSelectedModules(newSelectedModules);
      
      // 通知父组件数据变化
      if (onChange) {
        onChange({
          productKey: selectedProduct,
          selectedModules: newSelectedModules,
        });
      }
    };

    const renderModuleTabs = () => {
      if (moduleOptions.length === 0) {
        return <div className="no-modules">请先选择产品</div>;
      }

      return (
        <div className="module-tabs">
          <div className="module-tabs-header">
            <span className="label">模块选择：</span>
            <span className="tip">（点击选择模块，支持多选）</span>
          </div>
          <div className="module-tabs-content">
            {moduleOptions.map((module) => (
              <div
                key={module.productModelNo}
                className={`module-tab ${
                  selectedModules.includes(module.productModelNo) ? 'selected' : ''
                }`}
                onClick={() => handleModuleClick(module.productModelNo)}
              >
                {module.productModelName}
              </div>
            ))}
          </div>
          {selectedModules.length > 0 && (
            <div className="selected-info">
              已选择 {selectedModules.length} 个模块
            </div>
          )}
        </div>
      );
    };

    return (
      <div className="product-module-selector">
        <div className="section-title">选择产品及模块</div>
        <div className="section-content">
          <CommonForm
            formConfig={ProductFormConfig}
            onRef={(ref) => (formRef.current = ref)}
            formType="search"
            layout="vertical"
            defaultValue={{
              fetchProductKey: true,
              productKey: selectedData?.productKey,
            }}
            onValuesChange={handleFormChange}
          />
          
          {renderModuleTabs()}
        </div>
      </div>
    );
  }
);

ProductModuleSelector.displayName = 'ProductModuleSelector';

export default ProductModuleSelector;
