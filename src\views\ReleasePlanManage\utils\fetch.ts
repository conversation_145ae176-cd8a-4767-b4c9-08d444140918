import { request } from '@/fetch/core';

export class ReleasePlanManageFetch {
  public fetchVersionDropDown = (params: { appName: string }) => {
    const requestParams: RequestOptions = {
      path: '/ota/web/application_version_get_list',
      body: {
        appName: params.appName,
        enable: 1,
      },
      method: 'POST',
    };
    return request(requestParams);
  };

  public fetchModuleUpgradeRes = (params: {
    vehicleName: string;
    issueTaskNumber: string;
  }) => {
    const requestParams: RequestOptions = {
      path: '/ota/web/app_upgrade_result',
      body: params,
      method: 'POST',
    };
    return request(requestParams);
  };
}
