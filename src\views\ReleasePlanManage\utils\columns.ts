import { SearchFormConfigData } from '@/components/Searchform';
import moment from 'moment';
import {
  DropDownType,
  dropDownKey,
  dropDownList<PERSON>ey,
  ClstagKey,
} from '@/utils/searchFormEnum';

export const TableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '发布计划编号',
    width: 200,
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间设置',
    width: 120,
    dataIndex: 'isImmediatelyName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    width: 180,
    dataIndex: 'issueTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布模块',
    width: 100,
    dataIndex: 'issueAppAliasList',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发版描述',
    dataIndex: 'issueDescription',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '发布计划状态',
    width: 120,
    dataIndex: 'statusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布车辆数',
    width: 110,
    dataIndex: 'issueVehicleCount',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '成功/失败/取消',
    width: 150,
    dataIndex: 'issueVehicleResult',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    width: 140,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 180,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 150,
    dataIndex: 'operate',
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const searchConfig: SearchFormConfigData[] = [
  {
    name: 'number',
    title: '发布计划编号',
    placeHolder: '请输入关键字',
    type: DropDownType.INPUT,
    xxl: 8,
    xl: 8,
  },
  {
    name: 'status',
    title: '发布计划状态',
    placeHolder: '请选择',
    type: DropDownType.SELECT,
    dropDownKey: dropDownKey.issueTaskStatusList,
    dropDownListKey: dropDownListKey.issueTaskStatusList,
    xxl: 8,
    xl: 8,
  },
  {
    name: 'modifyUser',
    title: '最后操作人',
    placeHolder: '请输入用户名',
    type: DropDownType.INPUT,
    checkInputRules: ['number', 'letter'],
    xxl: 8,
    xl: 8,
  },
  {
    name: 'productType',
    title: '产品类型',
    type: DropDownType.SELECT,
    placeHolder: '请选择产品类型',
  },
  {
    name: 'cascader',
    title: '发布版本号',
    type: DropDownType.CASCADER,
    cascaderList: [
      {
        name: 'appName',
        placeholder: '请选择模块',
        xxl: 8,
        xl: 8,
      },
      {
        name: 'version',
        placeholder: '请输入版本号，支持关键字联想全称',
        xxl: 16,
        xl: 16,
      },
    ],
    xxl: 12,
    xl: 12,
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  },
  {
    name: 'issueTime',
    title: '发布时间',
    showTime: {
      defaultValue: [
        moment('00:00:00', 'HH:mm:ss'),
        moment('00:00:00', 'HH:mm:ss'),
      ],
    },
    type: DropDownType.DATEPICKER,
    xxl: 12,
    xl: 12,
    labelCol: { span: 3 },
    wrapperCol: { span: 21 },
  },
];

export const UpgradeResTableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 30 },
  {
    title: '车牌号',
    width: 50,
    dataIndex: 'vehicleName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 120,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    width: 120,
    dataIndex: 'issueTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点',
    width: 120,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '城市',
    width: 40,
    dataIndex: 'cityName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆归属方',
    width: 60,
    dataIndex: 'ownerUseCaseName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆升级结果',
    width: 50,
    dataIndex: 'statusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 120,
    dataIndex: 'moduleUpgradeRes',
    align: 'center',
    ellipsis: true,
  },
];

export const UpgradeResSearchConfig: any[] = [
  {
    name: 'city',
    title: '所在城市',
    placeHolder: '请选择所在城市',
    type: DropDownType.SELECT,
    childrenList: ['station'],
    clearChild: true,
    showParent: true,
  },
  {
    name: 'station',
    title: '站点名称',
    placeHolder: '请选择站点名称',
    type: DropDownType.SELECT,
    parentList: ['city'],
  },
  {
    name: 'vehicleName',
    title: '车牌号',
    placeHolder: '请输入关键字',
    type: DropDownType.INPUT,
  },
  {
    name: 'ownerUseCase',
    title: '车辆归属方',
    placeHolder: '请选择（支持多选）',
    type: DropDownType.MULTIPLESELECT,
    dropDownKey: dropDownKey.vehicleOwnerUseCaseList,
    dropDownListKey: dropDownListKey.vehicleOwnerUseCaseList,
  },
  {
    name: 'latestIssueTaskResult',
    title: ' 车端升级结果  ',
    placeHolder: '请选择',
    type: DropDownType.SELECT,
    dropDownKey: dropDownKey.issueTaskVehicleStatusList,
    dropDownListKey: dropDownListKey.issueTaskVehicleStatusList,
  },
  {
    name: 'serialNo',
    title: '车架号',
    placeHolder: '请输入车架号',
    type: DropDownType.INPUT,
  },
];

export const CheckTableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 20 },
  {
    title: '模块',
    width: 30,
    dataIndex: 'appAlias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '版本号',
    width: 120,
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '系统版本编号',
    width: 120,
    dataIndex: 'versionNumber',
    align: 'center',
    ellipsis: true,
  },
];

export const CheckModuleUpgradeRes: any[] = [
  {
    title: '模块名称',
    width: 200,
    dataIndex: 'appAlias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '升级状态',
    width: 200,
    dataIndex: 'progressStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '失败原因',
    width: 200,
    dataIndex: 'failReason',
    align: 'center',
    ellipsis: true,
  },
];
