.select-instruction {
  .number-container {
    display: flex;
  }
  .ant-input-number {
    width: 100%;
  }
  .value-range {
    margin-left: 10px;
    margin-top: 5px;
  }

  .struct-info {
    .obj-title {
      height: 32px;
      line-height: 32px;
    }
  }
  .array-add-btn {
    margin-bottom: 10px;
    button {
      margin-right: 10px;
    }
    .tip {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .array-basic {
    .array-row {
      display: flex;
    }
  }

  .array-struct {
    .array-row {
      display: flex;
    }
    .array-col {
      padding: 10px 25px;
      border: 1px solid #d9d9d9;
    }
  }
}
