/**
 * 配置类数据类型定义
 */

/**
 * 配置类项接口
 * 根据图片中的表格内容定义
 */
export interface ConfigType {
  /**
   * 产品Key
   * 备注：productKey + itemTypeNo列表唯一
   */
  productKey: string;

  /**
   * 产品名称
   */
  productName: string;

  /**
   * 标识符
   * 备注：productKey + itemTypeNo列表唯一
   */
  itemTypeNo: string;

  /**
   * 配置类名称
   */
  name: string;

  /**
   * 描述
   */
  description?: string;
}

/**
 * 配置类列表数据接口
 */
export interface ConfigTypeListData {
  /**
   * 配置类列表
   */
  data: ConfigType[];
  code: string;
  message: string;
}

/**
 * 配置类新增请求参数
 */
export interface AddConfigTypeParams {
  /**
   * 产品Key
   */
  productKey: string;

  /**
   * 标识符
   */
  itemTypeNo: string;

  /**
   * 配置类名称
   */
  name: string;

  /**
   * 描述
   */
  description?: string;
}
