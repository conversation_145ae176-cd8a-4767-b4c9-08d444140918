import { request } from '@/fetch/core';

export class VehicleConfigAndReleaseFetch {
  public fetchVersionDropDown = (params: {
    appName: string;
    enable: number;
  }) => {
    const requestParams: RequestOptions = {
      path: '/ota/web/application_version_get_list',
      body: {
        appName: params.appName,
        enable: params.enable,
      },
      method: 'POST',
    };
    return request(requestParams);
  };
}
