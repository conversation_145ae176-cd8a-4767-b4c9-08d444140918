import React, { useState, useEffect } from 'react';
import { Tabs, Table } from 'antd';
import { useNavigate } from 'react-router-dom';
import { formatLocation } from '@/utils/formatLocation';
import FormTitle from '@/components/FormTitle';
import BreadCrumb from '@/components/BreadCrumb';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import FileRecord from '../components/FileRecord';
import { TabType, tabMenu } from '../utils/constant';
import { recordColumns } from '../utils/columns';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';
const { TabPane } = Tabs;
const BreadCrumbItemsMap = new Map([
  ['confRecord', [{ title: 'OTA管理', route: '' }, { title: '车辆配置与发布', route: '' }, { title: '配置操作记录', route: '' }]],
])
const ConfRecord = () => {
  const navigator = useNavigate()
  const urlData = formatLocation(window.location.search)
  const [activeTab, setActiveTab] = useState<string>(TabType.CURRENT_RECORD)
  const [tabContent, setTabContent] = useState<any[]>([])
  const [checkModalShow, setCheckModalShow] = useState<boolean>(false)
  const [conf, setConf] = useState<any>()
  const [loading, setLoading] = useState<boolean>(false)
  useEffect(() => {
    fetchConfRecord(TabType.CURRENT_RECORD)
  }, [])
  const fetchConfRecord = (isExist: any) => {
    setLoading(true)
    try {
      request({
        method: 'POST',
        path: api.getVehicleConfRecordList,
        body: {
          vehicleName: urlData.vehicleName,
          isExist: isExist
        }
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTabContent(res.data)
        }
      })
    } catch (e) {
      console.log(e)
    } finally {
      setLoading(false)
    }
  }
  const makeColumns = () => {
    return recordColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${index + 1}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className='operate-btn'>
                <a onClick={() => {
                  checkRecord(record)
                }}>查看记录</a>
              </div>
            )
          }
          break;
        default:
          col.render = (text: any) => `${text || '-'}`
          break;
      }
      return col
    })
  }
  // 点击查看记录
  const checkRecord = (value: any) => {
    setConf(value)
    setCheckModalShow(true)
  }
  // 切换标签页
  const onTabsChange = (key: any) => {
    setActiveTab(key)
    fetchConfRecord(key)
    setTabContent([])
  }
  return <div className='conf-record'>
    <BreadCrumb items={BreadCrumbItemsMap.get('confRecord')} />
    <div className='record-content'>
      <FormTitle title={`${urlData.vehicleName}配置操作记录`} />
      <div className="card-title">
        <Tabs activeKey={activeTab} onChange={onTabsChange}>
          {
            tabMenu?.map((item: any) => {
              return <TabPane tab={item.name} key={item.key} ></TabPane>
            })
          }
        </Tabs>
      </div>
      <div className='card-content'>
        <Table
          loading={loading}
          dataSource={tabContent}
          bordered
          rowKey={(record) => record.version}
          columns={makeColumns()}
          pagination={false}
        />
      </div>
      <div className='history-btn'>
        <CustomButton
          buttonType={ButtonType.DefaultButton}
          otherCSSProperties={{ marginLeft: '20px' }}
          onSubmitClick={() => navigator('/vehicleConfigAndRelease')}
          title={'返回'} />
      </div>
    </div>
    {
      checkModalShow && <FileRecord
        checkModalShow={checkModalShow}
        onCancel={() => {
          setCheckModalShow(false)
        }}
        conf={conf}
        vehicleName={urlData.vehicleName}
      />
    }
  </div>
}

export default React.memo(ConfRecord);