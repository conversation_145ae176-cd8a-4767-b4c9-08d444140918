import React, { useState, useEffect } from 'react';
import { Col, Form, Input, Row, Select, DatePicker, FormInstance, Button } from 'antd';
import { SearchFormFetch } from './utils/fetch';
import { HttpStatusCode } from '@/fetch/core/constant';
const { RangePicker } = DatePicker;
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import './index.scss';
import { BuryPointClstagMap } from './utils/constant';
import {
  DropDownType,
  dropDownKey,
  dropDownListKey,
  ClstagKey,
} from '@/utils/searchFormEnum';
import { CustomButton, ButtonType } from '../CustomButton';

interface CascaderItem {
  name: string;
  placeholder?: string;
  xxl?: number;
  xl?: number;
  clstagKey?: ClstagKey;
  onFocus?: () => void;
}
export interface SearchFormConfigData {
  name?: 'station' | 'province' | 'city' | 'country' | string;
  title: string;
  placeHolder?: string;
  type: DropDownType;
  showSearch?: boolean;
  maxLength?: number;
  dropDownKey?: dropDownKey; // 通过getCommonDrownList接口获取下拉框内容传dropDownKey
  dropDownListKey?: dropDownListKey; // 通过getCommonDrownList接口获取下拉框内容传dropDownListKey
  childrenList?: ('station' | 'province' | 'city' | 'country' | 'stop')[];
  parentList?: ('station' | 'province' | 'city' | 'country')[];
  showParent?: boolean;
  clearChild?: boolean;
  onFocus?: Function;
  dataLevel?: 'COMPANY' | 'PERSON'; // 获取公司或者账户下站点COMPNAY-公司下,PERSON-账户下
  enable?: 0 | 1;
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  xxl?: number;
  xl?: number;
  showTime?: Object | boolean; // 日期选择器是否精确到时分秒
  maxTagCount?: number; // 多选最多显示多少个 tag
  clstagKey?: ClstagKey;
  checkInputRules?: ('phone' | 'number' | 'letter')[];
  cascaderList?: Array<CascaderItem>; // 级联选择器
}

// key与对应下拉框配置项configData里的name保持一致
export interface DropDownMapType {
  [key: string]: { label: any; value: any }[];
}

interface Props {
  configData: SearchFormConfigData[]; // 搜索框表单的配置项
  onResetClick?: Function; // 点击重置
  onSearchClick?: Function; // 点击查询
  initValues?: any; // 搜索条件的初始值
  formRef: FormInstance;
  dropDownMap?: DropDownMapType; // getCommonDrownList接口无法获取到下拉框列表，从页面中传过来
  noResetBtn?: boolean; // 是否没有重置按钮
  noSearchBtn?: boolean; // 是否没有查询按钮
  colon?: boolean;
  title?: string;
  children?: any;
}

interface Options {
  label: any;
  value: any;
}

const Searchform = ({
  title,
  colon,
  noResetBtn,
  dropDownMap,
  formRef,
  configData,
  onSearchClick,
  onResetClick,
  initValues,
  noSearchBtn,
  children,
}: Props) => {
  const linkedDepartmentList = ['station', 'province', 'city', 'country'];
  const fetchApi = new SearchFormFetch();
  const [currentList, setCurrentList] = useState<Options[]>([]);
  const [currentLoading, setCurrentLoading] = useState<boolean>(false);

  useEffect(() => {
    if (initValues) {
      formRef.setFieldsValue(initValues);
    }
  }, [JSON.stringify(initValues)]);

  const makeLayout = () => {
    let titleMaxLength = 4;
    configData?.forEach((item: any) => {
      if (
        (item.title?.length && item.title?.length > titleMaxLength) ||
        (item.label?.length && item.label?.length > titleMaxLength)
      ) {
        titleMaxLength = item.title.length || item.label.length;
      }
    });
    titleMaxLength = titleMaxLength >= 9 ? 9 : titleMaxLength;
    return {
      labelCol: { span: titleMaxLength + 1 },
      wrapperCol: { span: 24 - (titleMaxLength + 1) },
    };
  };

  const getDropDownList = async (item: any) => {
    setCurrentLoading(true);
    try {
      if (item.dropDownKey) {
        const res: any = await fetchApi.getCommonDropDown([item.dropDownKey]);
        if (res && res.code === HttpStatusCode.Success) {
          setCurrentList(
            res.data[item.dropDownListKey]?.map((item: any) => {
              return {
                label: item.name,
                value: item.code,
              };
            })
          );
        }
      }
    } catch (e) {
      console.log(e);
    } finally {
      setCurrentLoading(false);
    }
  };

  // 当点击大区、省份、城市、站点、停靠点下拉框时
  const getDepartmentList = async (item: any) => {
    setCurrentLoading(true);
    try {
      const data = item.parentList ? formRef.getFieldsValue(item.parentList) : null;
      // 获取当前项的下拉框内容
      const res: any = await fetchApi.getCurrentDownList({
        level: item.name,
        countryId: data?.country?.value,
        provinceId: data?.province?.value,
        cityId: data?.city?.value,
        enable: item.enable === 1 || item.enable === 0 ? item.enable : null,
      });
      if (res && res.code === HttpStatusCode.Success) {
        setCurrentList(
          res.data?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          })
        );
      }
    } catch (e) {
      console.log(e);
    } finally {
      setCurrentLoading(false);
    }
  };

  // 当选中值变化
  const onValueChange = async (item: any, value: any) => {
    // 清空子级
    if (item.childrenList && item.clearChild) {
      formRef.resetFields(item.childrenList);
    }
    // 获取父辈节点
    if (item.showParent && item.parentList) {
      const res: any = await fetchApi.getParentLinked({
        id: value?.value,
        level: item.name,
      });
      if (res && res.code === HttpStatusCode.Success) {
        item.parentList?.forEach((item: any) => {
          const data = res.data.filter((value: any) => value.level === item)[0];
          formRef.setFieldsValue({
            [item]: {
              key: data.code,
              value: data.code,
              label: data.name,
            },
          });
        });
      }
    }
  };

  const makeSelectOptions = (item: any) => {
    let list: any[] = [];
    if (!item.dropDownKey && dropDownMap) {
      list = item.name && dropDownMap[item.name];
    }
    return list;
  };

  const checkInputValue = (inputValue: any, checkInputRules: string[]) => {
    let newValue = null;
    if (checkInputRules?.length <= 1) {
      if (checkInputRules?.indexOf('number') > -1) {
        newValue = inputValue.replace(/[^\-?\d.]/g, '');
      }
      if (checkInputRules?.indexOf('phone') > -1) {
        newValue = inputValue.replace(/[^0-9-]+/, '');
      }
    }
    if (
      checkInputRules?.indexOf('number') > -1 &&
      checkInputRules?.indexOf('letter') > -1
    ) {
      newValue = inputValue.replace(/[\W]/g, '');
    }
    return newValue;
  };

  const renderFieldItem = (item: any) => {
    let bar = null;
    switch (item.type) {
      case DropDownType.SELECT:
        bar = (
          <Select
            style={{ textAlign: 'left' }}
            labelInValue
            options={
              item.dropDownKey || linkedDepartmentList.includes(item.name)
                ? currentList
                : makeSelectOptions(item)
            }
            filterOption={(input: any, option: any) => {
              const label: any = option?.label || '';
              return label.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            allowClear
            showSearch={item.showSearch ?? true}
            placeholder={item.placeHolder}
            loading={currentLoading}
            onFocus={() => {
              item.onFocus && item.onFocus(); // 自定义下拉框列表
              linkedDepartmentList.includes(item.name) && getDepartmentList(item); // 四级联动
              item.dropDownKey && getDropDownList(item); // 公共下来列表
            }}
            onBlur={() => {
              (item.dropDownKey || linkedDepartmentList.includes(item.name)) &&
                setCurrentList([]);
            }}
            onChange={(value: any) => {
              linkedDepartmentList.includes(item.name) && onValueChange(item, value);
            }}
            onClear={() => item.onClear && item.onClear()}
          />
        );
        break;
      case DropDownType.DATEPICKER:
        bar = (
          <RangePicker
            showTime={
              typeof item.showTime === 'boolean' ? item.showTime : { ...item.showTime }
            }
            locale={locale}
            format="YYYY-MM-DD HH:mm:ss"
            style={{ width: '100%', minWidth: item.showTime && '360px' }}
          />
        );
        break;
      case DropDownType.INPUT:
        bar = (
          <Input
            allowClear
            placeholder={item.placeHolder ?? ''}
            onChange={(e: any) => {
              const inputValue = e.target.value?.trim();
              const newValue = item.checkInputRules
                ? checkInputValue(inputValue, item.checkInputRules)
                : inputValue;
              formRef?.setFieldsValue({
                [item.name]: newValue,
              });
              item.onChange && item.onChange(newValue);
            }}
            maxLength={
              item.maxLength
                ? item.maxLength
                : item.checkInputRules?.indexOf('phone') > -1
                ? 11
                : 50
            }
            onFocus={() => {
              item.onFocus && item.onFocus();
            }}
          />
        );
        break;
      case DropDownType.MULTIPLESELECT:
        bar = (
          <Select
            style={{ textAlign: 'left' }}
            mode="multiple"
            maxTagCount={item.maxTagCount ?? 1}
            options={
              item.dropDownKey || linkedDepartmentList.includes(item.name)
                ? currentList
                : makeSelectOptions(item)
            }
            labelInValue
            allowClear
            showSearch={item.showSearch ?? true}
            placeholder={item.placeHolder}
            loading={currentLoading}
            filterOption={(input: any, option: any) => {
              const label: any = option?.label || '';
              return label.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            onFocus={() => {
              item.onFocus && item.onFocus(); // 自定义下拉框列表
              linkedDepartmentList.includes(item.name) && getDepartmentList(item); // 四级联动
              item.dropDownKey && getDropDownList(item); // 公共下来列表
            }}
            onBlur={() => {
              (item.dropDownKey || linkedDepartmentList.includes(item.name)) &&
                setCurrentList([]);
            }}
            onChange={(value: any) => {
              linkedDepartmentList.includes(item.name) && onValueChange(item, value);
            }}
            onClear={() => item.onClear && item.onClear()}
          />
        );
        break;
      case DropDownType.CASCADER:
        bar = (
          <Row gutter={8} style={{ width: '100%' }}>
            {item.cascaderList?.map((cascaderItem: any, index: any) => {
              return (
                <Col
                  key={cascaderItem.name}
                  xxl={cascaderItem.xxl ?? Math.floor(24 / item.cascaderList?.length)}
                  xl={cascaderItem.xl ?? Math.floor(24 / item.cascaderList?.length)}
                  lg={24}
                  md={24}
                >
                  <div
                    className={`searchform-field-item searchform-field-item_${cascaderItem.name}`}
                    clstag={`h|keycount|${BuryPointClstagMap.get(
                      cascaderItem.clstagKey
                    )}`}
                  >
                    <Form.Item name={cascaderItem.name}>
                      <Select
                        style={{ textAlign: 'left' }}
                        labelInValue
                        options={makeSelectOptions(cascaderItem)}
                        filterOption={(input: any, option: any) => {
                          const label: any = option?.label || '';
                          return (
                            label.toString().toLowerCase().indexOf(input.toLowerCase()) >=
                            0
                          );
                        }}
                        allowClear
                        showSearch={true}
                        placeholder={cascaderItem.placeholder}
                        onFocus={() => {
                          cascaderItem.onFocus && cascaderItem.onFocus();
                        }}
                        onChange={(value: any) => {
                          if (index < item.cascaderList.length - 1) {
                            const clearList = item.cascaderList.slice(index + 1);
                            formRef.resetFields(
                              clearList?.map((value: any) => value.name)
                            );
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                </Col>
              );
            })}
          </Row>
        );
        break;
      default:
        return null;
    }
    return bar;
  };

  return (
    <div className="searchform-container">
      <Row>
        <Col span={20}>
          <Form
            {...makeLayout()}
            form={formRef}
            colon={colon ? colon : false}
            size="middle"
          >
            <Row gutter={24} align="middle" style={{ width: '100%' }}>
              {configData?.map((item: any, subIndex: any) => {
                return (
                  <Col
                    xxl={item.xxl ? item.xxl : 6}
                    xl={item.xl ? item.xl : 8}
                    lg={item.lg ? item.lg : 12}
                    md={item.md ? item.md : 24}
                    key={subIndex}
                  >
                    <div
                      className={`searchform-field-item searchform-field-item_${item.name}`}
                      clstag={`h|keycount|${BuryPointClstagMap.get(item.clstagKey)}`}
                    >
                      <Form.Item
                        key={subIndex}
                        name={item.name}
                        label={<div className="label-name">{item.title}</div>}
                        wrapperCol={item.wrapperCol && item.wrapperCol}
                        labelCol={item.labelCol && item.labelCol}
                      >
                        {renderFieldItem(item)}
                      </Form.Item>
                    </div>
                  </Col>
                );
              })}
            </Row>
          </Form>
        </Col>
        <Col span={4}>
          <Row style={{ height: '100%' }} justify="center" align="middle">
            <CustomButton
              title="查询"
              onSubmitClick={() => onSearchClick && onSearchClick()}
            />
            {noResetBtn ? null : (
              <CustomButton
                title="重置"
                buttonType={ButtonType.DefaultButton}
                otherCSSProperties={{ marginLeft: '15px', marginRight: '20px' }}
                onSubmitClick={() => {
                  onResetClick && onResetClick();
                }}
              />
            )}
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(Searchform);
