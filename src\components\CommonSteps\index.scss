.common-steps {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  .step-bar {
    display: flex;
    margin-bottom: 20px;
    .step-container {
      display: flex;
    }
    .circle-and-title {
      width: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .circle {
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.06);
        border-color: transparent;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        &.cur {
          border: 1px solid #1677ff;
          background-color: #1677ff;
          color: #ffffff;
        }
        &.finished {
          border: 1px solid #e6f4ff;
          background-color: #e6f4ff;
          color: #1677ff;
        }
      }
      .title {
        margin-top: 10px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.45);
        white-space: nowrap;
        &.cur {
          color: rgba(0, 0, 0, 0.88);
        }
        &.finished {
          color: rgba(0, 0, 0, 0.88);
        }
      }
    }
    .line {
      width: 160px;
      height: 2px;
      background-color: rgba(5, 5, 5, 0.06);
      margin-top: 14px;
      margin-left: 20px;
      margin-right: 20px;
      &.finished {
        background-color: #1677ff;
      }
    }
  }
  .content {
    width: 100%;
  }
  .bottom-btns {
    margin-top: 20px;
    button {
      margin-left: 5px;
      margin-right: 5px;
    }
  }
}
