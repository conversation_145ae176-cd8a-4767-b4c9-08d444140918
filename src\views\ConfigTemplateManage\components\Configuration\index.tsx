import React, { useEffect, useRef, useState } from 'react';
import {
  Tree,
  Table,
  Input,
  Button,
  Space,
  Tabs,
  message,
  Select,
  Form,
} from 'antd';
import type { DataNode } from 'antd/es/tree';
import type { TableRowSelection } from 'antd/es/table/interface';
import './index.scss';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import showModal from '@/components/commonModal';
import TextArea from 'antd/es/input/TextArea';

interface FileItem {
  fileNo: string;
  fileName: string;
  blockNo: string;
  blockName: string;
}

interface ItemType {
  itemNo: string;
  itemName: string;
}

interface ItemsType {
  itemTypeNo: string;
  itemTypeName: string;
  itemList: ItemType[];
}

interface BlockData {
  blockNo: string;
  blockName: string;
  itemsTypeList: ItemsType[];
  fileList: FileItem[];
}
export interface SelectedData {
  product: Object;
  blockData: {
    blockNo: string;
    selectedItems: string[]; // 选中的结构化配置项
    selectedFiles: string[]; // 选中的配置文件
  }[];
}
const ViewConfigFileModal: React.FC<{ data: any }> = ({ data }) => {
  const [form] = Form.useForm();

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={data}
      className="view-config-file-form"
    >
      <Form.Item
        label={<span className="required-label">产品模块</span>}
        required
      >
        <Input.Group compact>
          <Form.Item name="productKey" noStyle>
            <Select
              style={{ width: '50%' }}
              disabled
              options={[{ label: data.productName, value: data.productKey }]}
            />
          </Form.Item>
          <Form.Item name="blockName" noStyle>
            <Select
              style={{ width: '50%' }}
              disabled
              options={[{ label: data.blockName, value: data.blockNo }]}
            />
          </Form.Item>
        </Input.Group>
      </Form.Item>

      <Form.Item
        label={<span className="required-label">配置文件编号</span>}
        name="fileNo"
        required
      >
        <Input disabled />
      </Form.Item>

      <Form.Item
        label={<span className="required-label">配置文件名称</span>}
        name="fileName"
        required
      >
        <Input disabled />
      </Form.Item>

      <Form.Item
        label={<span className="required-label">配置路径</span>}
        name="filePath"
        required
      >
        <Input disabled />
      </Form.Item>
      <Form.Item label="描述" name="description">
        <TextArea
          disabled
          autoSize={{ minRows: 3, maxRows: 5 }}
          maxLength={100}
          showCount
        />
      </Form.Item>

      <Form.Item
        label={<span className="required-label">模板内容</span>}
        name="content"
        required
      >
        <TextArea
          disabled
          autoSize={{ minRows: 6, maxRows: 10 }}
          maxLength={10000}
          showCount
        />
      </Form.Item>
    </Form>
  );
};

const Configuration: React.FC<{
  onRef?: (ref: {
    checkAndGetSelectedData: () => Promise<SelectedData>;
  }) => void;
}> = ({ onRef }) => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const deviceApi = useRef(new Device());
  // 创建ref来存储选中的产品和tabStates信息
  const selectedDataRef = useRef<{
    selectedProduct?: any;
    tabStates: Record<string, any>;
  }>({
    selectedProduct: {},
    tabStates: {},
  });
  const [productOptions, setProductOptions] = useState([]);
  const [configData, setConfigData] = useState<BlockData[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>();
  const [activeBlockNo, setActiveBlockNo] = useState<string | undefined>(
    configData[0]?.blockNo,
  );
  // 为每个tab维护独立的状态
  const [tabStates, setTabStates] = useState<any>({});

  useEffect(() => {
    fetchProductList();
  }, []);

  // 暴露方法给父组件
  useEffect(() => {
    if (onRef) {
      onRef({ checkAndGetSelectedData });
    }
  }, [onRef]);

  // 校验并获取选中数据
  const checkAndGetSelectedData = async (): Promise<SelectedData> => {
    // 使用ref中存储的数据
    const { selectedProduct, tabStates } = selectedDataRef.current;
    // 1. 校验产品是否选择
    if (!selectedProduct?.value) {
      throw new Error('请选择产品');
    }

    // 2. 校验是否至少选择了一个配置项或配置文件
    const hasAnySelection = Object.values(tabStates).some((info: any) => {
      return (
        (info?.selectedKeys?.length || 0) > 0 ||
        (info?.selectedFileRows?.length || 0) > 0
      );
    });

    if (!hasAnySelection) {
      throw new Error('请至少选择一个结构化配置项或配置文件');
    }

    // 3. 构建返回数据
    const blockData = Object.keys(tabStates).map((key) => {
      const info = tabStates[key];
      return {
        blockNo: key,
        selectedItems: info?.selectedKeys || [],
        selectedFiles: info?.selectedFileRows || [],
      };
    });

    // 4. 返回所有选中的数据
    return {
      product: selectedProduct,
      blockData,
    };
  };

  // 获取产品列表
  const fetchProductList = async () => {
    try {
      const res = await deviceApi.current.queryProductList();
      if (res.code === HttpStatusCode.Success) {
        const productOptions =
          res.data?.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          })) || [];
        setProductOptions(productOptions);
      } else {
        message.error(res.message || '获取产品列表失败');
      }
    } catch (error) {
      message.error('获取产品列表失败');
    }
  };

  // 获取配置列表数据
  const fetchConfigData = async (val: string) => {
    setLoading(true);
    try {
      const res =
        await configTemplateApi.current.getAllEnableConfigBaseInfoList({
          productKey: val,
        });

      if (res && res.code === HttpStatusCode.Success) {
        setConfigData(res.data);
        // 设置默认选中第一个tab
        if (res.data && res.data.length > 0) {
          setActiveBlockNo(res.data[0].blockNo);
          // 初始化每个tab的状态
          const initialStates: Record<string, any> = {};
          res.data.forEach((block: BlockData) => {
            initialStates[block.blockNo] = {
              selectedKeys: [],
              selectedFileRows: [],
              searchText: '',
              filteredFileList: block.fileList,
            };
          });
          setTabStates(initialStates);

          // 同步更新ref中的tabStates数据
          selectedDataRef.current = {
            ...selectedDataRef.current,
            tabStates: initialStates,
          };
        }
      } else {
        message.error(res?.message || '获取配置列表失败');
      }
    } catch (error) {
      message.error('获取配置列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理产品选择
  const handleProductChange = (value: any) => {
    setSelectedProduct(value);
    setConfigData([]);
    setTabStates({});
    setActiveBlockNo(undefined);

    // 同步更新ref中的数据
    selectedDataRef.current = {
      ...selectedDataRef.current,
      selectedProduct: value,
      tabStates: {},
    };

    if (value) {
      fetchConfigData(value);
    }
  };

  // 获取当前tab的状态
  const getCurrentTabState = () => activeBlockNo && tabStates[activeBlockNo];
  // 更新当前tab的状态
  const updateCurrentTabState = (updates: any) => {
    if (activeBlockNo) {
      // 更新状态
      setTabStates((prev: any) => {
        const newTabStates = {
          ...prev,
          [activeBlockNo]: {
            ...prev[activeBlockNo],
            ...updates,
          },
        };

        // 同步更新ref中的数据
        selectedDataRef.current = {
          ...selectedDataRef.current,
          tabStates: newTabStates,
        };

        return newTabStates;
      });
    }
  };

  // 构建树形数据
  const buildTreeData = (itemsTypeList: ItemsType[]): DataNode[] => {
    return itemsTypeList.map((type) => ({
      key: type.itemTypeNo,
      title: type.itemTypeName,
      checkable: false, // 配置类不可选
      children: type.itemList.map((item) => ({
        key: item.itemNo,
        title: item.itemName,
        checkable: true, // 允许选择
      })),
    }));
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '模块',
      dataIndex: 'blockName',
      key: 'blockName',
    },
    {
      title: '配置文件编码',
      dataIndex: 'fileNo',
      key: 'fileNo',
    },

    {
      title: '配置文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <a onClick={() => handleCheck(record.fileNo)}>查看</a>
        </Space>
      ),
    },
  ];

  const handleCheck = async (v: string) => {
    const res = await configTemplateApi.current.getConfigFile({
      fileNo: v,
      productKey: selectedProduct?.value!,
    });
    if (res.code !== HttpStatusCode.Success) {
      message.error(res.message);
      return;
    }
    showModal({
      title: '查看配置文件',
      content: <ViewConfigFileModal data={res.data} />,
      width: '800px',
      footer: [
        {
          text: '返回',
          type: 'cancelBtn',
          onClick: (cb: () => void) => {
            cb();
          },
        },
      ],
    });
  };
  const getCurrentBlock = () => {
    return configData.find((block) => block.blockNo === activeBlockNo);
  };
  // 表格选择配置
  const rowSelection: TableRowSelection<FileItem> = {
    selectedRowKeys: getCurrentTabState()?.selectedFileRows ?? [],
    onChange: (selectedRowKeys) => {
      updateCurrentTabState({
        selectedFileRows: selectedRowKeys as string[],
      });
    },
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    const currentBlock = getCurrentBlock();
    if (!currentBlock) return;

    let filteredList = currentBlock.fileList;

    // 如果有搜索内容，进行过滤
    if (value.trim()) {
      const searchText = value.toLowerCase();
      filteredList = currentBlock.fileList.filter(
        (file: any) =>
          file.fileName.toLowerCase().includes(searchText) ||
          file.fileNo.toLowerCase().includes(searchText),
      );
    }

    // 更新状态时只更新搜索文本和过滤后的列表，保持已选项不变
    updateCurrentTabState({
      searchText: value,
      filteredFileList: filteredList,
      // 不更新 selectedFileRows，保持原有选中状态
    });
  };

  // 渲染配置项选择部分
  const renderModuleSection = () => {
    const { selectedKeys } = getCurrentTabState();
    const currentBlock = getCurrentBlock();

    // 获取所有可选择的配置项key
    const getAllSelectableKeys = () => {
      const keys: string[] = [];
      currentBlock?.itemsTypeList?.forEach((type: any) => {
        type.itemList?.forEach((item: any) => {
          keys.push(item.itemNo);
        });
      });
      return keys;
    };

    // 全选处理
    const handleSelectAll = () => {
      const allKeys = getAllSelectableKeys();
      updateCurrentTabState({
        selectedKeys: allKeys,
      });
    };

    // 清空处理
    const handleClear = () => {
      updateCurrentTabState({
        selectedKeys: [],
      });
    };

    return (
      <div className="module-section">
        <div className="section-header">
          <span>请选择结构化配置项</span>
          <div className="header-actions">
            <Button type="link" size="small" onClick={handleSelectAll}>
              全选
            </Button>
            <Button type="link" size="small" onClick={handleClear}>
              清空
            </Button>
          </div>
        </div>
        <Tree
          treeData={buildTreeData(getCurrentBlock()?.itemsTypeList || [])}
          checkedKeys={selectedKeys}
          defaultExpandAll={true} // 默认展开所有节点
          checkable={true} // 显示复选框
          selectable={false} // 禁止点击选择，只能通过 checkbox 选择
          onCheck={(checked) => {
            // checked 可能是字符串数组或 {checked: string[], halfChecked: string[]}
            const checkedKeys = Array.isArray(checked)
              ? checked
              : checked.checked;
            updateCurrentTabState({
              selectedKeys: checkedKeys,
            });
          }}
        />
      </div>
    );
  };

  // 渲染文件选择部分
  const renderFileSection = () => {
    const { selectedFileRows, searchText, filteredFileList } =
      getCurrentTabState();
    return (
      <div className="file-section">
        <div className="section-header">请选择配置文件</div>
        <div className="search-bar">
          <Input.Search
            placeholder="请输入配置文件名或描述进行搜索"
            enterButton="查询"
            value={searchText}
            allowClear
            onChange={(e) =>
              updateCurrentTabState({
                searchText: e.target.value,
                // 当清空搜索框时，恢复显示所有文件
                filteredFileList: e.target.value
                  ? filteredFileList
                  : getCurrentBlock()?.fileList,
              })
            }
            onSearch={handleSearch}
          />
        </div>
        <div className="table-info">
          <span>已选择 {selectedFileRows.length} 项</span>
        </div>
        <Table
          rowSelection={{
            ...rowSelection,
            // 保持所有选中项，即使当前页面看不到
            preserveSelectedRowKeys: true,
          }}
          columns={columns}
          dataSource={filteredFileList}
          rowKey="fileNo"
          pagination={false}
          loading={loading}
        />
      </div>
    );
  };
  // 渲染页面 // 渲染主内容
  const renderContent = () => {
    return (
      <div className="content-container">
        {renderModuleSection()}
        {renderFileSection()}
      </div>
    );
  };

  return (
    <div className="configuration-page">
      <div className="search-item">
        <span className="label">产品</span>
        <Select
          options={productOptions}
          placeholder="请选择产品"
          allowClear
          labelInValue
          style={{ width: 200 }}
          onChange={handleProductChange}
        />
      </div>

      {configData.length > 0 && (
        <>
          <Tabs
            activeKey={activeBlockNo}
            onChange={(key) => setActiveBlockNo(key)}
            items={configData.map((block) => ({
              key: block.blockNo,
              label: block.blockName,
            }))}
          />
          {renderContent()}
        </>
      )}
    </div>
  );
};

export default Configuration;
