import { Col, Form, Input, Radio, Row, Select, Flex, message } from 'antd';
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from 'react';
import { DataType } from '../../utils/constant';
import { DataTypeOptions, rwFlagOptions } from '../../utils/column';
import NumberModule from './NumberModule';
import EnumModule from './EnumModule';
import BoolModule from './BoolModule';
import { cloneDeep } from 'lodash';
import './index.scss';
import showModal from '@/components/commonModal';
import { sendGlobalEvent } from '@/utils/emit';
import ArrayModule from './ArrayModule';

let propertiesData: any = null;
let instanceList: any = null;
let curEditLevel: any = null;

const StructModule = React.memo(
  ({
    level,
    disabled,
    parentKey,
    initChildKeyList,
    changeParentStatus,
  }: {
    level: number;
    disabled: boolean;
    parentKey: any;
    initChildKeyList: any[];
    changeParentStatus: AnyFunc;
  }) => {
    const [curEditKey, setCurEditKey] = useState<any>(null);
    const formInstance = instanceList[level][0];
    const parentInstance = instanceList[level - 1][0];
    const [parent, setParent] = useState<any>({});
    useEffect(() => {
      setParent({
        ...propertiesData[level - 1][parentKey],
        dataSpecsList: initChildKeyList,
      });
      parentInstance.setFieldsValue({ dataSpecsList: initChildKeyList });
    }, [parentKey]);

    const addJSONProperty = () => {
      formInstance.resetFields();
      curEditLevel = level;
      const key = String(Date.now());
      propertiesData[level] = {
        ...propertiesData[level],
        [key]: {},
      };
      setCurEditKey(key);
      changeParentStatus(false);
    };
    const handleStage = () => {
      if (level !== curEditLevel) {
        message.error('请先保存里面的层级');
        return;
      }
      formInstance
        .validateFields()
        .then((res: any) => {
          // 对当前项的处理：
          if (!propertiesData[level]) {
            propertiesData[level] = {};
          }
          let curInfo = cloneDeep(propertiesData[level][curEditKey]);
          if (
            res.dataType === DataType.ENUM ||
            res.dataType === DataType.INT_ENUM
          ) {
            if (!res.enumList) {
              message.error('枚举参数为空');
              sendGlobalEvent('checkRes', false);
              throw new Error('枚举参数为空');
            } else {
              propertiesData[level][curEditKey] = res;
              sendGlobalEvent('checkRes', true);
            }
          } else if (
            (res.dataType === DataType.ARRAY &&
              res.childDataType === DataType.STRUCT) ||
            res.dataType === DataType.STRUCT
          ) {
            //  当前项是结构体时，保存当前项，只保存没有被打删除标记的数据，斩断父与子的关联，来达到删除
            if (level === 5) {
              propertiesData[level][curEditKey] = curInfo
                ? {
                    ...curInfo,
                    ...res,
                  }
                : res;
            }
            if (
              level < 5 &&
              curInfo?.dataSpecsList?.length > 0 &&
              curInfo?.dataSpecsList.filter((v: any) => v.type === 'del')
                .length !== curInfo.dataSpecsList?.length
            ) {
              const childList: any[] = [];
              curInfo.dataSpecsList.forEach((v: any) => {
                if (!v.type) {
                  childList.push(v);
                } else if (v.type === 'add') {
                  childList.push(v.key);
                }
              });
              propertiesData[level][curEditKey] = {
                ...curInfo,
                ...res,
                dataSpecsList: childList,
              };
            }
          } else {
            // 当前项不是特殊项
            propertiesData[level][curEditKey] = curInfo
              ? {
                  ...curInfo,
                  ...res,
                }
              : res;
          }

          // 对父级的处理： 该项是新增时，父级没有该项，将该项与父级关联上
          const hasCurChild = parent.dataSpecsList?.includes(curEditKey);
          if (!hasCurChild) {
            const childKeyList = parent.dataSpecsList
              ? parent.dataSpecsList.concat([
                  level === 1 ? curEditKey : { key: curEditKey, type: 'add' },
                ])
              : [level === 1 ? curEditKey : { key: curEditKey, type: 'add' }];

            propertiesData[level - 1][parentKey] = {
              ...parent,
              dataSpecsList: childKeyList,
            };
            instanceList[level - 1][0].setFieldsValue({
              dataSpecsList: childKeyList,
            });
          }

          setParent(propertiesData[level - 1][parentKey]);
          setCurEditKey(null);
          curEditLevel = curEditLevel - 1;
          changeParentStatus(true);
        })
        .catch((err: any) => {
          console.log(err);
        });
    };
    const handleCheckBack = () => {
      curEditLevel = curEditLevel - 1;
      setCurEditKey(null);
      changeParentStatus(true);
    };
    const handleBack = () => {
      if (disabled) {
        handleCheckBack();
        return;
      }
      if (level !== curEditLevel) {
        message.error('请先取消里面的层级');
        return;
      }
      showModal({
        title: '',
        width: '600px',
        content:
          '此操作会恢复本层级的上一次暂存状态（包括其子状态层级），是否确认操作？',
        footer: [
          {
            text: '取消',
            type: 'cancelBtn',
          },
          {
            text: '确定',
            type: 'notCancelBtn',
            needValidate: true,
            onClick: (cb: any) => {
              const hasCurChild =
                parent.dataSpecsList?.filter(
                  (v: any) => v === curEditKey || v.key === curEditKey,
                ).length > 0;
              const curInfo = propertiesData[level][curEditKey];
              if (!hasCurChild) {
                // 父级不包含当前删除的项
                delete propertiesData[level][curEditKey];
              }
              if (
                (curInfo.dataType === DataType.ARRAY &&
                  curInfo.childDataType === DataType.STRUCT) ||
                curInfo.dataType === DataType.STRUCT
              ) {
                const childList: any[] = [];
                curInfo.dataSpecsList?.forEach((v: any) => {
                  if (!v.type) {
                    childList.push(v);
                  } else if (v.type === 'del') {
                    childList.push(v.key);
                  }
                });
                propertiesData[level][curEditKey] = {
                  ...curInfo,
                  dataSpecsList: childList,
                };
              }
              curEditLevel = curEditLevel - 1;
              setCurEditKey(null);
              changeParentStatus(true);
              cb();
            },
          },
        ],
      });
    };

    const handleDel = (key: any, index: number) => {
      if (curEditKey && curEditKey !== key) {
        message.info('请先编辑完里面的层级');
        return;
      }
      const p = cloneDeep(parent);
      propertiesData[level - 1][parentKey] = {
        ...p,
        dataSpecsList: p.dataSpecsList.map((v: any) => {
          if (v === key || v.key === key) {
            return {
              key: key,
              type: 'del',
            };
          } else {
            return v;
          }
        }),
      };
      setParent(propertiesData[level - 1][parentKey]);
    };

    const handleEdit = (key: any) => {
      if (curEditKey && curEditKey !== key) {
        message.info('请先保存里面的层级');
        return;
      }
      const val = propertiesData[level][key];
      curEditLevel = level;
      formInstance.setFieldsValue(val);
      setCurEditKey(key);
      changeParentStatus(false);
    };

    return (
      <div className="struct-module">
        <div className="number">{level}</div>
        <div className="content">
          <Form.Item
            label="JSON对象"
            name="dataSpecsList"
            labelCol={{ span: 3 }}
            rules={[{ required: true, message: '结构体参数不能为空' }]}
          ></Form.Item>

          <div className="added">
            {parent.dataSpecsList &&
              parent.dataSpecsList.map((key: any, index: number) => {
                const _key = key.key ?? key;
                const info = propertiesData[level][_key];
                if (
                  info &&
                  ((!key.type && key !== curEditKey) ||
                    (key.type === 'add' && key.key !== curEditKey))
                ) {
                  return (
                    <div key={key} className="property">
                      <div className="name">参数名称：{info.name}</div>
                      <div className="btns">
                        <span
                          onClick={() => {
                            handleEdit(_key);
                          }}
                        >
                          {disabled ? '查看' : '编辑'}
                        </span>

                        {!disabled && (
                          <span
                            onClick={() => {
                              handleDel(_key, index);
                            }}
                          >
                            删除
                          </span>
                        )}
                      </div>
                    </div>
                  );
                } else {
                  return <></>;
                }
              })}
          </div>
          {!curEditKey && !disabled && (
            <div className="JSON-add-btn">
              <span onClick={addJSONProperty}>+新增参数</span>
            </div>
          )}
          {curEditKey && (
            <RenderFunctionInfo
              key={curEditKey}
              level={level}
              disabled={disabled}
              curKey={curEditKey}
            />
          )}

          {curEditKey && (
            <div className="save-btns">
              {!disabled && <span onClick={handleStage}>暂存</span>}
              <span onClick={handleBack}>取消</span>
            </div>
          )}
        </div>
      </div>
    );
  },
);
const RenderDataInfo = React.memo(
  ({
    type,
    level,
    curKey,
    editable,
    disabled,
    changeParentStatus,
  }: {
    type: any;
    level: number;
    curKey: any;
    editable: boolean;
    disabled: boolean;
    changeParentStatus: AnyFunc;
  }) => {
    const instance = instanceList[level][0];
    switch (type) {
      case DataType.INT:
        return <NumberModule disabled={disabled} dataType={type} />;
      case DataType.DOUBLE:
        return <NumberModule disabled={disabled} dataType={type} />;
      case DataType.LONG:
        return <NumberModule disabled={disabled} dataType={type} />;
      case DataType.DATE:
        return (
          <Form.Item label="时间格式">
            <Input value="String类型的UTC时间戳（毫秒）" disabled />
          </Form.Item>
        );
      case DataType.INT_ENUM:
        const intEnumData = cloneDeep(propertiesData[level][curKey]);
        return (
          <>
            <EnumModule
              key={Date.now()}
              formInstance={instance}
              initEnumData={intEnumData}
              disabled={disabled}
              type="number"
            />
          </>
        );
      case DataType.ENUM:
        const enumData = cloneDeep(propertiesData[level][curKey]);
        return (
          <EnumModule
            key={Date.now()}
            initEnumData={enumData}
            formInstance={instance}
            disabled={disabled}
            type="string"
          />
        );
      case DataType.BOOL:
        return <BoolModule disabled={disabled} />;
      case DataType.TEXT:
        return (
          <Form.Item
            label="数据长度"
            name="length"
            rules={[
              { required: true, message: '数据长度不能为空' },
              {
                pattern: /^(?:10240|[1-9]\d{0,3}|[1-9])$/g,
                message: '字符串长度应为1-10240',
              },
            ]}
          >
            <Input
              placeholder="请输入数据长度"
              allowClear
              suffix="字节"
              disabled={disabled}
            />
          </Form.Item>
        );
      case DataType.ARRAY:
        const initInfo = propertiesData[level][curKey];
        const [childDataType, setChildDataType] = useState(
          initInfo.childDataType,
        );
        const initArrStructChildKeyList =
          initInfo.dataType === DataType.ARRAY ? initInfo.dataSpecsList : [];
        return (
          <>
            <ArrayModule
              disabled={disabled ? disabled : !editable}
              initInfo={initInfo}
              formInstance={instance}
              changeChildDataType={(v: DataType) => setChildDataType(v)}
            />
            {childDataType === DataType.STRUCT && (
              <StructModule
                level={level + 1}
                disabled={disabled}
                parentKey={curKey}
                initChildKeyList={initArrStructChildKeyList}
                changeParentStatus={changeParentStatus}
              />
            )}
          </>
        );
      case DataType.STRUCT:
        let initData = cloneDeep(propertiesData[level][curKey]);
        if (initData.dataType === DataType.ARRAY) {
          delete initData.childDataType;
        }
        const initStructChildKeyList =
          initData.dataType === DataType.STRUCT ? initData.dataSpecsList : [];
        if (level + 1 < 6) {
          return (
            <StructModule
              level={level + 1}
              disabled={disabled}
              parentKey={curKey}
              initChildKeyList={initStructChildKeyList}
              changeParentStatus={changeParentStatus}
            />
          );
        } else {
          return (
            <Form.Item
              label="JSON对象"
              name="JSONObject"
              rules={[
                { required: true, message: '请输入文本' },
                {
                  validator: (_, value) => {
                    if (value[0] !== '[' || value[value.length - 1] !== ']') {
                      return Promise.reject(new Error('JSON文本格式不对'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input.TextArea
                placeholder="请输入文本"
                showCount
                disabled={disabled}
                style={{ height: 120, resize: 'none' }}
              />
            </Form.Item>
          );
        }
    }
  },
);

const RenderFunctionInfo = React.memo(
  ({
    level, // 当前项层级
    disabled,
    curKey, // 当前编辑项的key
  }: {
    level: number;
    disabled: boolean;
    curKey: string;
  }) => {
    const instance = instanceList[level][0];
    const dataType = Form.useWatch('dataType', instance);
    const [editable, setEditable] = useState<boolean>(curEditLevel === level);

    const changeParentStatus = (v: boolean) => {
      setEditable(v);
    };

    return (
      <div>
        <Form
          key={level}
          form={instance}
          labelCol={{ span: 2 }}
          wrapperCol={{ span: 8 }}
        >
          <Form.Item
            label={level > 0 ? '参数名称' : '功能名称'}
            name="name"
            rules={[
              { required: true, message: '该名称不能为空' },
              {
                pattern:
                  /^[\u4e00-\u9fa5a-zA-Z0-9][\u4e00-\u9fa5a-zA-Z0-9\-_\/.]*$/g,
                message:
                  '支持中文、大小写字母、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头，不超过 30 个字符',
              },
            ]}
          >
            <Input
              disabled={disabled ? disabled : !editable}
              maxLength={30}
              placeholder={
                level > 0 ? '请输入您的参数名称' : '请输入您的功能名称'
              }
            />
          </Form.Item>
          <Form.Item
            label="标识符"
            name="identifier"
            validateDebounce={100}
            rules={[
              { required: true, message: '标识符不能为空' },
              {
                pattern: /^[a-zA-Z0-9_]+$/g,
                message: '支持大小写字母、数字和下划线、不超过 50 个字符。',
              },
            ]}
          >
            <Input
              disabled={disabled ? disabled : !editable}
              maxLength={50}
              placeholder="请输入您的标识符"
            />
          </Form.Item>
          <Form.Item
            label="数据类型"
            name="dataType"
            rules={[{ required: true, message: '数据类型不能为空' }]}
          >
            <Select
              disabled={disabled ? disabled : !editable}
              options={
                level < 5
                  ? DataTypeOptions
                  : DataTypeOptions.filter(
                      (v: any) => v.value !== DataType.ARRAY,
                    )
              }
              placeholder="请选择数据类型"
            />
          </Form.Item>
          {dataType && (
            <RenderDataInfo
              disabled={disabled}
              editable={editable}
              type={dataType}
              level={level}
              curKey={curKey}
              changeParentStatus={changeParentStatus}
            />
          )}
        </Form>
      </div>
    );
  },
);

const Properties = forwardRef(
  (
    {
      type,
      ThingModel,
      basicInfo,
    }: {
      type: string;
      ThingModel: any;
      basicInfo: any;
    },
    ref,
  ) => {
    if (type === 'edit' || type === 'check') {
      propertiesData = cloneDeep(ThingModel);
    } else if (type === 'add') {
      propertiesData = [{ [String(Date.now())]: {} }];
    }
    instanceList = [];
    curEditLevel = 0;
    for (let i = 0; i < 6; i++) {
      const instance = Form.useForm();
      instanceList[i] = instance;
    }
    useImperativeHandle(
      ref,
      () => {
        return {
          submitCheck,
          clearData,
        };
      },
      [],
    );
    const [basicInfoRef] = Form.useForm();
    const firstFormInstance = instanceList[0][0];
    useEffect(() => {
      const firstProperty =
        propertiesData[0] && Object.values(propertiesData[0])[0];

      if (propertiesData?.length > 0) {
        firstFormInstance.setFieldsValue(firstProperty);
      }
      basicInfoRef.setFieldsValue(basicInfo ?? { rwFlag: 'READ_WRITE' });
    }, [propertiesData]);

    const submitCheck = async () => {
      const otherInfo = await firstFormInstance.validateFields();
      const basicInfo = await basicInfoRef.validateFields();
      const firstKey = Object.keys(propertiesData[0])[0];
      propertiesData[0] = {
        [firstKey]: {
          ...propertiesData[0][firstKey],
          ...otherInfo,
        },
      };
      return { basicInfo, propertiesData };
    };

    const clearData = () => {
      propertiesData = null;
      instanceList = null;
      curEditLevel = null;
    };

    return (
      <div className="add-edit-properties">
        <RenderFunctionInfo
          level={0}
          disabled={type === 'check'}
          curKey={Object.keys(propertiesData[0])[0]}
        />
        <Form
          labelCol={{ span: 2 }}
          wrapperCol={{ span: 8 }}
          form={basicInfoRef}
        >
          <Form.Item
            label="读写类型"
            name="rwFlag"
            rules={[{ required: true, message: '标识符不能为空' }]}
          >
            <Radio.Group options={rwFlagOptions} disabled={type === 'check'} />
          </Form.Item>
          <Form.Item label="描述" name="description">
            <Input.TextArea
              placeholder="请输入描述"
              showCount
              maxLength={100}
              disabled={type === 'check'}
              style={{ height: 120, resize: 'none' }}
            />
          </Form.Item>
        </Form>
      </div>
    );
  },
);

export default React.memo(Properties);
