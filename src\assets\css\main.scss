$primary-color: #31C2A6; // 全局主题颜色
$primary-dark-color: #287469; // 全局主题颜色(深色)
$text-red-color: #D9001B;
$text-normal-color: #333; // 普通字体颜色
$text-message-color: #808080; // 提示信息颜色
$color-blue: #1890ff;
$text-font-bold: 700; // 字体加粗
// ota
body,
html {
  font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
  background-color: #f0f2f5;  

  .status-use {
    color: $primary-color;
  }

  .status-unuse {
    color: $text-red-color;
  }
}
ul,
ol,
li {
  list-style: none;
}

/*弹性盒布局*/
/*让子元素 水平排列(row)  垂直排列(column) */
.box {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  -o-flex-direction: row;
  flex-direction: row;
}

.box_vertical {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  -o-flex-direction: column;
  flex-direction: column;
}

/*弹性盒子内 子元素超出父元素宽或高度
是否溢出(折行 wrap) 默认值 nowrap 不换行，
即当主轴尺寸固定时，当空间不足时，项目尺寸会随之调整而并不会挤到下一行。
*/
.wrap {
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
}

/* 子元素从左到右排列(flex-start)  从右到左(flex-end)*/
.box_pack_start {
  justify-content: flex-start;
}

.flex {
  display: flex;
}

/* 设置占父元素的一份 */
.box_flex {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

/* 子元素上下居中 */
.flex_orient_center {
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  -o-align-items: center;
  align-items: center;
}

/* 子元素靠x轴右对齐*/
.flex_orient_right {
  -webkit-align-items: flex-end;
  -moz-align-items: flex-end;
  -ms-align-items: flex-end;
  -o-align-items: flex-end;
  align-items: flex-end;
}

/* 子元素靠y轴右对齐 */
.flex_pack_right {
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  -ms-justify-content: flex-end;
  -o-justify-content: flex-end;
  justify-content: flex-end;
}

/* 子元素左右居中 */
.flex_pack_center {
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  -o-justify-content: center;
  justify-content: center;
}

/* 子元素两端对齐 */
.flex_pack_justify {
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

/* 子元素平分 */
.flex_pack_around {
  -webkit-justify-content: space-around;
  justify-content: space-around;
}

/*文本溢出省略*/
.text_overflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.light-font-color {
  color: $text-normal-color;
}

.t-a-r {
  text-align: right;
}

.p-r-10 {
  padding-right: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-15 {
  margin-top: 15px;
}

.m-t-20 {
  margin-top: 20px;
}