import React, {
  useState,
  useRef,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { CommonForm, CommonTable } from '@jd/x-coreui';
import { Tabs, Button, message, Card, Form, Input } from 'antd';
import type { FormInstance } from 'antd';

import { HttpStatusCode } from '@/fetch/core/constant';
import { ConfigTemplateApi, Device } from '@/fetch/bussiness';
import { SelectedData } from '../Configuration';
import './index.scss';
import showModal from '@/components/commonModal';
import TextArea from 'antd/es/input/TextArea';
import StructuredConfigEditor from '@/components/StructuredConfigEditor';

interface EditConfigurationProps {
  selectedConfig: SelectedData;
}

interface FileItem {
  fileNo: string;
  fileName: string;
  content: string;
  blockNo: string;
  blockName: string;
}

// 基本信息表单配置
const BasicInfoFormConfig = {
  fields: [
    {
      fieldName: 'templateName',
      label: '模板名称',
      type: 'input',
      placeholder: '请输入模板名称',
      validatorRules: [
        { required: true, message: '请输入模板名称' },
        { max: 50, message: '模板名称不能超过50个字符' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
          message: '只支持中文、字母、数字、中划线、下划线',
        },
      ],
      maxLength: 50,
      showCount: true,
    },
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      disabled: true,
      labelInValue: false,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      placeholder: '请选择型号',
      multiple: true,
      labelInValue: false,
      validatorRules: [{ required: true, message: '请选择型号' }],
    },
    {
      fieldName: 'remark',
      label: '备注',
      type: 'textArea',
      placeholder: '请输入备注',
      maxLength: 150,
      showCount: true,
      autoSize: { minRows: 3, maxRows: 5 },
      validatorRules: [{ max: 150, message: '备注不能超过150个字符' }],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) return [];
          const deviceApi = new Device();
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          }
          return [];
        },
      },
    ],
  },
};

// 编辑配置文件弹窗
const EditConfigFileModal = forwardRef<FormInstance, { data: any }>(
  ({ data }, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => form);

    return (
      <Form
        form={form}
        layout="vertical"
        initialValues={data}
        className="edit-config-file-form"
      >
        <Form.Item label="配置文件编号" name="fileNo">
          <Input disabled />
        </Form.Item>
        <Form.Item label="配置文件名称" name="fileName">
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="配置内容"
          name="content"
          rules={[{ required: true, message: '请输入配置内容' }]}
        >
          <TextArea
            autoSize={{ minRows: 8, maxRows: 15 }}
            placeholder="请输入配置内容"
          />
        </Form.Item>
      </Form>
    );
  },
);

const EditConfiguration = forwardRef<
  {
    getTemplateData: () => Promise<any>;
  },
  EditConfigurationProps
>(({ selectedConfig }, ref) => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const formRef = useRef<any>(null);

  const [configData, setConfigData] = useState<any[]>([]);
  const [activeBlockNo, setActiveBlockNo] = useState<string>();

  // 存储配置数据
  const configDataRef = useRef<Record<string, any>>({});
  const fileDataRef = useRef<Record<string, any>>({});

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getTemplateData: async () => {
      try {
        // 验证基本信息表单
        const formValues = await formRef.current?.validateFields();

        // 构造API参数
        const templateData = {
          ...formValues,
          blockConfigList: configData.map((block) => ({
            blockNo: block.blockNo,
            itemList: Object.keys(
              configDataRef.current[block.blockNo] || {},
            ).map((itemNo) => ({
              itemNo,
              content: JSON.stringify(
                configDataRef.current[block.blockNo][itemNo] || {},
              ),
            })),
            fileList: Object.keys(fileDataRef.current[block.blockNo] || {}).map(
              (fileNo) => ({
                fileNo,
                content: fileDataRef.current[block.blockNo][fileNo] || '{}',
              }),
            ),
          })),
        };

        return templateData;
      } catch (error) {
        throw new Error('请完善必填信息');
      }
    },
  }));

  // 当选中数据变化时，获取配置数据
  useEffect(() => {
    if (selectedConfig?.product?.value) {
      fetchConfigData();
    }
  }, [selectedConfig]);

  const fetchConfigData = async () => {
    try {
      const res = await configTemplateApi.current.getEnableConfigDetailList({
        productKey: selectedConfig.product.value,
        blockConfigList: selectedConfig.blockData.map((v: any) => ({
          blockNo: v.blockNo,
          itemNoList: v.selectedItems,
          fileNoList: v.selectedFiles,
        })),
      });

      if (res.code === HttpStatusCode.Success) {
        setConfigData(res.data || []);
        if (res.data && res.data.length > 0) {
          setActiveBlockNo(res.data[0].blockNo);

          // 初始化数据存储
          const initialConfigData: Record<string, any> = {};
          const initialFileData: Record<string, any> = {};

          res.data.forEach((block: any) => {
            initialConfigData[block.blockNo] = {};
            initialFileData[block.blockNo] = {};

            // 初始化文件数据
            if (block.fileList) {
              block.fileList.forEach((file: any) => {
                initialFileData[block.blockNo][file.fileNo] =
                  file.content || '{}';
              });
            }
          });

          configDataRef.current = initialConfigData;
          fileDataRef.current = initialFileData;
        }
      } else {
        message.error(res.message || '获取配置详情失败');
      }
    } catch (error) {
      message.error('获取配置数据失败');
    }
  };

  // 处理配置项数据变化
  const handleConfigDataChange = (
    blockNo: string,
    data: Record<string, any>,
  ) => {
    configDataRef.current = {
      ...configDataRef.current,
      [blockNo]: data,
    };
  };

  // 编辑配置文件
  const handleEditFile = async (file: FileItem) => {
    const formRef = useRef<FormInstance>(null);

    showModal({
      title: '编辑配置文件',
      content: <EditConfigFileModal ref={formRef} data={file} />,
      width: '800px',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: () => void) => {
            cb();
          },
        },
        {
          text: '保存',
          type: 'notCancelBtn',
          onClick: async (cb: () => void) => {
            try {
              const values = await formRef.current?.validateFields();
              if (values) {
                // 更新文件内容
                fileDataRef.current = {
                  ...fileDataRef.current,
                  [file.blockNo]: {
                    ...fileDataRef.current[file.blockNo],
                    [file.fileNo]: values.content,
                  },
                };
                message.success('保存成功');
                cb();
              }
            } catch (error) {
              message.error('请完善配置内容');
            }
          },
        },
      ],
    });
  };

  // 配置文件表格列定义
  const fileTableColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '模块',
      dataIndex: 'blockName',
      align: 'center',
    },
    {
      title: '配置文件名称',
      dataIndex: 'fileName',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      align: 'center',
      render: (_: any, record: FileItem) => (
        <Button type="link" onClick={() => handleEditFile(record)}>
          编辑
        </Button>
      ),
    },
  ];

  return (
    <div className="edit-configuration">
      <Card title="基本信息" className="basic-info-card">
        <CommonForm
          formConfig={BasicInfoFormConfig}
          onRef={(ref) => (formRef.current = ref)}
          formType="edit"
          layout="vertical"
          defaultValue={{
            productKey: selectedConfig?.product?.value,
            fetchProductKey: true,
          }}
        />
      </Card>

      <Card title="配置编辑" className="config-edit-card">
        {configData.length > 0 ? (
          <Tabs
            activeKey={activeBlockNo}
            onChange={setActiveBlockNo}
            items={configData.map((block) => ({
              key: block.blockNo,
              label: block.blockName,
              children: (
                <div className="block-content">
                  {/* 结构化配置项 */}
                  {block.itemsTypeList && block.itemsTypeList.length > 0 && (
                    <div className="structured-config-section">
                      <h4>结构化配置项</h4>
                      <StructuredConfigEditor
                        configTypes={block.itemsTypeList}
                        blockNo={block.blockNo}
                        onDataChange={handleConfigDataChange}
                      />
                    </div>
                  )}

                  {/* 配置文件 */}
                  {block.fileList && block.fileList.length > 0 && (
                    <div className="config-file-section">
                      <h4>配置文件</h4>
                      <CommonTable
                        tableListData={{
                          list: block.fileList || [],
                          totalNumber: block.fileList?.length || 0,
                          totalPage: 1,
                        }}
                        columns={fileTableColumns}
                        pagination={false}
                        size="small"
                      />
                    </div>
                  )}

                  {/* 如果没有配置项和文件 */}
                  {(!block.itemsTypeList || block.itemsTypeList.length === 0) &&
                    (!block.fileList || block.fileList.length === 0) && (
                      <div className="empty-content">
                        该模块下暂无选中的配置项和配置文件
                      </div>
                    )}
                </div>
              ),
            }))}
          />
        ) : (
          <div className="no-data">暂无配置数据</div>
        )}
      </Card>
    </div>
  );
});

EditConfiguration.displayName = 'EditConfiguration';

export default EditConfiguration;
