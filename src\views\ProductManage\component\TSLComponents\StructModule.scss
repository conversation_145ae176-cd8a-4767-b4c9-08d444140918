.struct-module {
  margin-left: 20px;
  width: calc(100% - 20px);
  display: flex;
  .number {
    color: #0070cc;
  }
  .content {
    width: 100%;
    border: 1px solid #0070cc;
    border-radius: 10px;
    padding: 10px;
    margin-left: 10px;
    &.no-border {
      border: none;
    }
    .JSON-object {
      span:first-child {
        color: red;
      }
    }
    .added {
      .property {
        background: #e3f2fd;
        display: flex;
        justify-content: space-between;
        padding: 8px;
        margin-bottom: 8px;
        width: 430px;
        font-size: 12px;
        color: #555;
        .btns {
          span:first-child {
            display: inline-block;
            border-right: 1px solid #d8d8d8;
          }
          span {
            cursor: pointer;
            margin-right: 8px;
            color: #0070cc;
            padding-right: 8px;
            line-height: 12px;
          }
        }
      }
    }
    .JSON-add-btn {
      color: blue;
      cursor: pointer;
    }

    .save-btns {
      position: relative;
      left: 80%;
      span {
        cursor: pointer;
        display: inline-block;
        text-align: center;
        border-radius: 0;
        padding: 0 16px;
        height: 32px;
        width: 58px;
        line-height: 30px;
        font-size: 12px;
        border-width: 1px;
        background-color: #fafafa;
        border: 1px solid #dedede;
        color: #333;
        &:first-child {
          background-color: #0070cc;
          border-color: transparent;
          color: #fff;
          margin-right: 15px;
        }
      }
    }
  }
}
