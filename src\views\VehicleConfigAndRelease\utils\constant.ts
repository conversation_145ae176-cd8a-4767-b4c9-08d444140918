export enum TabType {
  HISTORY_RECORD = '0',
  CURRENT_RECORD = '1',
}
export const tabMenu = [
  {
    key: TabType.CURRENT_RECORD,
    name: '现有配置记录',
  },
  {
    key: TabType.HISTORY_RECORD,
    name: '历史删除记录',
  }
]

export enum PublishTabType {
  CURRENT_VERSION = '1',
  HISTORY_VERSION = '0',
}
export const publishTabMenu = [
  {
    key: PublishTabType.CURRENT_VERSION,
    name: '当前版本',
  },
  {
    key: PublishTabType.HISTORY_VERSION,
    name: '历史发布记录',
  }
]
// 配置发布状态
export enum VehicleConfIssueStatus {
  VEHICLE_TYPE_NOT_CONFIGURE = -2,  // 车型未配置
  VEHICLE_TYPE_CONFIGURE = -1,    // 车型已配置
  NOT_ISSUE = 0,    // 整车已配置,未发布
  TO_ISSUE = 1,     // 整车已配置,待生效
  ISSUED = 2,  // 整车已配置,已生效
  CANCEL = 3,    // 整车已配置,已取消
}
// 最近一次升级状态
export enum LatestIssueTaskResultStatus {
  TO_UPGRADE = 0,   // 未升级
  UPGRADED = 1,     // 已升级
}
// 模块（应用）
export enum application {
  ROVER = "rover",
  ANDROID = "android",
  VIDEO = "video",
  FIRMWARE = 'firmware',
  CONF = "rover-conf",
  OTHER = "other",
}
// 升级方式
export enum upgradeType {
  SILENT = 0,   // 静默升级
  FORCE = 1     // 强制升级
}
// 车端升级结果
export enum vehicleStatue {
  FAILURE = 0, //失败
  SUCCESS = 1  //成功
}