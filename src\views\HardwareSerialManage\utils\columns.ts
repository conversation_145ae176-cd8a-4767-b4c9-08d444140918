import {
  DropDownType,
  dropDownKey,
  dropDownList<PERSON>ey,
  ClstagKey,
} from '@/utils/searchFormEnum';
export const TableColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 60, },
  { title: '厂商id', width: 70, dataIndex: 'manufactoryId', align: 'center', ellipsis: true, },
  { title: '厂商名称', width: 150, dataIndex: 'manufactoryName', align: 'center', ellipsis: true, },
  { title: '硬件类型', width: 80, dataIndex: 'type', align: 'center', ellipsis: true, },
  { title: '模块编号', width: 100, dataIndex: 'modelNumber', align: 'center', ellipsis: true, },
  { title: '序列号', width: 100, dataIndex: 'serialNumber', align: 'center', ellipsis: true, },
  { title: '操作人', width: 120, dataIndex: 'modifyUser', align: 'center', ellipsis: true, },
  { title: '操作时间', width: 150, dataIndex: 'createTime', align: 'center', ellipsis: true, },
  { title: '操作', width: 60, dataIndex: 'operate', align: 'center', ellipsis: true, fixed: 'right'},
]

export const SearchConfig: any[] = [
  { name: 'serialNumber', title: '序列号', placeHolder: "请输入序列号", type: DropDownType.INPUT }
]

export const detailConfig: any[] = [
  { label: '厂商id', name: 'manufactoryId', },
  { label: '厂商名称', name: 'manufactoryName', },
  { label: '硬件类型', name: 'type', },
  { label: '模块编号', name: 'modelNumber', },
  { label: '序列号', name: 'serialNumber', },
  { label: '内参', name: 'parameter', },
]
