import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import CommonForm from '@/components/CommonForm';
import { SelectInstructionConfig } from '../../utils/column';
import { FormConfig } from '@/components/CommonForm/formConfig';
import { CommandControlFetch, Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { DataType } from '@/views/ProductManage/utils/constant';
import {
  Input,
  Row,
  Col,
  Form,
  Select,
  Button,
  DatePicker,
  InputNumber,
} from 'antd';
import { formatInputParams } from '../../utils/constant';
import AceEditor from 'react-ace';
import dayjs from 'dayjs';
import 'ace-builds/src-noconflict/mode-json';
import './index.scss';
import showModal from '@/components/commonModal';

const SelectInstruction = forwardRef((props, ref) => {
  const formRef = useRef<any>(null);
  const [inputParamsForm] = Form.useForm();
  const fetchApi = new CommandControlFetch();
  const deviceApi = new Device();
  const [formConfig, setFormConfig] = useState<FormConfig>(
    SelectInstructionConfig,
  );
  const dateVals = useRef<any>({});
  const [debugModel, setDebugModel] = useState<any>({ val: 0, disable: false });
  const [arrayRow, setArrayRow] = useState<any>({});
  const editInputParamRef = useRef<any>({});
  const [inputParams, setInputParams] = useState<any[]>([]);

  useImperativeHandle(
    ref,
    () => {
      return {
        checkData,
      };
    },
    [debugModel.val],
  );

  useEffect(() => {
    getProductionOptions();
  }, []);

  const checkData = async () => {
    let inputParams = null;
    const basicData = await formRef.current.validateFields();
    if (debugModel.val == 1) {
      inputParams = editInputParamRef.current;
    } else {
      const inputVal = await inputParamsForm.validateFields();
      inputParams = formatInputParams({ ...inputVal, ...dateVals.current });
    }
    return {
      ...basicData,
      commandArgs:
        debugModel.val == 1 ? inputParams : JSON.stringify(inputParams || {}),
    };
  };

  const getProductionOptions = async () => {
    const res = await deviceApi.queryProductList();
    let productOptions: any[] = [];
    if (res.code === HttpStatusCode.Success) {
      productOptions = res.data?.map((v: any) => ({
        label: v.productName,
        value: v.productKey,
      }));
      const val = {
        ...SelectInstructionConfig,
        fields: SelectInstructionConfig.fields.map((v) => {
          if (v.fieldName === 'productKey') {
            v = {
              ...v,
              options: productOptions,
            };
          }
          return v;
        }),
      };
      setFormConfig(val);
    }
  };

  const handleValChange = (vals: any, fieldName: string) => {
    if (fieldName === 'identifier') {
      editInputParamRef.current = {};
      setInputParams([]);
      setArrayRow({});
      if (vals.identifier) {
        getServerTSL(vals);
      }
    }
    if (fieldName === 'debugModel') {
      if (vals.debugModel === 1) {
        showModal({
          title: '确定使用研发调试模式？(非研发人员慎用)',
          width: '600px',
          content: '',
          footer: [
            {
              text: '取消',
              type: 'cancelBtn',
              onClick: (cb: any) => {
                setDebugModel({
                  ...debugModel,
                  disable: false,
                });
                formRef.current.setFieldsValue({
                  debugModel: 0,
                  debugModelDisable: false,
                });
                cb();
              },
            },
            {
              text: '确定',
              type: 'notCancelBtn',
              needValidate: true,
              onClick: async (cb: any) => {
                setDebugModel({
                  val: vals[fieldName],
                  disable: false,
                });
                cb();
              },
            },
          ],
        });
      } else {
        setDebugModel({
          val: vals[fieldName],
          disable: false,
        });
      }
    }
    if (fieldName === 'productKey' || fieldName === 'blockNo') {
      setInputParams([]);
    }
  };

  const getServerTSL = async (vals: any) => {
    const res = await fetchApi.getServerTSL({
      productKey: vals.productKey,
      blockNo: vals.blockNo,
      identifier: vals.identifier,
    });
    if (res.code === HttpStatusCode.Success) {
      setDebugModel({
        val: res.data.debugModel,
        disable: res.data.debugModel === 1,
      });

      const tsl = JSON.parse(res.data?.content || {});
      setInputParams(tsl.inputParams);
    }
  };

  const handleArrayAdd = (
    identifier: string,
    colPerRow: number,
    total: number,
  ) => {
    const cur = arrayRow[identifier] || 1;
    if (Math.ceil(total / colPerRow) === cur) {
      return;
    }
    setArrayRow({
      ...arrayRow,
      [identifier]: cur + 1,
    });
  };

  const calculateColPerRow = (
    total: number,
    colPerRow: number,
    rowNum: number,
  ) => {
    const sum = colPerRow * (rowNum - 1); // 已经展示出来的总数
    if (total - sum > colPerRow) {
      return colPerRow;
    }
    const remainder = (total - sum) % colPerRow;
    if (remainder === 0) {
      return colPerRow;
    }
    return remainder;
  };

  const renderBasicParams = (v: any, colon?: boolean) => {
    switch (v.dataType) {
      case DataType.TEXT:
        return (
          <Form.Item
            label={v.name}
            name={v.identifier}
            colon={colon != undefined ? colon : true}
          >
            <Input.TextArea
              showCount={v.dataSpecs.length ?? false}
              maxLength={v.dataSpecs.length ?? undefined}
              placeholder={v.placeholder || `请输入${v.name}`}
              style={{ height: 70 }}
            />
          </Form.Item>
        );
      case DataType.DOUBLE:
      case DataType.INT:
      case DataType.LONG:
        return (
          <Form.Item label={v.name} colon={colon != undefined ? colon : true}>
            <div className="number-container">
              <Form.Item
                name={v.identifier}
                label=""
                colon={false}
                rules={[
                  {
                    validator: (rule, value) => {
                      if (v.dataSpecs.min && value < v.dataSpecs.min) {
                        return Promise.reject(`不能小于${v.dataSpecs.min}`);
                      }
                      return Promise.resolve();
                    },
                  },
                  {
                    validator: (rule, value) => {
                      if (v.dataSpecs.max && value > v.dataSpecs.max) {
                        return Promise.reject(`不能大于${v.dataSpecs.max}`);
                      }
                      return Promise.resolve();
                    },
                  },
                  {
                    pattern: [DataType.INT, DataType.LONG].includes(v.dataType)
                      ? /^-?\d+$/g
                      : /.*/g,
                    message: '请输入整数',
                  },
                ]}
              >
                <InputNumber
                  addonAfter={v.dataSpecs.unitName}
                  controls={false}
                  placeholder={v.placeholder || `请输入${v.name}`}
                />
              </Form.Item>
              {v.dataSpecs.min != null &&
                v.dataSpecs.max != null &&
                v.dataSpecs.step != null && (
                  <div className="value-range">
                    (取值范围:
                    <span>
                      {v.dataSpecs.min != null && v.dataSpecs.max != null
                        ? `${v.dataSpecs.min}-${v.dataSpecs.max}`
                        : '-'}
                    </span>
                    <span>&nbsp;步长: {v.dataSpecs.step || '-'})</span>
                  </div>
                )}
            </div>
          </Form.Item>
        );
      case DataType.ENUM:
      case DataType.INT_ENUM:
      case DataType.BOOL:
        return (
          <Form.Item
            label={v.name}
            name={v.identifier}
            colon={colon != undefined ? colon : true}
          >
            <Select
              allowClear
              options={v.dataSpecsList.map((j: any) => ({
                label: j.name,
                value: j.value,
              }))}
              placeholder={`请选择${v.name}`}
            />
          </Form.Item>
        );
      case DataType.DATE:
        return (
          <Form.Item
            label={v.name}
            name={v.identifier}
            colon={colon != undefined ? colon : true}
          >
            <DatePicker
              showTime
              needConfirm={false}
              onChange={(value, dateString) => {
                dateVals.current[v.identifier] = dayjs(value).format(
                  'YYYY-MM-DD HH:mm:ss.SSS',
                );
              }}
            />
          </Form.Item>
        );
    }
  };

  const renderStructParams = (v: any) => {
    return (
      <Form.Item label={v.name}>
        <div className="struct-info">
          <div className="obj-title">结构体对象：</div>
          {v.dataSpecsList.map((j: any) => {
            const _j = {
              ...j,
              dataType: j.childDataType,
              identifier: `${DataType.STRUCT}:${v.identifier}-${j.identifier}`,
            };
            return renderBasicParams(_j, false);
          })}
        </div>
      </Form.Item>
    );
  };

  const renderArrayParams = (v: any) => {
    const colPerRow = v.dataSpecs.childDataType === DataType.STRUCT ? 2 : 10;
    return (
      <Form.Item label={v.name}>
        <div className="array-add-btn">
          <Button
            type="primary"
            onClick={() =>
              handleArrayAdd(v.identifier, colPerRow, v.dataSpecs.size)
            }
          >
            添加一行
          </Button>
          <span className="tip">该数组最多{v.dataSpecs.size}个元素</span>
        </div>
        {v.dataSpecs.childDataType === DataType.STRUCT
          ? renderArrayStruct({ ...v, ...v.dataSpecs }, colPerRow)
          : renderArrayBasic({ ...v, ...v.dataSpecs }, colPerRow)}
      </Form.Item>
    );
  };

  const renderArrayStruct = (v: any, colPerRow: number) => {
    const row = Array.from(
      { length: arrayRow[v.identifier] || 1 },
      (_, i) => i + 1,
    );
    return (
      <div className="array-struct">
        {row.map((r: number) => {
          const colNumber = calculateColPerRow(v.size, colPerRow, r);
          const col = Array.from({ length: colNumber }, (_, i) => i + 1);
          return (
            <div className="array-row" key={`array-${v.identifier}-row-${r}`}>
              {col.map((c: number) => {
                return (
                  <div
                    className="array-col"
                    key={`array-${v.identifier}-row-${r}-col-${c}`}
                  >
                    {renderStructParams({
                      dataType: 'STRUCT',
                      identifier: `${DataType.ARRAY}:${v.identifier}-${
                        (r - 1) * colPerRow + c
                      }`,
                      name: `元素${(r - 1) * colPerRow + c}`,
                      dataSpecsList: v.dataSpecsList,
                    })}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    );
  };

  const renderArrayBasic = (v: any, colPerRow: number) => {
    const row = Array.from(
      { length: arrayRow[v.identifier] || 1 },
      (_, i) => i + 1,
    );
    return (
      <div className="array-basic">
        {row.map((r: number) => {
          const colNumber = calculateColPerRow(v.size, colPerRow, r);
          const col = Array.from({ length: colNumber }, (_, i) => i + 1);
          return (
            <div className="array-row" key={`array-${v.identifier}-row-${r}`}>
              {col.map((c: number) => {
                return (
                  <div
                    className="array-col"
                    key={`array-${v.identifier}-row-${r}-col-${c}`}
                  >
                    {renderBasicParams({
                      identifier: `${DataType.ARRAY}:${v.identifier}-${
                        (r - 1) * colPerRow + c
                      }`,
                      placeholder: `请输入元素${(r - 1) * colPerRow + c}`,
                      name: `${(r - 1) * colPerRow + c}`,
                      dataType: v.childDataType,
                      dataSpecs: {},
                    })}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    );
  };

  const renderInputParams = (v: any) => {
    if (v.dataType === DataType.ARRAY) {
      return renderArrayParams(v);
    }
    if (v.dataType === DataType.STRUCT) {
      return renderStructParams(v);
    }
    return renderBasicParams(v);
  };

  const handleIEDInfoChange = (val: any) => {
    editInputParamRef.current = val;
  };

  return (
    <div className="select-instruction">
      <CommonForm
        name="select-instruction-form"
        formConfig={formConfig}
        layout="horizontal"
        defaultValue={{
          debugModel: debugModel.val,
          debugModelDisable: debugModel.disable,
        }}
        getFormInstance={(val: any) => (formRef.current = val)}
        onValueChange={handleValChange}
      />
      {debugModel.val === 0 ? (
        <Form
          form={inputParamsForm}
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 19 }}
        >
          <Row gutter={24} align="middle" style={{ width: '100%' }}>
            {inputParams.map((v) => {
              return <Col span={23}>{renderInputParams(v)}</Col>;
            })}
          </Row>
        </Form>
      ) : (
        <AceEditor
          key={new Date().getTime()}
          mode="json"
          value={JSON.stringify(editInputParamRef.current)}
          maxLines={18}
          minLines={18}
          setOptions={{
            useWorker: false,
            readOnly: false,
            wrap: true,
          }}
          editorProps={{ $blockScrolling: true }}
          style={{
            width: '600px',
            marginLeft: '180px',
            border: '1px solid rgba(0, 0, 0, 0.06)',
          }}
          onChange={handleIEDInfoChange}
        />
      )}
    </div>
  );
});

export default React.memo(SelectInstruction);
