import React, { useEffect, useRef, useState } from 'react';
import { Button, Modal, Table, Upload, message } from 'antd';
import { AppFormData, AppPackageForm, AppTableData } from './columns';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import showModal from '@/components/commonModal';
import CommonForm from '@/components/CommonForm';
import VersionTable from '../VersionTable';
import { FieldItem } from '@/components/CommonForm/formConfig';
import { ButtonType, CustomButton } from '@/components/CustomButton';
import { ProductType } from '@/utils/constant';
import { cloneDeep } from 'lodash';
import { parseResponseData } from '@/fetch/core/util';

const AppTable = () => {
  const appPackageForm = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableList, setTableList] = useState<any[]>([]);
  const [acceptType, setAcceptType] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<{
    current: number;
    pageSize: number;
  }>({
    current: 1,
    pageSize: 10,
  });
  const [pagination, setPagination] = useState<{
    total: number;
    totalPage: number;
  }>({
    total: 0,
    totalPage: 0,
  });
  useEffect(() => {
    fetchTable(currentPage);
  }, [currentPage]);

  const onDisabled = (record: any) => {
    Modal.confirm({
      content: (
        <p style={{ wordBreak: 'break-all' }}>
          {record.enable
            ? '确定要禁用此应用吗，禁用后此应用不能组成产品包；已生成的产品包不受影响'
            : '确定要启用此应用吗，启用后此应用可以组成产品包'}
        </p>
      ),
      onCancel: () => {},
      onOk() {
        request({
          method: 'POST',
          path: '/ota/web/change_app_status',
          body: {
            appName: record.appName,
            enable: record.enable == 0 ? 1 : 0,
          },
        })
          .then((res: any) => {
            fetchTable(currentPage);
          })
          .catch((err) => {
            console.log(err);
          });
      },
    });
  };

  const fetchTable = (opt: { current: number; pageSize: number }) => {
    request({
      path: '/ota/web/app_info_get_page_list',
      method: 'POST',
      urlParams: {
        pageNum: opt.current,
        pageSize: opt.pageSize,
      },
      body: {},
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          setTableList(res?.data?.list);
          setPagination({
            total: res?.data?.total,
            totalPage: res?.data?.pages,
          });
        }
      })
      .catch((e) => {});
  };

  const editAppInfo = async (data: any) => {
    return request({
      path: '/ota/web/edit_app_info',
      method: 'POST',
      body: data,
    });
  };

  const addApp = async (data: any) => {
    return request({
      path: '/ota/web/add_app',
      method: 'POST',
      body: data,
    });
  };

  const editAppItem = (record?: any) => {
    if (record?.enable === 0) {
      return;
    }
    let formInstance: any = null;
    const _formData = cloneDeep(AppFormData);
    const appName = _formData?.fields?.find(
      (field: FieldItem) => field.fieldName === 'appName',
    );
    if (record) {
      appName!.disabled = true;
    }
    showModal({
      type: 'confirm',
      title: '应用信息',
      content: (
        <CommonForm
          layout="vertical"
          formConfig={_formData}
          defaultValue={record}
          getFormInstance={(formRef: any) => {
            formInstance = formRef;
          }}
        />
      ),
      footer: [
        {
          type: 'notCancelBtn',
          text: '确定',
          needValidate: true,
          onClick: async (cb: AnyFunc) => {
            try {
              const fieldValues = await formInstance?.validateFields();
              if (record) {
                const res: any = await editAppInfo(fieldValues);
                if (res?.code === HttpStatusCode.Success) {
                  message.success('应用编辑成功');
                  fetchTable(currentPage);
                  cb();
                }
              } else {
                const res: any = await addApp(fieldValues);
                if (res?.code === HttpStatusCode.Success) {
                  message.success('应用新建成功');
                  fetchTable(currentPage);
                  cb();
                }
              }
            } catch (e: any) {}
          },
        },
      ],
    });
  };

  const getAppPackageTypeList = async () => {
    return request({
      path: '/ota/web/get_app_package_type_list',
      method: 'GET',
    });
  };
  const previewVersionList = (record: any) => {
    showModal({
      title: '版本信息',
      width: '1360px',
      content: (
        <VersionTable
          appName={record.appName}
          versionNumber={record.versionNumber}
          editPackage={uploadAppPackage}
        />
      ),
      footer: [],
    });
  };

  const getAppVersionInfo = async (record: any) => {
    return request({
      path: '/ota/web/get_app_version_info',
      method: 'POST',
      body: {
        appName: record.appName,
        versionNumber: record.versionNumber,
      },
    })
      .then((res: any) => {
        if (res.code === HttpStatusCode.Success) {
          return res.data;
        }
      })
      .catch((e) => {});
  };
  const customRequest = async (
    option: any,
    operateType?: 'add' | 'edit',
    showLoading?: AnyFunc,
  ) => {
    if (!option?.file && operateType === 'add') {
      message.warning('请上传文件');
      return;
    }
    const fileSize = option?.file?.file.size / 1024 / 1024 < 200;
    if (option?.file?.file && !fileSize) {
      message.warning('文件不能超过200MB');
      return false;
    }
    showLoading && showLoading();
    const initReq: RequestInit = {};
    initReq.method = 'POST';
    initReq.mode = 'cors';
    initReq.credentials = 'include';
    const formData = new FormData();
    option.file?.file && formData.append('file', option.file?.file);
    formData.append('appName', option.appName);
    option.applyVehicleBusinessType &&
      formData.append(
        'applyVehicleBusinessType',
        option.applyVehicleBusinessType,
      );
    formData.append('version', option.version);
    option.versionNumber &&
      formData.append('versionNumber', option.versionNumber);
    option.packageId && formData.append('packageId', option.packageId);
    formData.append(
      'packageType',
      option.packageType?.value || option.packageType || '',
    );
    formData.append('updateInfo', option.updateInfo || '');
    formData.append('defect', option.defect || '');
    formData.append('description', option.description || '');
    formData.append('extData', option.extData || '');
    initReq.body = formData;
    const url =
      operateType === 'add'
        ? '/ota/web/add_app_version'
        : '/ota/web/edit_app_version_info';
    const response = await window.fetch(
      `//${process.env.JDX_APP_FETCH_DOMAIN}${url}`,
      initReq,
    );
    const respBody: any = await parseResponseData(
      response,
      'application/x-www-form-urlencoded',
    );
    if (respBody) {
      return respBody;
    } else {
      return {
        code: '-200',
        message: '上传失败',
      };
    }
  };
  const renderUploadBtn = (info: any) => {
    return (
      <>
        <Upload
          disabled={info.isPublish === 1}
          accept={acceptType}
          name="file"
          beforeUpload={(file, fileList) => {
            appPackageForm.current.setFieldValue('file', { file, fileList });
            return false;
          }}
        >
          <CustomButton
            disable={info.isPublish === 1}
            title="上传升级包"
            buttonType={ButtonType.DefaultButton}
            height={30}
            otherCSSProperties={{ fontSize: 11 }}
            onSubmitClick={() => {}}
          />
          <i
            style={{ color: '#d9d9d9', fontStyle: 'normal', marginLeft: '4px' }}
          >
            最大支持200M
          </i>
        </Upload>
        {info?.packageName && (
          <i
            style={{
              fontStyle: 'normal',
            }}
          >
            {info?.packageName}
          </i>
        )}
      </>
    );
  };
  const uploadAppPackage = async (
    record: any,
    operateType?: 'add' | 'edit',
  ) => {
    if (record?.enable === 0) {
      return;
    }
    try {
      const res: any = await getAppPackageTypeList();
      let initValue = record;
      const uploadField = AppPackageForm?.fields?.find(
        (i: FieldItem) => i.fieldName === 'file',
      );
      const businessType = AppPackageForm?.fields?.find(
        (i: FieldItem) => i.fieldName === 'applyVehicleBusinessType',
      );
      const packageType = AppPackageForm?.fields?.find(
        (i: FieldItem) => i.fieldName === 'packageType',
      );
      const version = AppPackageForm?.fields?.find(
        (i: FieldItem) => i.fieldName === 'version',
      );
      if (operateType === 'edit') {
        initValue = await getAppVersionInfo(record);
      }
      packageType!.disabled = initValue.isPublish === 1;
      version!.disabled = initValue.isPublish === 1;
      const type = res?.data?.map((i: any) => ({
        label: i.typeName,
        value: i.type,
      }));

      packageType!.options = type || [];
      uploadField!.renderFunc = renderUploadBtn.bind(null, initValue);

      businessType!.options =
        record.productType === ProductType.VEHICLE
          ? [
              {
                label: '配送车',
                value: 'DISPATCH',
              },
              {
                label: '售卖车',
                value: 'VENDING',
              },
            ]
          : [
              {
                label: '京麟',
                value: 'JING_LIN',
              },
              {
                label: '重德',
                value: 'ZHONG_DE',
              },
            ];
      showModal({
        title: '应用包信息',
        content: (
          <CommonForm
            className="app-package"
            layout="horizontal"
            formConfig={AppPackageForm}
            defaultValue={initValue}
            getFormInstance={(formRef: any) => {
              appPackageForm.current = formRef;
            }}
            onValueChange={(values: any, changedFieldName: string) => {
              if (changedFieldName === 'version') {
                const val = values.version;
                if (val <= 0) {
                  appPackageForm.current.setFieldValue('version', null);
                }
              }
            }}
          />
        ),
        footer: [
          {
            text: '确定',
            needValidate: true,
            onClick: async (
              handleClose: AnyFunc,
              showLoading: AnyFunc,
              hideLoading: AnyFunc,
            ) => {
              try {
                const fieldsValue =
                  await appPackageForm.current?.validateFields();
                let res = await customRequest(
                  {
                    ...fieldsValue,
                    versionNumber: initValue.versionNumber,
                    packageId: initValue.packageId,
                  },
                  operateType,
                  showLoading,
                );
                if (!res) {
                  return;
                }
                res = JSON.parse(res);
                hideLoading();
                if (res?.code === HttpStatusCode.Success) {
                  message.success('上传应用包成功');
                  handleClose();
                } else {
                  res?.message && message.warning(res?.message);
                }
              } catch (e: any) {
                if (e.message) {
                  message.error(e.message);
                }
              }
            },
          },
        ],
      });
    } catch (e) {}
  };

  const formatColumns = (columns: any[]) => {
    return columns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (text: any, record: any) => {
            return (
              <div className="btn-group">
                <a
                  style={{
                    color: record.enable ? '#1677ff' : 'gray',
                  }}
                  onClick={() => {
                    uploadAppPackage(record, 'add');
                  }}
                >
                  上传应用包
                </a>
                <a onClick={previewVersionList.bind(null, record)}>版本信息</a>
                <a
                  style={{
                    color: record.enable ? '#1677ff' : 'gray',
                  }}
                  onClick={editAppItem.bind(null, record)}
                >
                  编辑
                </a>
                <a
                  onClick={onDisabled.bind(null, record)}
                  style={{
                    color: record.enable ? '#1677ff' : 'red',
                  }}
                >
                  {record.enable ? '禁用' : '启用'}
                </a>
              </div>
            );
          };
          break;
      }
      return col;
    });
  };

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          editAppItem();
        }}
        style={{
          marginBottom: '10px',
        }}
      >
        创建应用
      </Button>
      <Table
        columns={formatColumns(AppTableData)}
        dataSource={tableList}
        loading={loading}
        bordered
        scroll={{
          y: 600,
        }}
        pagination={{
          position: ['bottomCenter'],
          total: pagination.total,
          current: currentPage.current,
          pageSize: currentPage.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '30', '40', '100'],
          showTotal: (total) =>
            `共 ${pagination.totalPage}页,${pagination.total} 条记录`,
        }}
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            setCurrentPage({
              current,
              pageSize,
            });
          }
        }}
      />
    </>
  );
};

export default AppTable;
