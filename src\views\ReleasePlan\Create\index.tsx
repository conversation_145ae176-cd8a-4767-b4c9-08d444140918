import React, { useEffect, useRef, useState } from 'react';
import CommonSteps from '@/components/CommonSteps';
import BreadCrumb from '@/components/BreadCrumb';
import UpgradeSelect from '../components/UpgradeSelect';
import { isEmpty } from '@/utils/utils';
import { Modal, message } from 'antd';
import { UpgradeRule } from '../utils/constant';
import UpgradeRules from '../components/UpgradeRules';
import { createUpgradeTask } from '@/fetch/bussiness/releasePlanning';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';
import { removeInfo } from '@/redux/reducers/createOTATask';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { isEqual } from 'lodash';
import { sendGlobalEvent } from '@/utils/emit';
import DeviceSelection from '@/components/DeviceSelection';
import { useNavigate } from 'react-router-dom';
import ResultFeedback from '@/components/ResultFeedback';
const ReleasePlanManage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const upgradePackageRef = useRef<any>(null);
  const upgradeScopeRef = useRef<any>(null);
  const upgradeRulesRef = useRef<any>(null);
  const selectDevice = useRef<any>(null);
  const [appType, setAppType] = useState<string>('');
  const [issueNumber, setIssueNumber] = useState<string>('');
  const [deviceChoiceType, setDeviceChoiceType] = useState<number>(
    UpgradeRule.DIRECTIONAL,
  );
  const createOTATask = useSelector((state: RootState) => state.createOTATask);
  const [productKey, setProductKey] = useState<string>('');
  const [modelList, setModelList] = useState<any>([]);

  const creatTask = async (values: any) => {
    try {
      const res = await createUpgradeTask(values);
      if (res?.code === HttpStatusCode.Success) {
        message.success('创建成功');
        setIssueNumber(res?.data);
        return true;
      } else {
        message.error(res?.message || '创建失败');
        return false;
      }
    } catch (e) {
      message.error('创建失败');
      return false;
    }
  };

  const handleSubmit = async (cb: AnyFunc) => {
    const flag = await handleValidateUpgradeRules();
    if (!flag) {
      return;
    }
    Modal.confirm({
      content: `确定推送${selectDevice.current?.length}台设备的升级？`,
      onOk: async () => {
        const packageData = upgradePackageRef.current.getFormValues();
        const rulesData = upgradeRulesRef.current.getFormValues();
        const deviceChoiceInfo: any = {
          deviceChoiceType: deviceChoiceType,
        };
        if (deviceChoiceType === UpgradeRule.DIRECTIONAL) {
          deviceChoiceInfo.deviceNameList = selectDevice.current;
        } else if (deviceChoiceType === UpgradeRule.UPLOAD) {
          const result = upgradeScopeRef.current.getUploadResult();
          if (result?.success) {
            deviceChoiceInfo.deviceNameFileS3Key = result?.deviceNameFileS3Key;
            deviceChoiceInfo.deviceNameFileS3BucketName =
              result?.deviceNameFileS3BucketName;
            deviceChoiceInfo.deviceNameFileMd5 = result?.deviceNameFileMd5;
          }
        } else if (deviceChoiceType === UpgradeRule.CONDITIONAL) {
          const result = upgradeScopeRef.current.getConditionFormValues();
          deviceChoiceInfo.productKey = result?.productKey;
          deviceChoiceInfo.productModelNoList = result?.productModelNoList;
          deviceChoiceInfo.groupNoList = result?.groupNoList;
          deviceChoiceInfo.appType = result?.appType;
          deviceChoiceInfo.appName = result?.appName;
          deviceChoiceInfo.appVersionNumber = result?.appVersionNumber;
          deviceChoiceInfo.deviceName = result?.deviceName;
        }
        const requestData = {
          ...packageData,
          ...rulesData,
          productKey: productKey,
          productModelNoList: modelList,
          issueTime: rulesData?.issueTime?.format('YYYY-MM-DD HH:mm:ss'),
          deviceChoiceInfo: deviceChoiceInfo,
        };
        const res = await creatTask(requestData);
        if (res) {
          cb();
        }
        return res;
      },
    });
  };

  const updateSelectDevice = (selectRowKeys: any[], total?: number) => {
    if (deviceChoiceType === UpgradeRule.CONDITIONAL) {
      selectDevice.current = new Array(total);
    } else {
      selectDevice.current = selectRowKeys;
    }
  };
  const updateDeviceChoiceType = (value: any) => {
    setDeviceChoiceType(value);
  };
  const handleValidatePackage = async () => {
    try {
      const res = await upgradePackageRef.current?.validateFormValues();
      if (
        productKey !== res?.productKey ||
        !isEqual(modelList, res?.productModelNoList)
      ) {
        sendGlobalEvent('PRODUCT_HAS_CHANGED');
      }
      setProductKey(res?.productKey);
      setModelList(res?.productModelNoList);
      setAppType(res?.appType);
      return res;
    } catch (e) {
      return false;
    }
  };

  const handleValidateSelectDevice = async () => {
    if (deviceChoiceType === UpgradeRule.UPLOAD) {
      const result = upgradeScopeRef.current.getUploadResult();
      if (result?.success) {
        selectDevice.current = new Array(result?.total);
      }
      return result?.success;
    } else if (deviceChoiceType === UpgradeRule.CONDITIONAL) {
      const searchData = upgradeScopeRef.current.getConditionFormValues();
      const flag = searchData.hasSearched;
      if (!flag) {
        message.error('请先搜索设备');
      }
      return flag;
    } else if (deviceChoiceType === UpgradeRule.DIRECTIONAL) {
      if (isEmpty(selectDevice.current)) {
        message.error('请选择设备');
      }
      return selectDevice.current?.length > 0;
    }

    return false;
  };

  const handleValidateUpgradeRules = async () => {
    try {
      const res = await upgradeRulesRef.current?.validateFormValues();
      return res;
    } catch (e) {
      return false;
    }
  };

  const upgradeResultButton = [
    {
      text: '查看升级详情',
      clickFunc: () => {
        navigate('/releasePlan/detail?issueNumber=' + issueNumber);
      },
    },
  ];

  useEffect(() => {
    if (createOTATask?.info) {
      setProductKey(createOTATask?.info?.productKey);
      setModelList(createOTATask?.info?.productModelNoList);
      setAppType(createOTATask?.info?.appType);
    }
  }, [createOTATask?.info]);
  useEffect(() => {
    return () => {
      dispatch(removeInfo(null));
    };
  }, []);
  return (
    <div className="release-plan-manage">
      <BreadCrumb
        items={[
          { title: '通用设备管理', route: '/releasePlan' },
          { title: '发布计划管理', route: '/releasePlan' },
          { title: '新建发布计划', route: '' },
        ]}
      />
      <CommonSteps
        currentStep={createOTATask?.info?.currentStep}
        stepTipList={['选择升级包', '升级范围', '升级规则', '推送升级']}
        children={[
          <UpgradeSelect ref={upgradePackageRef} />,
          <DeviceSelection
            ref={upgradeScopeRef}
            dataFromOutside={{
              productKey,
              productModelNoList: modelList,
            }}
            deviceChoiceType={deviceChoiceType}
            updateSelectDevice={updateSelectDevice}
            updateDeviceChoiceType={updateDeviceChoiceType}
          />,
          <UpgradeRules ref={upgradeRulesRef} appType={appType} />,
          <ResultFeedback
            buttonConfig={upgradeResultButton}
            resultMessage={'成功创建推送计划'}
          />,
        ]}
        onNextCheckList={[
          handleValidatePackage,
          handleValidateSelectDevice,
          handleValidateUpgradeRules,
        ]}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default ReleasePlanManage;
