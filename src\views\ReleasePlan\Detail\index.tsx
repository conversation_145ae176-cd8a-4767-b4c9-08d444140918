import React, { useEffect, useRef, useState } from 'react';
import BreadCrumb from '@/components/BreadCrumb';
import FormTitle from '@/components/FormTitle';
import qs from 'qs';
import {
  getIssueDeviceCount,
  getIssueDeviceList,
  getTaskDetail,
  stopIssueDevice,
} from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import {
  UpgradeDeviceDetail,
  UpgradeDeviceSearchForm,
  UpgradeForm,
  UpgradeRulesForm,
} from '../utils/constant';
import CommonForm from '@/components/CommonForm';
import { CommonTable, useTableData } from '@jd/x-coreui';
import { isEmpty } from '@/utils/utils';
import UpgradeStatus, {
  Status,
} from '@/views/Firmware/components/UpgradeStatus';
import { Button, Table, message } from 'antd';
import { useDispatch } from 'react-redux';
import { saveInfo } from '@/redux/reducers/createOTATask';
import { useNavigate } from 'react-router-dom';
import showModal from '@/components/commonModal';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';

const Detail = () => {
  const query: any = qs.parse(window.location.search.split('?')[1]);
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedDeviceInfo, setSelectedDeviceInfo] = useState<any[]>([]);
  const initSearchCondition: any = {
    productKey: null,
    issueTaskNumber: null,
    groupNoList: null,
    deviceName: null,
    issueDeviceStatusList: null,
    online: null,
    pageNum: 1,
    pageSize: 10,
  };
  const formRef = useRef<any>(null);
  const searchFormRef = useRef<any>(null);
  const [searchCondition, setSearchCondition] =
    useState<any>(initSearchCondition);
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [config, setConfig] = useState<FormConfig>({ fields: [] });
  const [searchForm, setSearchForm] = useState<any>(UpgradeDeviceSearchForm);
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    getIssueDeviceList,
    'taskDetail',
    autoFetch,
  );
  const getReleasePlanDetail = () => {
    if (query.issueNumber) {
      getTaskDetail(query.issueNumber).then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          const {
            issueAppInfoList,
            productModelNoMap,
            issueTime,
            ...otherData
          } = res?.data || {};
          const { appAlias, appTypeName, appVersion } = !isEmpty(
            issueAppInfoList,
          )
            ? issueAppInfoList[0]
            : {};
          const modalList: any = [];
          const modalListValue: string[] = [];
          for (const [key, value] of Object.entries(productModelNoMap) as any) {
            modalList.push(key);
            modalListValue.push(value);
          }
          const detailInfo = {
            ...otherData,
            issueTime: issueTime ? dayjs(issueTime) : null,
            productModelNoList: modalList,
            ...(!isEmpty(issueAppInfoList) ? issueAppInfoList[0] : {}),
          };

          setTimeout(() => {
            formRef.current?.setFieldsValue({
              ...detailInfo,
              productKey: detailInfo?.productName,
              productModelNoList: modalListValue,
              appType: appTypeName,
              appName: appAlias,
              appVersionNumber: appVersion,
              afterUpgrade: detailInfo?.afterUpgradeName,
            });
          }, 200);

          setDetail(detailInfo);
          setSearchCondition({
            ...searchCondition,
            productKey: res?.data?.productKey,
            issueTaskNumber: res?.data?.issueTaskNumber,
          });
          setAutoFetch(true);
        }
      });
    }
  };

  const mergeConfig = () => {
    const _mergeConfig: FormConfig = {
      fields: [
        {
          fieldName: 'issueTaskNumber',
          label: '发布计划编号',
          type: 'input',
          disabled: true,
          labelCol: { span: 10 },
          wrapperCol: { span: 14 },
          xl: 14,
          xxl: 14,
        },
        ...UpgradeForm.fields?.map((item: FieldItem) => ({
          ...item,
          disabled: true,
        })),
        ...UpgradeRulesForm.fields?.map((item: FieldItem) => ({
          ...item,
          disabled: true,
        })),
      ],
      linkRules: {
        ...UpgradeForm.linkRules,
        ...UpgradeRulesForm.linkRules,
      },
    };

    setConfig(_mergeConfig);
  };

  const handleResend = () => {
    if (selectedRowKeys.length <= 0) {
      message.error('请选择设备');
      return;
    }
    const keyArr: string[] = [];
    selectedDeviceInfo.forEach((v: any) => {
      if (
        [Status.to_be_effective, Status.creating, Status.stop].includes(
          v.issueDeviceStatus,
        )
      ) {
        keyArr.push(v.detailNo);
      }
    });
    if (keyArr.length > 0) {
      showModal({
        title: '“创建中/待生效/已终止”的不可重新推送',
        width: '600px',
        content: '',
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
      return;
    }
    showModal({
      title: `确定重新推送${selectedRowKeys.length}台设备的升级？`,
      width: '600px',
      content: '',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: any) => {
            cb();
          },
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            dispatch(
              saveInfo({
                ...detail,
                deviceNameList: selectedRowKeys,
                currentStep: 2,
              }),
            );
            navigator('/releasePlan/create');
            cb();
          },
        },
      ],
    });
  };
  const onSearchClick = (values: any) => {
    const searchValue = {
      ...searchCondition,
      ...values,
      pageNum: 1,
      pageSize: 10,
    };
    setSelectedRowKeys([]);
    if (!isEqual(searchValue, searchCondition)) {
      setSearchCondition(searchValue);
    } else {
      reloadTable();
    }
  };
  const onResetClick = () => {
    const resetValue = {
      ...initSearchCondition,
      productKey: detail?.productKey,
      issueTaskNumber: detail?.issueTaskNumber,
    };
    setSearchCondition(resetValue);
    setSelectedRowKeys([]);
    searchFormRef.current?.setFieldsValue(resetValue);
  };
  const countUpgradeStatus = async () => {
    const res = await getIssueDeviceCount({
      ...searchCondition,
      productKey: detail?.productKey,
      issueTaskNumber: query.issueNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      const cols: any[] = [];
      const val: any = {};
      res.data.forEach((v: any) => {
        cols.push({
          title: v.name,
          dataIndex: v.key,
        });
        val[v.key] = v.value;
      });
      showModal({
        title: '升级状态统计',
        width: '600px',
        content: (
          <Table
            columns={cols}
            dataSource={[val]}
            pagination={false}
            rowKey={'success'}
          />
        ),
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
    }
  };

  const handleStop = () => {
    if (selectedRowKeys.length <= 0) {
      message.error('请选择设备');
      return;
    }
    const keyArr: string[] = [];
    selectedDeviceInfo.forEach((v: any) => {
      if (
        [
          Status.already_effective,
          Status.to_be_effective,
          Status.received,
          Status.downloading,
          Status.download_success,
        ].includes(v.issueDeviceStatus)
      ) {
        keyArr.push(v.detailNo);
      }
    });
    if (keyArr.length !== selectedDeviceInfo.length) {
      showModal({
        title: '仅“已生效、待生效、已接收、下载中、下载完成“状态可终止',
        width: '600px',
        content: '',
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
      return;
    }

    showModal({
      title: `确定终止${selectedRowKeys?.length}台设备的升级？若设备端已经执行升级任务，终止操作不一定成功`,
      width: '600px',
      content: '',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: any) => {
            cb();
          },
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            const res = await stopIssueDevice({
              issueTaskNumber: query.issueNumber,
              deviceNameList: selectedRowKeys,
            });
            if (res.code === HttpStatusCode.Success) {
              message.success('下发终止成功');
            } else {
              message.error(res.message);
            }
            cb();
          },
        },
      ],
    });
  };

  const formatColumns = (columns: any[]) => {
    return columns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'issueDeviceStatus':
          col.render = (text: any, record: any, index: number) => {
            return <UpgradeStatus status={record.issueDeviceStatus} />;
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onSelect: (record: any, selected: boolean) => {
      if (selected) {
        const newSelectedRowKeys = [...selectedRowKeys, record.deviceName];
        const selectedRows = [...selectedDeviceInfo, record];
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedDeviceInfo(selectedRows);
      } else {
        const newSelectedRowKeys = selectedRowKeys.filter(
          (v: string) => v !== record.deviceName,
        );
        const selectedRows = selectedDeviceInfo.filter(
          (v: any) => v.deviceName !== record.deviceName,
        );
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedDeviceInfo(selectedRows);
      }
    },
    onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
      if (selected) {
        const set1 = new Set(selectedRowKeys);

        changeRows.forEach((v: any) => {
          set1.add(v.deviceName);
          const hasDevice = selectedDeviceInfo?.find(
            (item: any) => item.deviceName === v.deviceName,
          );
          !hasDevice && selectedDeviceInfo.push(v);
        });

        setSelectedRowKeys([...set1]);
        setSelectedDeviceInfo([...selectedDeviceInfo]);
      } else {
        const arr2 = selectedRowKeys.filter((v) => {
          return !changeRows.some((i: any) => i.deviceName === v);
        });
        const selectRows = selectedDeviceInfo.filter(
          (item: any) =>
            !changeRows.some((i: any) => i.deviceName === item.deviceName),
        );
        setSelectedRowKeys([...arr2]);
        setSelectedDeviceInfo(selectRows);
      }
    },
  };
  useEffect(() => {
    getReleasePlanDetail();
  }, [query.issueNumber]);

  useEffect(() => {
    mergeConfig();
  }, []);
  return (
    <div className="release-plan-detail">
      <BreadCrumb
        items={[
          { title: '通用设备管理', route: '/releasePlan' },
          { title: '发布计划管理', route: '/releasePlan' },
          { title: '升级详情', route: '' },
        ]}
      />
      <FormTitle title="配置及软件发布" />
      <CommonForm
        defaultValue={detail}
        getFormInstance={(ref: any) => {
          formRef.current = ref;
        }}
        layout="inline"
        formConfig={config}
      />
      <FormTitle title="发布设备升级详情" />
      <Button
        onClick={handleResend}
        type="primary"
        disabled={selectedRowKeys?.length <= 0}
      >
        重新推送
      </Button>
      <Button
        onClick={countUpgradeStatus}
        type="primary"
        style={{ margin: '0px 10px' }}
      >
        升级状态统计
      </Button>
      <Button
        onClick={handleStop}
        type="primary"
        style={{ backgroundColor: 'red' }}
      >
        终止
      </Button>
      <CommonForm
        defaultValue={{
          productKey: detail?.productKey,
        }}
        getFormInstance={(ref: any) => {
          searchFormRef.current = ref;
        }}
        formType="search"
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        layout="inline"
        formConfig={searchForm}
      />
      <CommonTable
        searchCondition={searchCondition}
        columns={formatColumns(UpgradeDeviceDetail)}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        rowKey={'deviceName'}
        rowSelection={rowSelection}
        onPageChange={(paginationData: any) => {
          const val = {
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          };
          setSearchCondition(val);
        }}
      />
    </div>
  );
};

export default Detail;
