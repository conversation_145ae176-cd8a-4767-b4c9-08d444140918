import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Select,
  Row,
  Col,
  Button,
  Form,
  FormInstance,
  message,
} from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { TableListType, pageSizeOptions } from '@/utils/constant';
import CompareModal from '@/components/CompareModal';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
const EditableContext = React.createContext<FormInstance<any> | null>(null);
interface Props {
  checkModalShow: boolean; // 是否显示弹窗
  onCancel: Function; // 关闭弹窗
  conf: any; // 当前选中的文件
  vehicleName: string; // 车牌号
}
interface DataType {
  key: string;
  version: string;
  operationTypeName: string;
  modifyUser: string;
  modifyTime: string;
}
interface Item {
  key: string;
  version: string;
  operationTypeName: string;
  modifyUser: string;
  modifyTime: string;
}
interface EditableRowProps {
  index: number;
}
interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  options: any;
  check: Function;
}
const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  options,
  check,
  ...restProps
}) => {
  const [currentVersion, setCurrentVersion] = useState<string>();
  const [choosedVersion, setChoosedVersion] = useState<string>();
  const makeOptions = () => {
    if (options.length > 0) {
      return options?.map((item: any) => {
        return {
          label: item,
          value: item,
        };
      });
    } else {
      return [];
    }
  };
  let childNode = children;
  if (editable) {
    childNode = (
      <Row>
        <Col span={17}>
          <Select
            allowClear
            showSearch
            filterOption={(input, option) => {
              const label: any = option?.label || '';
              return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            style={{ width: '100%' }}
            placeholder={'请选择对比操作版本'}
            options={makeOptions()}
            onFocus={() => setCurrentVersion(record.version)}
            onChange={(value: any) => setChoosedVersion(value)}
          />
        </Col>
        <Col span={7}>
          <Button
            type={'text'}
            style={{ color: 'blue' }}
            onClick={() => check(record, choosedVersion)}
          >
            对比查看
          </Button>
        </Col>
      </Row>
    );
  } else {
    <div>{children}</div>;
  }
  return <td {...restProps}>{childNode}</td>;
};

const FileRecord = (props: Props) => {
  const { checkModalShow, onCancel, conf, vehicleName } = props;
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalNumber: 30,
    totalPage: 5,
  });
  const [loading, setLodaing] = useState<boolean>(false);
  const [current, setCurrent] = useState<number>(1);
  const [size, setSize] = useState<number>(10);
  const [options, setOptions] = useState<any[]>([]);
  const [compareModalShow, setCompareModalShow] = useState<boolean>(false);
  const [content, setContent] = useState<any>({});
  const recordColumns: any[] = [
    {
      title: '操作版本号',
      width: 100,
      dataIndex: 'version',
      key: 'version',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作来源',
      width: 50,
      dataIndex: 'operationTypeName',
      key: 'operationTypeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作人',
      width: 70,
      dataIndex: 'modifyUser',
      key: 'modifyUser',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 90,
      dataIndex: 'modifyTime',
      key: 'modifyTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      align: 'center',
      width: 160,
      editable: true,
    },
  ];
  useEffect(() => {
    fetchFileRecord();
    fetchVersionOptions();
  }, [JSON.stringify(conf), current, size, vehicleName]);
  const fetchFileRecord = () => {
    setLodaing(true);
    request({
      method: 'POST',
      path: api.getVehicleConfHistoryList,
      body: {
        vehicleName: vehicleName,
        name: conf.name,
        position: conf.position,
      },
      urlParams: {
        page: current,
        size: size,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalNumber: res.data.total,
            totalPage: res.data.pages,
          });
          setLodaing(false);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchVersionOptions = () => {
    request({
      method: 'POST',
      path: api.getVehicleConfHistoryVersionList,
      body: {
        vehicleName: vehicleName,
        name: conf.name,
        position: conf.position,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setOptions(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const check = (value: any, choosedVersion: any) => {
    if (choosedVersion) {
      request({
        method: 'POST',
        path: api.getDifferentVehicleConfContent,
        body: {
          vehicleName: vehicleName,
          selectedVersion: choosedVersion,
          comparedVersion: value.version,
          name: conf.name,
          position: conf.position,
        },
      })
        .then((res: any) => {
          if (res && res.code === HttpStatusCode.Success) {
            setContent({
              leftTitle: `版本号${res.data.selectedVersion}`,
              leftValue: res.data.selectedContent,
              rightTitle: `版本号${res.data.comparedVersion}`,
              rightValue: res.data.comparedContent,
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
      setCompareModalShow(true);
    } else {
      message.error('请先选择对比操作版本！');
    }
  };
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };
  const columns = recordColumns?.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: DataType) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        options: options,
        check: check,
      }),
    };
  });
  return (
    <>
      <Modal
        width={1100}
        title={
          <p
            style={{ wordBreak: 'break-all', textAlign: 'left', marginRight: '10px' }}
          >{`文件操作记录-[${vehicleName}/${conf.position}/${conf.name}]`}</p>
        }
        visible={checkModalShow}
        onCancel={() => {
          onCancel();
        }}
        footer={
          <div>
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              onSubmitClick={() => {
                onCancel();
              }}
              title={'退出'}
            />
          </div>
        }
      >
        <Table
          components={components}
          bordered
          rowKey={(record) => record.version}
          dataSource={tableList.list}
          columns={columns}
          loading={loading}
          scroll={{
            y: 500,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: current,
            pageSize: size,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(paginationData: any, filters: any, sorter: any, extra: any) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              if (pageSize !== size) {
                setSize(pageSize);
                setCurrent(1);
              } else {
                setSize(pageSize);
                setCurrent(current);
              }
            }
          }}
        />
      </Modal>
      {compareModalShow && (
        <CompareModal
          footer={
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              onSubmitClick={() => {
                setCompareModalShow(false);
                setContent({});
              }}
              title={'退出'}
            />
          }
          onCancel={() => {
            setCompareModalShow(false);
            setContent({});
          }}
          title={`操作内容对比-[${vehicleName}/${conf.position}/${conf.name}]`}
          compareModalShow={compareModalShow}
          content={content}
        />
      )}
    </>
  );
};

export default React.memo(FileRecord);
