.TSL-add-edit {
  margin: 10px;
  padding: 20px;
  background-color: white;
  .title {
    cursor: pointer;
    margin-bottom: 20px;
    span {
      margin-left: 5px;
    }
  }

  .function-type {
    margin-bottom: 15px;
    .field-name {
      text-align: right;
      span:first-child {
        color: red;
      }
    }
    .type {
      span {
        padding: 0 8px;
        height: 32px;
        line-height: 32px;
        color: #555;
        border: 1px solid #555;
        background-color: #fff;
        cursor: pointer;
        &.selected {
          border: 1px solid #0070cc;
          color: #0070cc;
        }
      }
    }
  }

  .bottom-btns {
    text-align: center;
    button:first-child {
      margin-right: 20px;
    }
  }
}
