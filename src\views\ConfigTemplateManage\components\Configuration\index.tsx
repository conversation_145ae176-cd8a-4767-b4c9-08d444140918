import React, { useRef, useState } from 'react';
import { message } from 'antd';
import './index.scss';
import ProductModuleSelector from '@/components/ProductModuleSelector';
import StructuredConfigSelector from '@/components/StructuredConfigSelector';
import ConfigFileSelector from '@/components/ConfigFileSelector';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';

export interface SelectedData {
  product: any;
  blockData: {
    blockNo: string;
    selectedItems: string[]; // 选中的结构化配置项
    selectedFiles: string[]; // 选中的配置文件
  }[];
}

// 第一步：产品选择表单配置
const FirstStepFormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      labelInValue: false,
      validatorRules: [{ required: true, message: '请选择产品' }],
      style: { width: 300 },
      allowClear: true,
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const deviceApi = new Device();
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return (
              res.data?.map((item: any) => ({
                label: item.productName,
                value: item.productKey,
              })) || []
            );
          }
          return [];
        },
      },
    ],
  },
};

const Configuration: React.FC<{
  onRef?: (ref: {
    checkAndGetSelectedData: () => Promise<SelectedData>;
  }) => void;
}> = ({ onRef }) => {
  const productModuleSelectorRef = useRef<any>(null);
  const structuredConfigSelectorRef = useRef<any>(null);
  const configFileSelectorRef = useRef<any>(null);

  const [selectedData, setSelectedData] = useState<any>();

  // 暴露方法给父组件
  React.useEffect(() => {
    if (onRef) {
      onRef({ checkAndGetSelectedData });
    }
  }, [onRef]);

  const checkAndGetSelectedData = async (): Promise<SelectedData> => {
    try {
      // 1. 获取产品和模块选择数据
      const productModuleData =
        await productModuleSelectorRef.current?.getSelectedData();
      if (!productModuleData?.productKey) {
        throw new Error('请选择产品');
      }
      if (!productModuleData?.selectedModules?.length) {
        throw new Error('请选择至少一个模块');
      }

      // 2. 获取结构化配置项数据
      const structuredConfigs =
        await structuredConfigSelectorRef.current?.getSelectedData();

      // 3. 获取配置文件数据
      const configFiles =
        await configFileSelectorRef.current?.getSelectedData();

      // 4. 合并数据，确保至少选择了一个配置项或配置文件
      const allBlockNos = new Set([
        ...Object.keys(structuredConfigs || {}),
        ...Object.keys(configFiles || {}),
      ]);

      if (allBlockNos.size === 0) {
        throw new Error('请至少选择一个配置项或配置文件');
      }

      // 5. 构造返回数据
      const blockData = Array.from(allBlockNos).map((blockNo) => ({
        blockNo,
        selectedItems: structuredConfigs?.[blockNo] || [],
        selectedFiles: configFiles?.[blockNo] || [],
      }));

      const result: SelectedData = {
        product: {
          value: productModuleData.productKey,
          selectedModules: productModuleData.selectedModules,
        },
        blockData,
      };

      return result;
    } catch (error: any) {
      throw new Error(error.message || '请完善配置选择');
    }
  };

  // 处理产品模块选择变化
  const handleProductModuleChange = (data: any) => {
    setSelectedData(data);
  };

  return (
    <div className="configuration-container">
      <ProductModuleSelector
        ref={productModuleSelectorRef}
        selectedData={selectedData}
        onChange={handleProductModuleChange}
        needFormConfig={true}
        formConfig={FirstStepFormConfig}
        formType="search"
        title="选择产品及模块"
      />

      <StructuredConfigSelector
        ref={structuredConfigSelectorRef}
        selectedData={selectedData}
      />

      <ConfigFileSelector
        ref={configFileSelectorRef}
        selectedData={selectedData}
      />
    </div>
  );
};

export default Configuration;
