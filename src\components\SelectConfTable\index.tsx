import React, { useState, useEffect } from 'react';
import { Table, Row, Col, Input, Form, FormInstance } from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import './index.scss';
interface Props {
  title: string; // 标题
  onSearchClick: Function; // 点击查询
  dataSource: any; // 表格内容
  loading: boolean;
  tableColumns: any[]; // 表头
  isSelect: boolean; // 是否可选
  saveSelectedConf?: Function; // 保存选中行数
  rowKey: string;
  selectedRowKeys?: React.Key[];
  selectedRows?: any[];
  columnWidth?: string | number; // 可选列宽度
  formRef?: FormInstance;
}
const SelectConfTable = (props: Props) => {
  const {
    selectedRowKeys,
    columnWidth,
    title,
    onSearchClick,
    dataSource,
    loading,
    tableColumns,
    isSelect,
    saveSelectedConf,
    rowKey,
    formRef,
  } = props;
  const [inputName, setInputName] = useState<string>('');
  const [selectConfFormRef] = Form.useForm();
  return (
    <div className="select-conf-table">
      <div className="select-title">{title}</div>
      <div className="select-content">
        <Row>
          <Col span={22}>
            <Form form={formRef ?? selectConfFormRef}>
              <Form.Item name={'inputContainer'}>
                <Input
                  size={'large'}
                  style={{ marginBottom: '10px' }}
                  placeholder="请输入配置名称，支持关键字联想全称"
                  onChange={(e) => setInputName(e.target.value)}
                  value={inputName}
                  allowClear
                />
              </Form.Item>
            </Form>
          </Col>
          <Col span={2}>
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              onSubmitClick={() => {
                onSearchClick(inputName);
              }}
              title={'查询'}
            />
          </Col>
        </Row>
        <Row>
          {isSelect ? (
            <Table
              dataSource={dataSource}
              loading={loading}
              bordered
              scroll={{
                y: 400,
              }}
              rowKey={(record) => record[rowKey]}
              columns={tableColumns}
              pagination={false}
              rowSelection={{
                columnWidth: columnWidth,
                selectedRowKeys: selectedRowKeys,
                onChange: (
                  selectedRowKeys: React.Key[],
                  selectedRows: any[],
                ) => {
                  if (isSelect && saveSelectedConf) {
                    saveSelectedConf(selectedRowKeys, selectedRows);
                  }
                },
              }}
            />
          ) : (
            <Table
              dataSource={dataSource}
              loading={loading}
              bordered
              scroll={{
                y: 400,
              }}
              rowKey={(record) => record[rowKey]}
              columns={tableColumns}
              pagination={false}
            />
          )}
          {isSelect && (
            <p
              style={{
                marginTop: '10px',
                color: '#777',
                textAlign: 'left',
                fontSize: '15px',
              }}
            >
              {`共${dataSource?.length}条，已选择${selectedRowKeys?.length}条`}
            </p>
          )}
        </Row>
      </div>
    </div>
  );
};

export default React.memo(SelectConfTable);
