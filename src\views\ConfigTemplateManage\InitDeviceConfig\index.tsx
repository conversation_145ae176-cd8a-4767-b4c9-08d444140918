import React, { useState, useRef } from 'react';
import { CommonForm } from '@jd/x-coreui';
import DeviceSelection from '@/components/DeviceSelection';
import { DeviceChoiceTypeMap } from '@/components/DeviceSelection/utils/constant';
import { formatLocation } from '@/utils/formatLocation';
import { InitDeviceConfigFormConfig } from '../utils/columns';
import BreadCrumb from '@/components/BreadCrumb';
import './index.scss';
import { Button, Modal } from 'antd';
import { message } from 'antd';
import { isEmpty } from '@/utils/utils';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useNavigate } from 'react-router-dom';

const configTemplateApi = new ConfigTemplateApi();

const InitDeviceConfig = () => {
  const { templateNo, templateName, productKey, productModelNoList } =
    formatLocation(window.location.search);
  const formatProductModelNoList = productModelNoList?.split('_') || [];
  const navigator = useNavigate();
  const [deviceChoiceType, setDeviceChoiceType] = useState<number>(
    DeviceChoiceTypeMap.DIRECTIONAL,
  );
  const selectedDeviceRef = useRef<any>(null);
  const deviceSelectionRef = useRef<any>(null);

  const updateSelectDevice = (selectRowKeys: any[], total?: number) => {
    if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
      selectedDeviceRef.current = new Array(total);
    } else {
      selectedDeviceRef.current = selectRowKeys;
    }
  };

  const updateDeviceChoiceType = (value: any) => {
    setDeviceChoiceType(value);
  };

  const validateSelectDevice = () => {
    const searchValue = deviceSelectionRef.current?.getConditionFormValues();
    if (!searchValue?.productKey || isEmpty(searchValue?.productModelNoList)) {
      message.error('产品和型号不能为空');
      return false;
    }
    if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
      const flag = searchValue?.hasSearched;
      if (!flag) {
        message.error('请先搜索设备');
        return false;
      }
    } else if (deviceChoiceType === DeviceChoiceTypeMap.DIRECTIONAL) {
      if (isEmpty(selectedDeviceRef.current)) {
        message.error('请选择设备');
        return false;
      }
    } else if (deviceChoiceType === DeviceChoiceTypeMap.UPLOAD) {
      const result = deviceSelectionRef.current?.getUploadResult();
      if (result?.success) {
        selectedDeviceRef.current = new Array(result?.total);
      } else {
        message.error('请先上传设备文件');
        return false;
      }
    }
    return true;
  };

  const handleCancel = () => {
    navigator(-1);
  };

  const handleSubmit = () => {
    if (!validateSelectDevice()) {
      return;
    }
    Modal.confirm({
      content: `是否将配置模板应用到${selectedDeviceRef.current?.length}台设备？`,
      onOk: async () => {
        const deviceChoiceInfo: any = {
          deviceChoiceType: deviceChoiceType,
        };
        if (deviceChoiceType === DeviceChoiceTypeMap.DIRECTIONAL) {
          deviceChoiceInfo.deviceNameList = selectedDeviceRef.current;
        } else if (deviceChoiceType === DeviceChoiceTypeMap.UPLOAD) {
          const result = deviceSelectionRef.current.getUploadResult();
          if (result?.success) {
            deviceChoiceInfo.deviceNameFileS3Key = result?.deviceNameFileS3Key;
            deviceChoiceInfo.deviceNameFileS3BucketName =
              result?.deviceNameFileS3BucketName;
            deviceChoiceInfo.deviceNameFileMd5 = result?.deviceNameFileMd5;
          }
        } else if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
          const result = deviceSelectionRef.current.getConditionFormValues();
          deviceChoiceInfo.productKey = result?.productKey;
          deviceChoiceInfo.productModelNoList = result?.productModelNoList;
          deviceChoiceInfo.groupNoList = result?.groupNoList;
          deviceChoiceInfo.appType = result?.appType;
          deviceChoiceInfo.appName = result?.appName;
          deviceChoiceInfo.appVersionNumber = result?.appVersionNumber;
          deviceChoiceInfo.deviceName = result?.deviceName;
        }
        const requestData = {
          templateNo,
          deviceChoiceInfo: deviceChoiceInfo,
        };
        try {
          const res = await configTemplateApi.initConfigTemplateToDevice(
            requestData,
          );
          if (res.code === HttpStatusCode.Success) {
            if (res?.data?.failTotal > 0) {
              message.error(`共${res?.data?.failTotal}个操作失败`);
            } else {
              message.success('操作成功');
            }
          } else {
            message.error(res?.message || '网络错误');
          }
        } catch (err) {
          message.error('网络错误');
        }
      },
    });
  };

  const breadCrumbList = [
    {
      title: '通用设备管理',
      route: '',
    },
    {
      title: '配置模板管理',
      route: '/ota/configTemplate',
    },
    {
      title: '初始化设备配置',
      route: '',
    },
  ];

  return (
    <div className="init-device-config">
      <BreadCrumb items={breadCrumbList} />
      <div className="search-form">
        <CommonForm
          formConfig={InitDeviceConfigFormConfig}
          defaultValue={{
            templateNo,
            templateName,
          }}
        />
      </div>
      <DeviceSelection
        ref={deviceSelectionRef}
        deviceChoiceType={deviceChoiceType}
        updateDeviceChoiceType={updateDeviceChoiceType}
        updateSelectDevice={updateSelectDevice}
        dataFromOutside={{
          productKey: productKey,
          productModelNoList: formatProductModelNoList,
        }}
        needCheckDeviceDetail
      />
      <div className="bottom-btns">
        <Button onClick={handleCancel}>取消</Button>
        <Button type="primary" onClick={handleSubmit}>
          确认
        </Button>
      </div>
    </div>
  );
};

export default InitDeviceConfig;
