import { Button, Input, Radio, Select, Flex, Form, Modal, message } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import './index.scss';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import { HttpStatusCode } from '@/fetch/core/constant';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import CommonForm from '@/components/CommonForm';
import showModal from '@/components/commonModal';
import { EditBlockFormConfig, AddBlockFormConfig } from '../../utils/column';

const BlockPart = ({
  type,
  selectBlock,
  blockList,
  productKey,
  allBlockInfo,
  handleSelectBlock,
  createBlockCb,
}: {
  type: 'edit' | 'check';
  selectBlock: any;
  blockList: any[];
  productKey: string;
  allBlockInfo?: any[];
  handleSelectBlock: AnyFunc;
  createBlockCb?: AnyFunc;
}) => {
  const fetchApi = new TSLModelFetch();
  const [curEditBlock, setCurEditBlock] = useState<string | null>();

  const handleHover = (val: string) => {
    setCurEditBlock(val);
  };

  const handleEdit = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    let formRef: any = null;
    showModal({
      title: '编辑模块',
      content: (
        <CommonForm
          formConfig={EditBlockFormConfig}
          layout="vertical"
          getFormInstance={(val: any) => {
            formRef = val;
            allBlockInfo &&
              val.setFieldsValue({
                ...allBlockInfo.filter((v) => v.blockNo === curEditBlock)[0],
              });
          }}
        />
      ),
      onOk: async (cb: any) => {
        const val = await formRef.validateFields();
        const res = await fetchApi.createTSLInfo({
          productKey,
          ...val,
        });
        if (res.code === HttpStatusCode.Success) {
          message.success(res.message);
          createBlockCb && createBlockCb();
          setCurEditBlock(null);
          cb && cb();
        } else {
          message.error(res.message);
        }
      },
      onCancel: (cb: any) => {
        setCurEditBlock(null);
        cb && cb();
      },
    });
  };

  const handleAdd = () => {
    let formRef: any = null;
    showModal({
      title: '添加模块',
      content: (
        <CommonForm
          formConfig={AddBlockFormConfig}
          layout="vertical"
          getFormInstance={(val: any) => {
            formRef = val;
            val.setFieldsValue({
              dataProtocol: 'JSON',
            });
          }}
        />
      ),
      onOk: async (cb: any) => {
        const data = await formRef.validateFields();
        const res = await fetchApi.createBlock({
          ...data,
          productKey: productKey,
        });
        if (res.code === HttpStatusCode.Success) {
          message.success(res.message);
          createBlockCb && createBlockCb();
          cb && cb();
        } else {
          message.error(res.message);
        }
      },
      onCancel: (cb: any) => {
        setCurEditBlock(null);
        cb && cb();
      },
    });
  };

  const handleDel = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    showModal({
      title: '',
      content: '确认删除该模块',
      type: 'confirm',
      onOk: (cb: any) => {
        fetchApi
          .delBlockOrTSL({ productKey, blockNo: curEditBlock! })
          .then((res: any) => {
            if (res.code === HttpStatusCode.Success) {
              message.success(res.missage);
              createBlockCb && createBlockCb();
              setCurEditBlock(null);
              cb && cb();
            } else {
              message.warning(res.missage);
            }
          });
      },
      onCancel: (cb: any) => {
        setCurEditBlock(null);
        cb && cb();
      },
    });
  };

  return (
    <div className="block-part">
      <>
        <Flex gap="middle" align="start">
          {type === 'edit' && (
            <div className="add-icon" onClick={() => handleAdd()}>
              +
            </div>
          )}
          <Select
            options={blockList}
            placeholder="请输入模块名称"
            style={{
              width: `${type === 'edit' ? '80%' : '93%'}`,
              marginBottom: '10px',
            }}
            showSearch
            labelInValue
            filterOption={(input: any, option: any) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            onSelect={(value: any) => handleSelectBlock(value)}
          />
        </Flex>

        {blockList.map((v: any) => {
          return (
            <div
              className={
                selectBlock.value === v.value
                  ? 'block-name selected'
                  : 'block-name'
              }
              key={v.value}
              onClick={() => handleSelectBlock(v)}
              onMouseEnter={() => handleHover(v.value)}
              onMouseLeave={() => setCurEditBlock(null)}
            >
              <span className="name">
                {v.label}
                {curEditBlock === v.value && type === 'edit' && (
                  <span className="edit-btns">
                    <EditOutlined onClick={(e) => handleEdit(e)} />
                    <DeleteOutlined onClick={(e) => handleDel(e)} />
                  </span>
                )}
              </span>
              <span className="identifier">标识符: {v.value}</span>
            </div>
          );
        })}
      </>
    </div>
  );
};

export default React.memo(BlockPart);
