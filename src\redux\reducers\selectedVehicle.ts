/* eslint-disable no-unused-vars */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { isNullObject } from '@/utils/utils';
const initialState: any = {
  selectedVehicle: new Map(),
};
const selectedVehicleSlice = createSlice({
  name: 'selectedVehicle',
  initialState,
  reducers: {
    saveSelectedVehicle(state, actions) {
      state.selectedVehicle = actions.payload;
    },
    removeSelectedVehicle(state, actions) {
      const index = actions.payload;
      if (isNullObject(index)) {
        state.selectedVehicle.clear();
      } else if (typeof index === 'string') {
        state.selectedVehicle.delete(index);
      }
    },
  },
});

export const selectedVehicleReducer = selectedVehicleSlice.reducer;
export const { saveSelectedVehicle, removeSelectedVehicle } =
  selectedVehicleSlice.actions;
export const selectedVehicleSelector = (state: RootState) => state.selectedVehicle;
