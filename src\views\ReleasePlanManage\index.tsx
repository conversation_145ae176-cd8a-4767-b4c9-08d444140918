import React, { useState, useEffect, useRef } from 'react';
import Searchform from '@/components/Searchform';
import { TableColumns, searchConfig } from './utils/columns';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  pageSizeOptions,
  ProductType,
  ProductTypeName,
  SearchCondition,
  TableListType,
} from '@/utils/constant';
import { Table, Form, Modal, message } from 'antd';
import {
  saveSearchValues,
  removeSearchValues,
  searchformSelector,
} from '@/redux/reducers/searchform';
import UpgradeRes from './components/UpgradeRes';
import { api } from '@/fetch/core/api';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { formatLocation } from '@/utils/formatLocation';
import './index.scss';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PublishStatus, Application } from './utils/constant';
import { formatDateToSecond } from '@/utils/formatTime';
import { ReleasePlanManageFetch } from './utils/fetch';

const ReleasePlanManagement = () => {
  const fetchApi = new ReleasePlanManageFetch();
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const [formRef] = Form.useForm();
  const historySearchValue = useSelector(searchformSelector).searchValues;
  const [loading, setLoading] = useState<boolean>(false);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [dropDownList, setDropDownList] = useState<any>({
    productType: [
      {
        value: ProductType.VEHICLE,
        label: ProductTypeName.VEHICLE,
      },
      {
        value: ProductType.ROBOT,
        label: ProductTypeName.ROBOT,
      },
    ],
  });
  const latestIssueTaskNumber = sessionStorage.getItem('latestIssueTaskNumber');
  const urlData: any = formatLocation(window.location.search);
  const initSearchCondition = {
    searchForm: {
      number: latestIssueTaskNumber ? latestIssueTaskNumber : null,
      status: null,
      appName: null,
      modifyUser: null,
      issueTime: null,
      issueStartTime: null,
      issueEndTime: null,
      version: null,
      productType: null,
    },
    current: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValue ? historySearchValue : initSearchCondition;
    },
  );
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [issueTaskNumber, setIssueTaskNumber] = useState<any>();
  useEffect(() => {
    if (urlData.clear) {
      sessionStorage.removeItem('latestIssueTaskNumber');
    }
    fetchTableData(searchCondition);
  }, []);

  /**
   * 获取列表数据
   * @param {Object} searchValues 搜索条件
   */
  const fetchTableData = (searchValues: any) => {
    setLoading(true);
    try {
      request({
        method: 'POST',
        path: api.getIssueTaskList,
        body: {
          number: searchValues.searchForm?.number,
          status: searchValues.searchForm.status?.value,
          appName: searchValues.searchForm.appName?.value,
          version: searchValues.searchForm.version?.label,
          modifyUser: searchValues.searchForm?.modifyUser,
          issueStartTime: searchValues.searchForm?.issueStartTime,
          issueEndTime: searchValues.searchForm?.issueEndTime,
          productType: searchValues.searchForm?.productType?.value,
        },
        urlParams: {
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          });
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取模块下拉框列表数据
   */
  const fetchApplicationInfoList = () => {
    request({
      method: 'GET',
      path: api.getApplicationInfoList,
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setDropDownList({
            appName: res.data?.map((item: any) => {
              return { label: item.name, value: item.code };
            }),
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  /**
   * 获取版本号下拉框内容
   * @returns
   */
  const fetchVersionDropDown = async () => {
    const searchValue = formRef.getFieldsValue();
    if (!searchValue?.appName?.value) {
      setDropDownList({
        version: [],
      });
      message.error('请先选择模块！');
      return;
    }
    const res: any = await fetchApi.fetchVersionDropDown({
      appName: searchValue?.appName?.value,
    });
    if (res.code === HttpStatusCode.Success && res.data) {
      setDropDownList({
        version: res.data?.map((item: any) => {
          return { label: item.version, value: item.versionNumber };
        }),
      });
    }
  };

  const cascaderDropDownApiMap = new Map([
    ['appName', () => fetchApplicationInfoList()],
    ['version', () => fetchVersionDropDown()],
  ]);

  const formatColumns = () => {
    return TableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    if (
                      record.issueAppAliasList.indexOf(Application.FIRMWARE) ===
                      -1
                    ) {
                      dispatch(
                        saveSearchValues({
                          routeName: location.pathname,
                          searchValues: searchCondition,
                        }),
                      );
                      navigator(
                        '/releasePlanManage/checkConfSoftware?issueTaskNumber=' +
                          record.number,
                      );
                    }
                  }}
                >
                  升级详情
                </a>
                {record.status === PublishStatus.INEFFECTIVE && (
                  <a
                    onClick={() => {
                      Modal.confirm({
                        icon: <ExclamationCircleOutlined />,
                        content: (
                          <p
                            style={{
                              wordBreak: 'break-all',
                              textAlign: 'left',
                            }}
                          >
                            请确认“作废”{record.number}吗？
                          </p>
                        ),
                        okText: '确认',
                        cancelText: '取消',
                        onCancel: () => {},
                        onOk: () => {
                          request({
                            method: 'PUT',
                            path: api.obsoleteIssueTask + '/' + record.number,
                          })
                            .then((res: any) => {
                              if (res && res.code === HttpStatusCode.Success) {
                                message.success(res.message);
                                fetchTableData(searchCondition);
                              }
                            })
                            .catch((err) => {
                              console.log(err);
                            });
                        },
                      });
                    }}
                  >
                    作废
                  </a>
                )}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const formateSearchConfig = () => {
    return searchConfig?.map((item) => {
      switch (item.name) {
        case 'cascader':
          item.cascaderList = item.cascaderList?.map((value) => {
            return {
              ...value,
              onFocus: cascaderDropDownApiMap.get(value.name),
            };
          });
          break;
      }
      return item;
    });
  };

  const onSearchClick = () => {
    const values = formRef.getFieldsValue();
    const formateTime = formatDateToSecond(values.issueTime);
    const data = {
      searchForm: {
        ...values,
        issueStartTime: formateTime?.startTime,
        issueEndTime: formateTime?.endTime,
      },
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
    fetchTableData(data);
  };

  const onResetClick = () => {
    const search = {
      searchForm: {
        number: null,
        status: null,
        appName: null,
        modifyUser: null,
        issueTime: null,
        issueStartTime: null,
        issueEndTime: null,
        version: null,
        productType: null,
      },
      current: 1,
      pageSize: 10,
    };

    formRef.setFieldsValue(search.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: search,
      }),
    );
    setSearchCondition({ ...search });
    fetchTableData(search);
    sessionStorage.removeItem('latestIssueTaskNumber');
  };
  return (
    <div className="release-plan-manage">
      <div className="searchform">
        <Searchform
          configData={formateSearchConfig()}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          initValues={searchCondition.searchForm}
          formRef={formRef}
          dropDownMap={dropDownList}
        />
      </div>
      <div className="table-container">
        <Table
          rowKey={(record) => record.number}
          loading={loading}
          bordered
          dataSource={tableList.list}
          columns={formatColumns()}
          scroll={{
            y: 600,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: searchCondition.current,
            pageSize: searchCondition.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any,
          ) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                current,
                pageSize,
              };
              dispatch(
                saveSearchValues({
                  routeName: location.pathname,
                  searchValues: newSearchValue,
                }),
              );
              setSearchCondition(newSearchValue);
              fetchTableData(newSearchValue);
            }
          }}
        />
      </div>
      {modalShow && (
        <UpgradeRes
          issueTaskNumber={issueTaskNumber}
          modalShow={modalShow}
          onCancel={() => {
            setModalShow(false);
          }}
        />
      )}
    </div>
  );
};

export default React.memo(ReleasePlanManagement);
