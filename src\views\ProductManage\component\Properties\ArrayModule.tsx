import React, { useEffect } from 'react';
import { Form, Input, Radio } from 'antd';
import { ArrayDataTypeOptions } from '../../utils/column';
import { DataType } from '../../utils/constant';

const ArrayModule = React.memo(
  ({
    disabled,
    formInstance,
    initInfo,
    changeChildDataType,
  }: {
    disabled: boolean;
    formInstance: any;
    initInfo: any;
    changeChildDataType: AnyFunc;
  }) => {
    useEffect(() => {
      formInstance.setFieldsValue({
        childDataType: initInfo.childDataType ?? DataType.INT,
        size: initInfo.size ?? 10,
      });
    }, []);

    return (
      <>
        <Form.Item
          label="元素类型"
          name="childDataType"
          rules={[{ required: true, message: '请选择元素类型' }]}
        >
          <Radio.Group
            options={ArrayDataTypeOptions}
            disabled={disabled}
            onChange={(e) => {
              const type = e.target.value;
              changeChildDataType(type);
            }}
          />
        </Form.Item>
        <Form.Item
          label="元素个数"
          name="size"
          rules={[
            { required: true, message: '元素个数应为1-512' },
            {
              validator: (_, value) => {
                if ((value && value < 1) || value > 512) {
                  return Promise.reject(new Error('元素个数应为1-512'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input placeholder="请输入元素个数" disabled={disabled} />
        </Form.Item>
      </>
    );
  },
);

export default ArrayModule;
