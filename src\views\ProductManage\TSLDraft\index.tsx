import {
  Button,
  message,
  Menu,
  Modal,
  Form,
  Select,
  Upload,
  UploadProps,
} from 'antd';
import React, { useState, useEffect } from 'react';
import './index.scss';
import { useNavigate, useLocation } from 'react-router-dom';
import showModal from '@/components/commonModal';
import { HttpStatusCode } from '@/fetch/core/constant';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import { ArrowLeftOutlined } from '@ant-design/icons';
import ExportTSL from '../component/ExportTSL';
import BlockPart from '../component/BlockPart';
import TSLPart from '../component/TSLPart';
import BtnSelect from '@/components/BtnSelect';
import CommonForm from '@/components/CommonForm';
import { PublishFormConfig } from '../utils/column';
import { request } from '@/fetch/core';
import { formatLocation } from '@/utils/formatLocation';

const TSLDraft = () => {
  const navigator = useNavigate();
  const { productKey, blockNo, blockName } = formatLocation(
    window.location.search,
  );
  const fetchApi = new TSLModelFetch();
  const [quickImportForm] = Form.useForm();
  const [blockList, setBlockList] = useState<any>([]);
  const [selectBlock, setSelectBlock] = useState<any>({});
  const [versionList, setVersionList] = useState<any>([]);
  const [selectVersion, setSelectVersion] = useState<any>(null);
  const [curSelectMenu, setCurSelectMenu] = useState<'copy' | 'import'>('copy');
  const [showImportModal, setShowImportModal] = useState<boolean>(false);
  const [allProduct, setAllProduct] = useState<any[]>([]);
  const sourceProductKey = Form.useWatch('sourceProductKey', quickImportForm);
  const [fileKey, setFileKey] = useState<string>('');
  const [allBlockInfo, setAllBlockInfo] = useState<any>([]);

  useEffect(() => {
    getThingModelBlocks('beta');
    getReleaseVersion(productKey);
  }, []);

  const getReleaseVersion = async (productKey: string) => {
    const res = await fetchApi.getReleaseVersion({ productKey });
    if (res.code === HttpStatusCode.Success) {
      setVersionList(
        res.data.map((v: any) => ({
          label: v.version,
          value: v.version,
        })),
      );
    }
  };

  const getThingModelBlocks = async (version: string) => {
    const res = await fetchApi.getThingModelBlocks({
      productKey,
      version: version,
    });
    if (res.code === HttpStatusCode.Success) {
      setBlockList(
        res.data.map((v: any) => ({
          label: v.blockName,
          value: v.blockNo,
        })),
      );
      setAllBlockInfo(res.data);
      setSelectBlock(
        blockNo && blockName
          ? { value: blockNo, label: blockName }
          : {
              label: res.data[0]?.blockName,
              value: res.data[0]?.blockNo,
            },
      );
    }
  };

  const goBack = () => {
    navigator(`/product/TSLModel?productKey=${productKey}`);
  };

  const handleSelectBlock = (value: any) => {
    setSelectBlock(value);
  };

  const handleSelectVersion = (val: any) => {
    getThingModelBlocks(val);
    setSelectVersion(val);
  };

  const quickImport = async () => {
    const list = await fetchApi.getProductList();
    setAllProduct(list);
    setShowImportModal(true);
  };

  const recover = async () => {
    showModal({
      title: '',
      width: '500px',
      content: '是否确认将此版本覆盖到草稿',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            const res = await fetchApi.copyThingModel({
              sourceProductKey: productKey,
              targetProductKey: productKey,
              sourceVersion: selectVersion,
            });
            if (res.code === HttpStatusCode.Success) {
              message.success(`成功恢复至${selectVersion}版本`);
              setSelectVersion(null);
            } else {
              message.error(res.message);
            }
            cb();
          },
        },
      ],
    });
  };

  const notRecover = () => {
    getThingModelBlocks('beta');
    setSelectVersion(null);
  };

  const publish = async () => {
    let formRef: any = null;
    showModal({
      title: '发布物模型上线',
      content: (
        <CommonForm
          formConfig={PublishFormConfig}
          layout="vertical"
          getFormInstance={(val: any) => {
            formRef = val;
            formRef.setFieldsValue({
              version: String(new Date().getTime()),
            });
          }}
        />
      ),
      onOk: async (cb: any) => {
        const val = await formRef.validateFields();
        const res = await fetchApi.publishThingModel({
          productKey,
          ...val,
        });
        if (res.code === HttpStatusCode.Success) {
          message.success(res.message);
          cb && cb();
          goBack();
        } else {
          message.error(res.message);
        }
      },
      onCancel: (cb: any) => {
        cb && cb();
      },
    });
  };

  const selectFunction = (val: AnyObj) => {
    setCurSelectMenu(val.key);
  };

  const cancelModal = () => {
    getReleaseVersion(productKey);
    setShowImportModal(false);
    setCurSelectMenu('copy');
    setAllProduct([]);
  };

  const uploadProps: UploadProps = {
    maxCount: 1,
    accept: '.json',
    progress: {
      strokeColor: {
        '0%': '#108ee9',
        '100%': '#87d068',
      },
      strokeWidth: 3,
      format: (percent) => percent && `${parseFloat(percent.toFixed(2))}%`,
    },
    beforeUpload: (file: any) => {
      const fileSize = file.size / 1024 / 1024 < 1;
      if (!fileSize) {
        message.warning('文件大小不能超过1MB');
      }

      return fileSize || Upload.LIST_IGNORE;
    },
    customRequest: async (option: any) => {
      const { file, onSuccess } = option;
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = async () => {
        request({
          path: '/k2/oss/upload',
          method: 'POST',
          body: {
            fileKey: file.name,
            bucketName: 'rover-operation',
          },
          newGeteway: true,
        }).then((res: any) => {
          const { uploadUrl } = res.data;
          request({
            absoluteURL: uploadUrl,
            contentType: 'multipart/form-data',
            method: 'PUT',
            body: file,
            timeout: 60000,
            newGeteway: true,
          }).then((res) => {
            setFileKey(file.name);
            onSuccess(res, file);
          });
        });
      };
    },
  };

  const importTSL = async () => {
    if (curSelectMenu === 'copy') {
      const val = await quickImportForm.validateFields();
      const res = await fetchApi.copyThingModel({
        ...val,
        targetProductKey: productKey,
      });
      if (res.code === HttpStatusCode.Success) {
        setShowImportModal(false);
        quickImportForm.resetFields();
        getThingModelBlocks('beta');
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    } else if (curSelectMenu === 'import') {
      const res = await fetchApi.importThingModel({
        fileKey,
        productKey,
        bucketName: 'rover-operation',
      });
      if (res.code === HttpStatusCode.Success) {
        setShowImportModal(false);
        getThingModelBlocks('beta');
        setCurSelectMenu('copy');
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    }
  };

  return (
    <>
      <div className="TSL-draft">
        <div className="back" onClick={goBack}>
          <ArrowLeftOutlined />
          <span>编辑草稿</span>
        </div>
        {selectVersion && <div>当前版本：{selectVersion}</div>}

        <div className="top-btns">
          {!selectVersion && <Button onClick={quickImport}>快速导入</Button>}
          <ExportTSL
            blockList={blockList}
            productKey={productKey}
            version={selectVersion ?? 'beta'}
          />
          <BtnSelect
            btnText="历史版本"
            options={versionList}
            handleSelect={(val) => handleSelectVersion(val)}
          />
        </div>
        <div className="body">
          <BlockPart
            type={selectVersion ? 'check' : 'edit'}
            blockList={blockList}
            allBlockInfo={allBlockInfo}
            selectBlock={selectBlock}
            productKey={productKey}
            handleSelectBlock={handleSelectBlock}
            createBlockCb={() => getThingModelBlocks('beta')}
          />
          <TSLPart
            selectBlock={selectBlock}
            productKey={productKey}
            type={selectVersion ? 'check' : 'edit'}
            version={selectVersion ?? 'beta'}
            backUrl="/product/TSLDraft"
          />
        </div>

        <div className="bottom-btns">
          {selectVersion && (
            <Button type="primary" onClick={recover}>
              恢复到此版本
            </Button>
          )}
          {selectVersion && <Button onClick={notRecover}>取消</Button>}
          {blockList.length > 0 && !selectVersion && (
            <Button type="primary" onClick={publish}>
              发布上线
            </Button>
          )}
          {blockList.length > 0 && !selectVersion && (
            <Button onClick={goBack}>返回</Button>
          )}
        </div>
      </div>

      <Modal
        title="导入物模型"
        className="quick-import-modal"
        open={showImportModal}
        onCancel={cancelModal}
        onOk={importTSL}
      >
        <div className="note">注：导入的物模型会覆盖原来的功能。</div>
        <Menu
          onClick={selectFunction}
          style={{ marginBottom: '20px' }}
          selectedKeys={[curSelectMenu]}
          mode="horizontal"
          items={[
            {
              label: '拷贝产品',
              key: 'copy',
            },
            {
              label: '导入物模型',
              key: 'import',
            },
          ]}
        />
        <Form layout="vertical" form={quickImportForm}>
          {curSelectMenu === 'copy' && (
            <Form.Item
              label="选择产品"
              name="sourceProductKey"
              rules={[{ required: true, message: '请先选择产品' }]}
            >
              <Select
                placeholder="请选择产品"
                options={allProduct}
                onChange={(v) => {
                  getReleaseVersion(v);
                }}
              />
            </Form.Item>
          )}
          {curSelectMenu === 'copy' && (
            <Form.Item
              label="选择版本"
              name="sourceVersion"
              rules={[{ required: true, message: '请先选择版本' }]}
            >
              <Select
                placeholder="请选择版本"
                options={sourceProductKey ? versionList : []}
              />
            </Form.Item>
          )}
          {curSelectMenu === 'import' && (
            <Form.Item label="上传物模型文件" name="fileKey" required>
              <Upload {...uploadProps}>
                <Button>点击上传</Button>
              </Upload>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(TSLDraft);
