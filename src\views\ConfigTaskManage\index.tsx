import React, { useState } from 'react';
import { CommonForm, CommonTable, useTableData } from '@jd/x-coreui';
import { SearchConfig, TableColumns, TaskStatus } from './utils/constants';
import ConfigTaskManageApi from '@/fetch/bussiness/configTaskManage';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import './index.scss';
import { message, Popconfirm } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';

const configTaskManageApi = new ConfigTaskManageApi();

const ConfigTaskManage = () => {
  const initSearchCondition = {
    searchForm: {
      productKey: null,
      productModelNo: null,
      taskNo: null,
      taskStatus: null,
      blockNo: null,
      createUser: null,
      createTimeStart: null,
      createTimeEnd: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchform,
  );
  // 表单搜索的字段
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const { tableData, loading, reloadTable } = useTableData(
    {
      pageNum: searchCondition?.pageNum,
      pageSize: searchCondition?.pageSize,
      ...searchCondition.searchForm,
    },
    configTaskManageApi.getDeviceConfigIssueTaskPage,
  );

  const onSearchClick = (searchCondition: any) => {
    const newSearchCondition = {
      searchForm: searchCondition,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(newSearchCondition);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newSearchCondition,
      }),
    );
  };
  const onResetClick = () => {
    setSearchCondition(initSearchCondition);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
  };

  const handleCreateConfigTask = () => {
    navigator('/configTaskManage/add');
  };

  const handleCheckTaskDetail = (taskNo: any, productKey: any) => {
    navigator(
      `/configTaskManage/detail?taskNo=${taskNo}&productKey=${productKey}`,
    );
  };

  const handleCancelTask = (taskNo: any) => {
    try {
      configTaskManageApi.cancelConfigIssueTask({ taskNo }).then((res) => {
        if (res.code === HttpStatusCode.Success && res?.data) {
          message.success('取消成功');
          reloadTable();
        } else {
          message.error(res?.message || '取消失败');
        }
      });
    } catch (err) {
      console.error(err);
    }
  };

  const middleBtns: any[] = [
    {
      title: '新建任务',
      key: 'addTask',
      onClick: () => handleCreateConfigTask(),
    },
  ];

  const formatColumns = () => {
    return TableColumns?.map((col, index) => {
      switch (col.dataIndex) {
        case 'index':
          return {
            ...col,
            render: (_: any, __: any, index: number) => {
              return (
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              );
            },
          };
        case 'successOrFailed':
          return {
            ...col,
            render: (_: any, record: any) => {
              return `${record?.successTotal}/${record?.failureTotal}`;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (_: any, record: any) => {
              const { taskStatus, taskNo, productKey } = record;
              return (
                <div className="operate-btns">
                  {taskStatus === TaskStatus.TO_BE_EFFECTIVE ||
                  taskStatus === TaskStatus.EFFECTIVE ||
                  taskStatus === TaskStatus.CANCELED ? (
                    <a
                      onClick={() => {
                        handleCheckTaskDetail(taskNo, productKey);
                      }}
                    >
                      任务详情
                    </a>
                  ) : null}
                  {taskStatus !== TaskStatus.CANCELED ? (
                    <Popconfirm
                      title="确认取消该任务？"
                      onConfirm={() => {
                        handleCancelTask(taskNo);
                      }}
                    >
                      <a>取消</a>
                    </Popconfirm>
                  ) : null}
                </div>
              );
            },
          };
        default:
          return col;
      }
    });
  };

  return (
    <div className="config-task-manage">
      <CommonForm
        formConfig={SearchConfig}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        formType="search"
        layout="inline"
        defaultValue={{
          productKey: 'pda',
          fetchProductKey: true,
        }}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        loading={loading}
        columns={formatColumns()}
        searchCondition={searchCondition}
        middleBtns={middleBtns}
        onPageChange={(pagination: any) => {
          const newSearchCondition = {
            ...searchCondition,
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
          };
          setSearchCondition(newSearchCondition);
          dispatch(
            saveSearchValues({
              routeName: location.pathname,
              searchValues: newSearchCondition,
            }),
          );
        }}
      />
    </div>
  );
};

export default ConfigTaskManage;
