import { ReactNode } from 'react';
import {
  dropDownKey,
  dropDownListKey,
  DropDownType,
} from '@/utils/searchFormEnum';
export interface FieldItem {
  // 通用属性
  type?:
    | 'input'
    | 'select'
    | 'rangeTime'
    | 'cascader'
    | 'textarea'
    | 'radioGroup'
    | 'checkboxGroup'
    | 'datePicker'
    | 'timePicker'
    | 'customize'
    | 'dateTime'
    | 'switch'
    | 'inputNumber'
    | 'ReactNode';
  renderFunc?: () => ReactNode;
  fieldName?: string;
  label?: ReactNode;
  placeholder?: string;
  hidden?: boolean; // 是否隐藏字段（依然会收集和校验字段）
  validatorRules?:
    | {
        // 校验规则
        required: boolean;
        message: string;
        validator?: (rule: any, value: any) => Promise<any>;
      }[]
    | any[]
    | null;
  help?: ReactNode;
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  childrenList?: string[]; // 多个表单项合并成一个
  width?: string;
  marginLeft?: number;
  marginRight?: number;
  disabled?: boolean;
  isChild?: boolean;
  xxl?: number;
  xl?: number;
  lg?: number;
  md?: number;
  specialFetch?: 'commonDown' | 'station' | 'city';
  departmentParams?: Object;
  dropDownKey?: dropDownKey;
  dropDownListKey?: dropDownListKey;
  tooltip?: string | ReactNode;

  // switch
  defaultChecked?: boolean;

  // 输入框
  maxLength?: number; // 输入框长度限制
  max?: number; // 数字输入框最大值
  min?: number; // 数字输入框最小值
  autoSize?: boolean | object; // textArea自适应内容高度
  showCount?: boolean;

  // 选择: 单多选、级联、单复选
  options?: any[];
  showSearch?: boolean; // 是否支持搜索
  allowClear?: boolean;
  maxTagCount?: number;
  multiple?: boolean;
  showSelectAll?: boolean;
  mapRelation?: object;
  changeOnSelect?: boolean;
  labelInValue?: boolean;
  showCheckedStrategy?: 'SHOW_PARENT' | 'SHOW_CHILD';
  checkable?: boolean;

  // 时间选择框
  disabledTime?: AnyFunc;
  disabledDate?: AnyFunc;
  showNow?: boolean;
}

export interface FormConfig {
  fields: FieldItem[];
  linkRules?: {
    // 变化的表单项
    [fieldName: string]: {
      // 表单项变化后导致哪些表单项联动变化
      linkFieldName: string;
      // 当前元素要发生什么变化 fetchData->获取数据(比如请求下拉框内容) clear->清空值 refresh->更新为初始值 visible->表单项是否展示 valueDisable->数据不可用 fieldItemDisable->表单项不可用
      rule:
        | 'fetchData'
        | 'clear'
        | 'refresh'
        | 'visible'
        | 'valueDisable'
        | 'fieldItemDisable';
      dependenceData?: any[]; // 依赖元素的值变为哪个时当前元素发生变化
      disabledValue?: any[]; // rule为valueDisable时，哪个值不可用
      fetchFunc?: Function; // rule为fetchData时，请求数据的函数
      refreshFunc?: Function;
    }[];
  };
}
