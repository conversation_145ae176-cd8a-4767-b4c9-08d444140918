import { But<PERSON>, Row, Col, Select, Flex, Table, Modal } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import './index.scss';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import { createRoot } from 'react-dom/client';
import AceEditor from 'react-ace';
// ace mode 模式 json 格式
import 'ace-builds/src-noconflict/mode-json';
import { HttpStatusCode } from '@/fetch/core/constant';
import jszip from 'jszip';
import { saveAs } from 'file-saver';

const ExportTSL = ({
  blockList,
  productKey,
  version,
}: {
  blockList: any[];
  productKey: any;
  version: string;
}) => {
  const fetchApi = new TSLModelFetch();
  const [modalShow, setModalShow] = useState<boolean>(false);
  const editorRef = useRef<any>(null);

  const getModelInfo = async (blockNo: string) => {
    const res = await fetchApi.getThingModel({
      productKey: productKey,
      blockNo: blockNo,
      version,
    });
    if (res.code === HttpStatusCode.Success) {
      return JSON.stringify(JSON.parse(res.data.content), null, 2);
    } else {
      throw new Error(res.message);
    }
  };

  const exportZip = async () => {
    const zip = new jszip();
    const jsonFileData: Blob[] = [];

    for (let i = 0; i < blockList.length; i++) {
      const val = await getModelInfo(blockList[i].value);
      const data = [val];
      const blobData = new Blob(data, { type: 'application/json' });
      jsonFileData.push(blobData);
      zip.file(`${blockList[i].label}.json`, blobData);
    }

    zip.generateAsync({ type: 'blob' }).then(function (res) {
      saveAs(res, 'model.zip');
    });
  };

  const handleAceEditor = (val: string) => {
    editorRef.current.render(
      <AceEditor
        key={new Date().getTime()}
        mode="json"
        value={val}
        maxLines={18}
        minLines={18}
        setOptions={{
          useWorker: false,
          readOnly: true,
          wrap: true,
        }}
        editorProps={{ $blockScrolling: true }}
      />,
    );
  };

  return (
    <>
      <Button
        onClick={async () => {
          let res = '';
          if (blockList[0]?.value) {
            res = await getModelInfo(blockList[0]?.value);
          }
          setModalShow(true);
          setTimeout(() => {
            editorRef.current = createRoot(document.getElementById('editor')!);
            handleAceEditor(res);
          }, 10);
        }}
      >
        物模型TSL
      </Button>
      <Modal
        title="查看物模型"
        open={modalShow}
        width={550}
        className="editor-modal"
        onCancel={() => setModalShow(false)}
        footer={[
          <Button type="primary" key={'export'} onClick={() => exportZip()}>
            导出模型文件
          </Button>,
        ]}
      >
        <Select
          options={blockList}
          placeholder="请输入模块名称"
          style={{ width: '50%', marginBottom: '10px', marginTop: '20px' }}
          showSearch
          defaultValue={blockList[0]?.value}
          filterOption={(input: any, option: any) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          onSelect={async (value: any) => {
            let res = '';
            if (blockList[0]?.value) {
              res = await getModelInfo(value);
            }

            handleAceEditor(res);
          }}
        />
        <div id="editor"></div>
      </Modal>
    </>
  );
};
export default React.memo(ExportTSL);
