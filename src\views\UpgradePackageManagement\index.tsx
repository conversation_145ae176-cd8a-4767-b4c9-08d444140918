import React from 'react';
import AppTable from './components/AppTable';
import ProductTable from './components/Product';
import { Tabs } from 'antd';
import type { TabsProps } from 'antd';
import './index.scss';
enum TabKey {
  APP = '1',
  PRODUCT = '2',
}
const UpgradePackageManagement = () => {
  const items: TabsProps['items'] = [
    {
      key: TabKey.APP,
      label: '应用',
      children: <AppTable />,
    },
    {
      key: TabKey.PRODUCT,
      label: '产品包',
      children: <ProductTable />,
    },
  ];
  return (
    <div className="upgrade-management">
      <div className="operate-area">
        <Tabs
          defaultActiveKey="1"
          items={items}
          onChange={(key: string) => {}}
        />
      </div>
    </div>
  );
};

export default UpgradePackageManagement;
