/* eslint-disable no-unused-vars */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
const initialState: any = {
  vehicleConfigContent: {},
};
const vehicleConfigSlice = createSlice({
  name: 'vehicleConfigContent',
  initialState,
  reducers: {
    saveVehicleConfigContent(state, actions) {
      state.vehicleConfigContent = actions.payload
    },
    removeVehicleConfigContent(state, actions) {
      console.log(actions.payload)
      state.vehicleConfigContent = actions.payload
    }
  }
});

export const vehicleConfigReducer = vehicleConfigSlice.reducer;
export const {
  saveVehicleConfigContent,
  removeVehicleConfigContent,
} = vehicleConfigSlice.actions;
export const vehicleConfigSelector = (state: RootState) => state.vehicleConfig;

