/* eslint-disable no-unused-vars */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { useLocation, useNavigate } from 'react-router-dom';

export declare type NavigatParams = {
  routeName: any, // 路由名称
  searchValues: any // 搜索条件数据
}

const initialState: NavigatParams = {
  routeName: null,
  searchValues: null
};
const routerSlice = createSlice({
  name: 'router',
  initialState,
  reducers: {
    // 跳转到某页面同时保留当前页面状态
    pushWithSaveState(state, event) {
      const navigator = useNavigate()
      const toUrl: any = event.payload
      navigator(toUrl)
    },
    // 跳转到某页面同时卸载所有保留的页面状态
    pushWithUnloadState(state, event) {
      const navigator = useNavigate()
      state.routeName = null
      state.searchValues = null
      const toUrl: any = event.payload
      navigator(toUrl)
    },
    // 正常的返回上一页操作
    pop(state, event) {
      const navigator = useNavigate()
      const toUrl = event.payload
      if (!toUrl) {
        window.history.back()
      } else {
        navigator(toUrl)
      }
    },
    saveSearchValues(state, event: PayloadAction<NavigatParams>) {
      const { routeName, searchValues } = event.payload
      state.routeName = routeName
      state.searchValues = searchValues
    },
    removeSearchValues(state, event) {
      state.routeName = null
      state.searchValues = null
    }
  }
});

export const routerReducer = routerSlice.reducer;
export const {
  pushWithSaveState,
  pushWithUnloadState,
  pop,
  saveSearchValues,
  removeSearchValues,
} = routerSlice.actions;
export const historySearchValueSelector: any = (state: RootState) => state.router;

