$tableHeaderBg: #f5f5f5;
$tableHeaderFontColor: #333333;
$tableRowFontColor: #666666;

// 业务主色
$themeNormal: #3c6ef0;
$themeHover: #3c6ef0;

/*----- 表格 -----*/
.ant-table-wrapper .ant-table-pagination.ant-pagination {
  margin: 16px !important;
}

.ant-table {
  .ant-table-thead
    > tr
    > th:not(:last-child):not(.ant-table-selection-column):not(
      .ant-table-row-expand-icon-cell
    ):not([colspan]) {
    &::before {
      height: 0px;
    }
  }

  .ant-table-header {
    th {
      background-color: $tableHeaderBg;
      color: $tableHeaderFontColor;
    }
  }
  .ant-table-tbody,
  .ant-table-row {
    color: $tableRowFontColor;
  }
  .ant-table-body {
    &::-webkit-scrollbar {
      height: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #b5b5b5;
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: none;
      border-radius: 0;
      background: #fff;
    }
  }
}
.ant-table-wrapper .ant-table-tbody > tr > td {
  padding: 16px;
}
/*----- button -----*/
.ant-btn-default {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(217, 217, 217, 1);
  border-radius: 4px;
  color: rgba(51, 51, 51, 1);
  &:hover {
    border-color: $themeHover;
    color: $themeHover;
  }
}
.ant-btn-primary {
  background: $themeNormal;
  border: 1px solid $themeNormal;
  border-radius: 4px;
  &:hover {
    border-color: $themeHover;
    background: $themeHover;
    color: white;
  }
}

/*----- form -----*/
.ant-form {
  .ant-form-item,
  .ant-form-item-has-success {
    margin-bottom: 16px;
  }
}

/*----- pagination -----*/
.ant-pagination {
  position: relative;
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-options {
    span,
    input {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(102, 102, 102, 1);
      &:hover {
        border-color: $themeHover;
      }
    }
    .ant-select,
    .ant-select-selector {
      &:hover {
        border-color: $themeHover;
      }
    }
    .ant-pagination-next,
    button {
      &:hover {
        border-color: $themeHover;
      }
    }
    .ant-pagination-prev,
    button {
      &:hover {
        border-color: $themeHover;
      }
    }
    button,
    .ant-select-selector,
    input {
      border-radius: 4px;
    }
  }

  .ant-pagination-item {
    border-radius: 4px;
    a {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(102, 102, 102, 1);
    }
    &:hover {
      border-color: $themeHover;
    }
    &.ant-pagination-item-active {
      width: 32px;
      height: 32px;
      background: rgba(60, 110, 240, 1);
      border-radius: 4px;
      border-color: rgba(60, 110, 240, 1);
      a {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(255, 255, 255, 1);
      }
    }
  }
  .ant-pagination-total-text {
    position: absolute;
    left: 0px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
  }
}

/*----- input -----*/
.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
  border-color: $themeHover;
}
.ant-input {
  &:hover {
    border-color: $themeHover;
  }
}
.ant-input-affix-wrapper,
.ant-input-affix-wrapper-status-success {
  height: 32px;
}
.ant-input-affix-wrapper > input.ant-input,
.ant-input-textarea-show-count > .ant-input {
  font-size: 14px;
  font-family: PingFang SC;
}
.ant-form-item-control-input .ant-input-number{
  width: 100%;
}
/*----- select -----*/
.ant-select:hover,
.ant-select-selector:hover {
  border-color: $themeHover;
}
.ant-select:not(.ant-select-disabled):hover {
  .ant-select-selector {
    border-color: $themeHover;
  }
}

/*----- breadCrumb -----*/
.ant-breadcrumb {
  li:last-child > .ant-breadcrumb-link > a {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  a {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: #999999;
  }
  .ant-breadcrumb-separator {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: #999999;
  }
}

/*----- checkbox -----*/
.ant-checkbox {
  .ant-checkbox-input {
    &:hover,
    &:active,
    &:focus {
      border-color: $themeNormal;
    }
  }

  &-checked {
    .ant-checkbox-inner {
      background: #1677ff;
      border-color: #1677ff !important;

      &:hover,
      &:active,
      &:focus {
        border-color: $themeNormal;
      }
    }

    &::after {
      border-color: $themeNormal !important;
    }
  }
  &-disabled {
    .ant-checkbox-inner {
      background: #eee;
      border-color: #eee !important;
    }
  }
}
