import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from 'react';
import './index.scss';
import { CommonTable, useTableData, CommonForm } from '@jd/x-coreui';
import CommandControlFetch from '@/fetch/bussiness/commandControl';
import FileUpload from './FileUpload';
import { flat, isEmpty } from '@/utils/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
} from '@/utils/emit';
import { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import {
  DeviceChoiceTypeMap,
  StepEditFormConfig,
  DeviceTableColumns,
  DeviceSearchForm,
} from './utils/constant';
import { message } from 'antd';
import { cloneDeep } from 'lodash';
import classNames from 'classnames';
import { formatLocation } from '@/utils/formatLocation';

const commonApi = new CommandControlFetch();

const DeviceSelection = forwardRef(
  (
    props: {
      deviceChoiceType: number;
      updateSelectDevice: (selectKeys: any[], total?: number) => void;
      updateDeviceChoiceType: (value: number) => void;
      dataFromOutside?: any;
      extraEditFormConfig?: FormConfig;
      needCheckDeviceDetail?: boolean;
      hideSearchFormConfig?: boolean;
      fetchDeviceListApi?: any;
    },
    ref: any,
  ) => {
    const {
      deviceChoiceType,
      updateSelectDevice,
      updateDeviceChoiceType,
      dataFromOutside,
      extraEditFormConfig,
      needCheckDeviceDetail,
      hideSearchFormConfig,
      fetchDeviceListApi = commonApi.getDeviceList,
    } = props;

    const {
      deviceName = '',
      productKey = '',
      productModelNo = '',
    } = formatLocation(window.location.search);
    const initSearchCondition = {
      productKey: null,
      productModelNoList: null,
      groupNoList: null,
      appType: null,
      appName: null,
      appVersionNumber: null,
      deviceName: null,
      pageNum: 1,
      pageSize: 10,
      hasSearched: false,
    };
    const createOTATask = useSelector(
      (state: RootState) => state.createOTATask,
    );
    const conditionRef = useRef<any>(null);
    const deviceChoiceTypeRef = useRef<any>(null);
    const [searchCondition, setSearchCondition] =
      useState<any>(initSearchCondition);
    const [uploadResult, setUploadResult] = useState<any>(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const [autoFetch, setAutoFetch] = useState<boolean>(false);
    const [dataFromEditForm, setDataFromEditForm] = useState<any>(null);

    const { tableData, loading, reloadTable } = useTableData(
      searchCondition,
      fetchDeviceListApi,
      'deviceSelection',
      autoFetch,
    );

    const validateKeyAndModel = () => {
      if (!dataFromOutside && deviceChoiceTypeRef?.current) {
        const { productKey, productModelNoList } =
          deviceChoiceTypeRef.current.getFieldsValue();
        if (!productKey || isEmpty(productModelNoList)) {
          return false;
        }
      }
      return true;
    };

    const getKeyAndModel = () => {
      if (!dataFromOutside && deviceChoiceTypeRef?.current) {
        const value = deviceChoiceTypeRef.current.getFieldsValue();
        return {
          productKey: value?.productKey,
          productModelNoList: value?.productModelNoList,
        };
      } else {
        return {
          productKey: dataFromOutside?.productKey,
          productModelNoList: dataFromOutside?.productModelNoList,
        };
      }
    };

    const setKeyAndModelInForm = (
      productKey: any,
      productModelNoList: any,
      syncEditInfo: boolean = false,
    ) => {
      conditionRef.current.setFieldsValue({
        productKey,
        productModelNoList,
      });
      if (syncEditInfo) {
        deviceChoiceTypeRef.current.setFieldsValue({
          productKey,
          productModelNoList,
        });
      }
    };

    const onSearchClick = (values: any) => {
      if (!validateKeyAndModel()) {
        message.error('请先选择产品和型号');
        return;
      }
      const { groupNoList, ...otherData } = values || {};
      const groupList = new Set(flat(groupNoList) || []);
      const { productKey, productModelNoList } = getKeyAndModel();
      setSearchCondition({
        ...searchCondition,
        ...otherData,
        groupNoList: [...groupList],
        productKey,
        productModelNoList,
        hasSearched: true,
        pageNum: 1,
        pageSize: 10,
      });
      setSelectedRowKeys([]);
      if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
        updateSelectDevice([], tableData?.total);
      } else {
        updateSelectDevice([]);
      }
    };

    const onResetClick = () => {
      const { productKey, productModelNoList } = getKeyAndModel();
      const resetValue: any = {
        ...initSearchCondition,
        productKey,
        productModelNoList,
      };
      setSearchCondition(resetValue);
      setSelectedRowKeys([]);
      updateSelectDevice([]);
      conditionRef.current.setFieldsValue(resetValue);
    };

    const formattedColumns = useMemo(() => {
      let newDeviceTableColumns = DeviceTableColumns;
      if (!needCheckDeviceDetail) {
        newDeviceTableColumns = DeviceTableColumns.filter(
          (col: any) => col.dataIndex !== 'operate',
        );
      }
      return newDeviceTableColumns.map((col: any) => {
        switch (col.dataIndex) {
          case 'order':
            col.render = (text: any, record: any, index: number) =>
              `${
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              }`;
            break;
          case 'operate':
            col.render = (text: any, record: any) => {
              return (
                <div className="operate-btn">
                  <a>查看配置</a>
                </div>
              );
            };
            break;
          default:
            col.render = (text: any) => `${text || '-'}`;
            break;
        }
        return col;
      });
    }, [searchCondition.pageNum, searchCondition.pageSize]);

    const enhancedSearchFormConfig = useMemo(() => {
      if (!hideSearchFormConfig) {
        return DeviceSearchForm;
      }
      const clonedConfig = cloneDeep(DeviceSearchForm);
      const linkRules = clonedConfig.linkRules;
      if (linkRules?.productKey) {
        const { fetchProductKey, productKey, ...otherLinkRules } = linkRules;
        const filteredProductKeyRules =
          productKey?.filter(
            (item: any) => item.linkFieldName !== 'productModelNoList',
          ) || [];
        clonedConfig.linkRules = {
          ...otherLinkRules,
          ...(filteredProductKeyRules.length > 0 && {
            productKey: filteredProductKeyRules,
          }),
        };
      }
      return clonedConfig;
    }, [hideSearchFormConfig]);

    const formatEditFormConfig = useMemo(() => {
      if (!extraEditFormConfig) {
        return StepEditFormConfig;
      }
      return {
        fields: [
          ...((extraEditFormConfig?.fields as any[]) || []),
          ...StepEditFormConfig?.fields,
        ],
        linkRules: {
          ...(extraEditFormConfig?.linkRules || {}),
        },
      };
    }, [extraEditFormConfig]);

    const onEditFormValueChange = (allValues: any, changedFieldName: any) => {
      const { productKey, productModelNoList, deviceChoiceType } = allValues;
      if (changedFieldName === 'deviceChoiceType') {
        updateDeviceChoiceType(deviceChoiceType);
      }
      if (
        productKey !== dataFromEditForm?.productKey ||
        productModelNoList?.toString() !==
          dataFromEditForm?.productModelNoList?.toString()
      ) {
        setKeyAndModelInForm(productKey, productModelNoList);
        setDataFromEditForm({
          productKey,
          productModelNoList,
        });
      }
    };

    const getSearchFormDefaultValue = () => {
      if (dataFromOutside) {
        return {
          productKey:
            createOTATask.info?.productKey || dataFromOutside?.productKey,
          productModelNoList:
            createOTATask.info?.productModelNoList ||
            dataFromOutside?.productModelNoList,
          fetchProductKey: true,
        };
      }
      return {
        productKey:
          createOTATask.info?.productKey ||
          dataFromEditForm?.productKey ||
          productKey,
      };
    };

    const getEditFormDefaultValue = () => {
      if (!dataFromOutside) {
        return {
          deviceChoiceType,
          ...(productKey && { productKey }),
          fetchProductKey: true,
        };
      }
      return { deviceChoiceType };
    };

    const getUploadResult = () => {
      return uploadResult;
    };

    const getConditionFormValues = () => {
      return {
        ...searchCondition,
      };
    };

    useImperativeHandle(ref, () => {
      return {
        getUploadResult,
        getConditionFormValues,
      };
    });

    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onSelect: (record: any, selected: boolean) => {
        if (selected) {
          const newSelectedRowKeys = [...selectedRowKeys, record.deviceName];
          setSelectedRowKeys(newSelectedRowKeys);
          updateSelectDevice(newSelectedRowKeys);
        } else {
          const newSelectedRowKeys = selectedRowKeys.filter(
            (v: string) => v !== record.deviceName,
          );
          setSelectedRowKeys(newSelectedRowKeys);
          updateSelectDevice(newSelectedRowKeys);
        }
      },
      onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
        if (selected) {
          const set1 = new Set(selectedRowKeys);
          changeRows.forEach((v: any) => {
            set1.add(v.deviceName);
          });
          setSelectedRowKeys([...set1]);
          updateSelectDevice([...set1]);
        } else {
          const arr2 = selectedRowKeys.filter((v: any) => {
            return !changeRows.some((i: any) => i.deviceName === v);
          });
          setSelectedRowKeys([...arr2]);
          updateSelectDevice([...arr2]);
        }
      },
    };

    useEffect(() => {
      if (deviceChoiceType === DeviceChoiceTypeMap.UPLOAD) {
        return;
      }
      const { productKey, productModelNoList } = getKeyAndModel();
      if (productKey && !isEmpty(productModelNoList)) {
        if (dataFromOutside) {
          conditionRef.current.setFieldsValue({
            productKey,
            productModelNoList,
          });
        }
        setSearchCondition({
          ...searchCondition,
          productKey,
          productModelNoList,
        });
        setAutoFetch(true);
      }
    }, [
      dataFromOutside?.productKey,
      dataFromOutside?.productModelNoList?.toString(),
      dataFromEditForm?.productKey,
      dataFromEditForm?.productModelNoList?.toString(),
      deviceChoiceType,
    ]);

    useEffect(() => {
      deviceChoiceTypeRef.current.setFieldValue(
        'deviceChoiceType',
        deviceChoiceType,
      );

      if (deviceChoiceType === DeviceChoiceTypeMap.DIRECTIONAL) {
        updateSelectDevice(selectedRowKeys);
      }
    }, [deviceChoiceType]);

    useEffect(() => {
      if (
        deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL &&
        searchCondition.hasSearched
      ) {
        updateSelectDevice([], tableData?.total);
      }
    }, [tableData?.total]);

    useEffect(() => {
      if (createOTATask.info) {
        const { productKey, productModelNoList, deviceNameList } =
          createOTATask.info;
        const searchValue = {
          ...searchCondition,
          productKey,
          productModelNoList,
        };
        setKeyAndModelInForm(productKey, productModelNoList, true);
        setSearchCondition(searchValue);
        if (!isEmpty(deviceNameList)) {
          setSelectedRowKeys(deviceNameList);
          updateSelectDevice(deviceNameList);
        }
      }
    }, [createOTATask.info]);

    useEffect(() => {
      if (!dataFromOutside) {
        return;
      }
      const cb = () => {
        setSelectedRowKeys([]);
        updateSelectDevice([]);
        updateDeviceChoiceType(0);
        const initValue = {
          ...initSearchCondition,
          productKey: dataFromOutside?.productKey,
          productModelNoList: dataFromOutside?.productModelNoList,
        };
        setSearchCondition(initValue);
        conditionRef.current?.setFieldsValue(initValue);
      };
      addGlobalEventListener('PRODUCT_HAS_CHANGED', cb);
      return () => {
        removeGlobalEventListener('PRODUCT_HAS_CHANGED', cb);
      };
    }, [
      dataFromOutside?.productKey,
      dataFromOutside?.productModelNoList?.toString(),
    ]);

    useEffect(() => {
      if (productKey && productModelNo && deviceName) {
        setKeyAndModelInForm(productKey, [productModelNo], true);
        conditionRef.current?.setFieldsValue({
          deviceName,
        });
        setSearchCondition({
          ...searchCondition,
          productKey: productKey,
          productModelNoList: [productModelNo],
          deviceName,
        });
        setAutoFetch(true);
        const newSelectedRowKeys = [...selectedRowKeys, deviceName];
        setSelectedRowKeys(newSelectedRowKeys);
        updateSelectDevice(newSelectedRowKeys);
      }
    }, [productModelNo, productKey, deviceName]);

    const renderChildCmp = () => {
      switch (deviceChoiceType) {
        case 0:
        case 2:
          return (
            <div
              className={classNames({
                'hide-search-form': hideSearchFormConfig,
              })}
              style={{ position: 'relative' }}
            >
              <CommonForm
                defaultValue={getSearchFormDefaultValue()}
                formConfig={enhancedSearchFormConfig}
                formType="search"
                layout="inline"
                getFormInstance={(ref: any) => {
                  conditionRef.current = ref;
                }}
                onSearchClick={onSearchClick}
                onResetClick={onResetClick}
              />

              <CommonTable
                searchCondition={searchCondition}
                columns={formattedColumns}
                tableListData={{
                  list: tableData?.list || [],
                  totalPage: tableData?.pages,
                  totalNumber: tableData?.total,
                }}
                onPageChange={(paginationData: any) => {
                  const val = {
                    ...searchCondition,
                    pageNum: paginationData.pageNum,
                    pageSize: paginationData.pageSize,
                  };
                  setSearchCondition(val);
                }}
                rowKey={'deviceName'}
                rowSelection={deviceChoiceType != 2 ? rowSelection : null}
              />
              <span
                className="select-num"
                style={{
                  position: 'absolute',
                  fontSize: '14px',
                  left: '20px',
                  bottom: '36px',
                  fontFamily: 'PingFang SC',
                  fontWeight: 'normal',
                  color: 'rgb(153, 153, 153)',
                }}
              >
                已选择
                {deviceChoiceType != 2
                  ? selectedRowKeys.length
                  : searchCondition.hasSearched
                  ? tableData?.total
                  : 0}
                ，共{tableData?.total}
              </span>
            </div>
          );
        case 1:
          return (
            <FileUpload
              productKey={
                dataFromOutside?.productKey || dataFromEditForm?.productKey
              }
              modelList={
                dataFromOutside?.productModelNoList ||
                dataFromEditForm?.productModelNoList
              }
              uploadChange={setUploadResult}
            />
          );
        default:
          break;
      }
    };

    return (
      <div className="device-selection">
        <CommonForm
          defaultValue={getEditFormDefaultValue()}
          getFormInstance={(ref: any) => {
            deviceChoiceTypeRef.current = ref;
          }}
          layout="inline"
          formConfig={formatEditFormConfig}
          onValueChange={onEditFormValueChange}
        />
        {renderChildCmp()}
      </div>
    );
  },
);

export default DeviceSelection;
