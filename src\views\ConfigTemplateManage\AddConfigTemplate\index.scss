.add-config-template-content {
  width: 100%;
  margin-top: 10px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  min-height: calc(100vh - 120px);

  .step-container {
    background: transparent;
    width: 100%;

    > div {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .edit-placeholder {
    background: white;
    padding: 40px;
    border-radius: 8px;
    text-align: center;

    h3 {
      color: #262626;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      color: #8c8c8c;
      margin-bottom: 12px;
      line-height: 1.6;
    }

    pre {
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 16px;
      text-align: left;
      font-size: 12px;
      color: #595959;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
