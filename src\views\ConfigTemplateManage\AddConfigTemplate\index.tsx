import React, { useRef, useState } from 'react';
import { message } from 'antd';
import './index.scss';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import CommonSteps from '@/components/CommonSteps';
import BreadCrumb from '@/components/BreadCrumb';
import Configuration, { SelectedData } from '../components/Configuration';
import EditConfiguration from '../components/EditConfiguration';

// 移除重复的接口定义，使用从Configuration组件导入的SelectedData

const AddConfigTemplate: React.FC = () => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const configRef = useRef<{
    checkAndGetSelectedData: () => Promise<SelectedData>;
  }>();
  const editConfigRef = useRef<any>(null);

  const [selectedConfig, setSelectedConfig] = useState<SelectedData>({
    product: {},
    blockData: [],
  });

  // 第一步：选择配置项和配置文件
  const checkStepOne = async () => {
    try {
      const selectedData: any =
        await configRef.current?.checkAndGetSelectedData();
      console.log('选中的数据：', selectedData);
      setSelectedConfig(selectedData);
      return true;
    } catch (error: any) {
      message.error(error.message);
      return false;
    }
  };

  // 提交创建配置模板
  const handleSubmit = async () => {
    try {
      const templateData = await editConfigRef.current?.getTemplateData();
      const res = await configTemplateApi.current.addConfigTemplate(
        templateData,
      );
      if (res.code === HttpStatusCode.Success) {
        message.success('配置模板创建成功');
        // 成功后直接跳转到列表页
        setTimeout(() => {
          window.location.href = '/ota/configTemplate';
        }, 1500);
        return true;
      } else {
        message.error(res.message || '创建失败');
        return false;
      }
    } catch (error: any) {
      message.error(error.message || '创建失败');
      return false;
    }
  };

  return (
    <>
      <BreadCrumb
        items={[
          { title: '通用设备管理', route: '' },
          { title: '配置模板管理', route: '/ota/configTemplate' },
          { title: '创建配置模板', route: '' },
        ]}
      />
      <div className="add-config-template-content">
        <CommonSteps
          stepTipList={['选择配置项', '编辑配置模板']}
          children={[
            <Configuration ref={configRef} />,
            <EditConfiguration
              ref={editConfigRef}
              selectedConfig={selectedConfig}
            />,
          ]}
          onNextCheckList={[checkStepOne]}
          onSubmit={handleSubmit}
        />
      </div>
    </>
  );
};

export default AddConfigTemplate;
