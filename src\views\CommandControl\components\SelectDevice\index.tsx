import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Button, Form, message, Radio, Upload, UploadProps } from 'antd';
import DeviceTable from '../DeviceTable';
import { request } from '@/fetch/core';
import './index.scss';
import { DeviceChoiceType } from '../../utils/constant';
import { DeviceTableConfig, TaskSelectDevice } from '../../utils/column';
import { CommandControlFetch, Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { FormConfig } from '@/components/CommonForm/formConfig';
const SparkMD5 = require('spark-md5');
const fetchApi = new CommandControlFetch();

const SelectDevice = forwardRef(
  ({ productKey }: { productKey: string }, ref) => {
    const deviceApi = new Device();
    const deviceRef = useRef<any>(null);
    const [formRef] = Form.useForm();
    const [fileKey, setFileKey] = useState<{
      deviceNameFileS3Key: string;
      deviceNameFileS3BucketName: string;
    }>({ deviceNameFileS3Key: '', deviceNameFileS3BucketName: '' });
    const [uploadRes, setUploadRes] = useState<any>({
      success: false,
      failUrl: null,
      total: 0,
    });
    const deviceChoiceType = Form.useWatch('deviceChoiceType', formRef);
    const [formatSearchConfig, setFormatSearchConfig] =
      useState<FormConfig>(TaskSelectDevice);
    const [selectDeviceSearchFormInit, setSelectDeviceSearchFormInit] =
      useState<any>(null);

    useEffect(() => {
      formRef.setFieldsValue({
        deviceChoiceType: DeviceChoiceType.directional_select,
      });
    }, []);

    useEffect(() => {
      if (productKey) {
        setSelectDeviceSearchFormInit({
          searchForm: {
            productKey: productKey,
            productModelNoList: [],
            groupNoList: null,
            deviceName: null,
          },
          pageNum: 1,
          pageSize: 10,
        });
        formatSearch();
      }
    }, [productKey]);

    useImperativeHandle(
      ref,
      () => {
        return {
          checkData,
        };
      },
      [deviceChoiceType, uploadRes],
    );

    const checkData = () => {
      let flag = false;
      let data = {};
      const vals = deviceRef.current?.checkData();

      if (deviceChoiceType === DeviceChoiceType.directional_select) {
        if (vals?.selectedDevice?.length <= 0) {
          message.error('请选择设备!');
        } else {
          flag = true;
          data = {
            deviceChoiceInfo: {
              deviceChoiceType,
              deviceNameList: vals?.selectedDevice,
            },
            num: vals?.selectedDevice?.length,
          };
        }
      } else if (deviceChoiceType === DeviceChoiceType.condition_select) {
        if (!vals?.searchFormSelected) {
          message.error('请重新搜索条件');
        } else {
          flag = true;
          data = {
            deviceChoiceInfo: {
              deviceChoiceType,
              ...vals?.searchForm,
            },
            num: vals?.searchFormSelected,
          };
        }
      } else if (deviceChoiceType === DeviceChoiceType.file_upload) {
        if (!uploadRes.success) {
          message.error('请重新上传文件');
        } else {
          flag = true;
          data = {
            deviceChoiceInfo: {
              deviceChoiceType,
              ...fileKey,
            },
            num: uploadRes.total,
          };
        }
      }
      return flag ? data : flag;
    };

    const formatSearch = async () => {
      const res = await deviceApi.queryProductList();
      if (res.code === HttpStatusCode.Success) {
        const val = {
          ...TaskSelectDevice,
          fields: formatSearchConfig.fields.map((v: any) => {
            if (v.fieldName === 'productKey') {
              v = {
                ...v,
                options: res.data?.map((v: any) => ({
                  label: v.productName,
                  value: v.productKey,
                })),
              };
            }
            return v;
          }),
        };
        setFormatSearchConfig(val);
      }
    };

    const handleDownLoad = async () => {
      const res = await fetchApi.getTemplateFile({
        templateType: 'device_choice_file',
      });
      if (res.code === HttpStatusCode.Success && res.data?.templateUrl) {
        window.open(res.data?.templateUrl, '_self');
      }
    };

    const uploadProps: UploadProps = {
      maxCount: 1,
      accept: '.xlsx',
      progress: {
        strokeColor: {
          '0%': '#108ee9',
          '100%': '#87d068',
        },
        strokeWidth: 3,
        format: (percent) => percent && `${parseFloat(percent.toFixed(2))}%`,
      },
      beforeUpload: (file: any) => {
        const fileSize = file.size / 1024 / 1024 < 1;
        if (!fileSize) {
          message.warning('文件大小不能超过1MB');
        }
        return fileSize || Upload.LIST_IGNORE;
      },
      customRequest: async (option: any) => {
        const { file, onSuccess, onError } = option;
        const spark = new SparkMD5.ArrayBuffer();
        const reader = new FileReader();
        let fileS3Md5 = '';
        reader.onload = function (e) {
          spark.append(e.target?.result);
          fileS3Md5 = spark.end();
        };
        reader.readAsArrayBuffer(file);
        reader.onloadend = async () => {
          request({
            path: '/k2/oss/upload',
            method: 'POST',
            body: {
              fileKey: `导入模板_${Date.now()}.xlsx`,
              bucketName: 'rover-operation',
            },
            newGeteway: true,
          }).then((res: any) => {
            const { uploadUrl, bucketName, fileKey } = res.data;
            request({
              absoluteURL: uploadUrl,
              contentType: 'multipart/form-data',
              method: 'PUT',
              body: file,
              timeout: 60000,
              newGeteway: true,
            }).then((res) => {
              setFileKey({
                deviceNameFileS3Key: fileKey,
                deviceNameFileS3BucketName: bucketName,
              });
              onSuccess(res, file);
              checkDevice(bucketName, fileKey, fileS3Md5, onSuccess, onError);
            });
          });
        };
      },
      onRemove: () => {
        setUploadRes({
          success: false,
          failUrl: null,
          total: 0,
        });
      },
    };

    const checkDevice = async (
      fileS3BucketName: string,
      fileS3Key: string,
      fileS3Md5: string,
      successCb: AnyFunc,
      failCb: AnyFunc,
    ) => {
      const res = await fetchApi.checkDevice({
        productKey,
        fileS3BucketName,
        fileS3Key,
        fileS3Md5,
      });
      if (res.code === HttpStatusCode.Success) {
        if (res.data?.result) {
          successCb();
        } else {
          failCb();
        }
        setUploadRes({
          success: res.data?.result,
          failUrl: res.data.url || null,
          total: res.data?.total || 0,
        });
      } else {
        failCb();
        setUploadRes({
          success: false,
          failUrl: null,
          total: 0,
        });
        message.error(res.message);
      }
    };

    const downLoadFail = () => {
      window.open(uploadRes.failUrl, '_self');
    };

    return (
      <div className="select-device">
        <Form form={formRef} labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
          <Form.Item name="deviceChoiceType" label="设备选择" required>
            <Radio.Group
              options={[
                {
                  label: '定向选择',
                  value: DeviceChoiceType.directional_select,
                },
                { label: '文件上传', value: DeviceChoiceType.file_upload },
                { label: '条件选择', value: DeviceChoiceType.condition_select },
              ]}
            />
          </Form.Item>
          {deviceChoiceType === DeviceChoiceType.file_upload && (
            <>
              <Form.Item label="下载模版">
                <Button type="primary" onClick={handleDownLoad}>
                  导入模版
                </Button>
              </Form.Item>
              <Form.Item label="上传文件">
                <div className="upload-device">
                  <Upload {...uploadProps}>
                    <Button>点击上传</Button>
                  </Upload>
                  {uploadRes.failUrl && (
                    <Button type="primary" onClick={downLoadFail}>
                      下载失败日志
                    </Button>
                  )}
                </div>
              </Form.Item>
            </>
          )}
        </Form>
        {deviceChoiceType !== DeviceChoiceType.file_upload && productKey && (
          <DeviceTable
            ref={deviceRef}
            formConfig={formatSearchConfig}
            tableConfig={DeviceTableConfig}
            initSearchCondition={selectDeviceSearchFormInit}
            rowKey="deviceName"
            showSelect={
              deviceChoiceType === DeviceChoiceType.directional_select
            }
            fetchApi={fetchApi.getDeviceList}
          />
        )}
      </div>
    );
  },
);

export default React.memo(SelectDevice);
