import React, { useState, useEffect, useRef } from 'react';
import { Form, Table, Modal, message } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { ConfigTableData, SearchConfig } from './utils/column';
import { CustomButton } from '@/components/CustomButton';
import {
  pageSizeOptions,
  SearchCondition,
  TableListType,
} from '@/utils/constant';
import {
  saveSearchValues,
  removeSearchValues,
  searchformSelector,
} from '@/redux/reducers/searchform';
import { EnableType } from './utils/constant';
import './index.scss';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { HttpStatusCode } from '@/fetch/core/constant';
import ConfigManagementRequest from '@/fetch/bussiness/configManagement';
import CommonForm from '@/components/CommonForm';
import { RootState } from '@/redux/store';
const configApi = new ConfigManagementRequest();
const ConfigManagement = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const formInstance = useRef<any>(null);
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );
  const initSearchCondition = {
    searchForm: {
      name: null,
      position: null,
      enable: { value: 1, key: 1, label: '有效' },
      productType: null,
    },
    current: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValue?.searchValues
        ? historySearchValue?.searchValues
        : initSearchCondition;
    },
  );
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    fetchTableData(searchCondition);
  }, []);
  const fetchTableData = async (values: any) => {
    console.log(values);
    setLoading(true);
    try {
      const res = await configApi.getConfTemplatePageList(
        {
          name: values?.searchForm.name,
          position: values?.searchForm?.position?.value,
          enable: values?.searchForm?.enable?.value,
          productType: values?.searchForm?.productType?.value,
        },
        {
          pageNum: values.current,
          pageSize: values.pageSize,
        },
      );
      if (res && res.code === HttpStatusCode.Success) {
        setTableList({
          list: res.data.list,
          totalPage: res.data.pages,
          totalNumber: res.data.total,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const changeConfStatus = (record: any, enable: number) => {
    configApi
      .changeConfTemplateState({
        number: record.number,
        enable: enable,
      })
      .then((res) => {
        if (res && res.code === HttpStatusCode.Success) {
          fetchTableData(searchCondition);
          message.success(res.message);
        }
      })
      .catch((err) => {});
  };
  const onDelModule = (record: any) => {
    configApi
      .deleteConfTemplate({
        number: record.number,
      })
      .then((res) => {
        if (res && res.code === HttpStatusCode.Success) {
          fetchTableData(searchCondition);
          message.success(res.message);
        }
      })
      .catch((err) => {});
  };
  const formatColumns = () => {
    return ConfigTableData?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    navigator(
                      '/configManagement/edit?type=edit&number=' +
                        record.number,
                    );
                  }}
                >
                  编辑
                </a>
                {record.enable === EnableType.ENABLE &&
                  record.name !== 'vehicle_info.json' && (
                    <a
                      onClick={() => {
                        Modal.confirm({
                          icon: <ExclamationCircleOutlined />,
                          content: (
                            <p style={{ wordBreak: 'break-all' }}>
                              请确认“作废”{record.name}吗？
                            </p>
                          ),
                          okText: '确认',
                          cancelText: '取消',
                          onCancel: () => {},
                          onOk: () => {
                            changeConfStatus(record, 0);
                          },
                        });
                      }}
                    >
                      作废
                    </a>
                  )}
                {record.enable === EnableType.UNENABLE &&
                  record.name !== 'vehicle_info.json' && (
                    <a
                      onClick={() => {
                        Modal.confirm({
                          icon: <ExclamationCircleOutlined />,
                          content: (
                            <p style={{ wordBreak: 'break-all' }}>
                              请确认删除{record.name}吗？
                            </p>
                          ),
                          okText: '确认',
                          cancelText: '取消',
                          onCancel: () => {},
                          onOk: () => {
                            onDelModule(record);
                          },
                        });
                      }}
                    >
                      删除
                    </a>
                  )}
                {record.enable === EnableType.UNENABLE && (
                  <a
                    onClick={() => {
                      Modal.confirm({
                        icon: <ExclamationCircleOutlined />,
                        content: (
                          <p style={{ wordBreak: 'break-all' }}>
                            请确认“生效”{record.name}吗？
                          </p>
                        ),
                        okText: '确认',
                        cancelText: '取消',
                        onCancel: () => {},
                        onOk: () => {
                          changeConfStatus(record, 1);
                        },
                      });
                    }}
                  >
                    生效
                  </a>
                )}
              </div>
            );
          };
          break;
        default:
          // eslint-disable-next-line react/display-name
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onSearchClick = () => {
    const values = formInstance.current.getFieldsValue();
    const newValue = {
      searchForm: values,
      current: 1,
      pageSize: 10,
    };
    setSearchCondition(newValue);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newValue,
      }),
    );
    fetchTableData(newValue);
  };
  const onResetClick = () => {
    formInstance.current.setFieldsValue(initSearchCondition.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    setSearchCondition({ ...initSearchCondition });
    fetchTableData(initSearchCondition);
  };
  return (
    <div className="config-manage">
      <div className="searchform">
        <CommonForm
          formConfig={SearchConfig}
          layout={'inline'}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          defaultValue={searchCondition.searchForm}
          formType="search"
          colon={false}
          getFormInstance={(formRef: any) => {
            formInstance.current = formRef;
          }}
        />
      </div>
      <div className="table-container">
        <div className="middle-btns">
          <CustomButton
            onSubmitClick={() => {
              navigator('/configManagement/add?type=add');
            }}
            title="新建"
            otherCSSProperties={{ height: 35, marginBottom: 10 }}
          />
        </div>
        <Table
          rowKey={(record) => record.number}
          columns={formatColumns()}
          dataSource={tableList.list}
          loading={loading}
          bordered
          scroll={{
            y: 600,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: searchCondition.current,
            pageSize: searchCondition.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any,
          ) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                current: current,
                pageSize: pageSize,
              };
              console.log(newSearchValue);
              dispatch(
                saveSearchValues({
                  routeName: location.pathname,
                  searchValues: newSearchValue,
                }),
              );
              setSearchCondition(newSearchValue);
              fetchTableData(newSearchValue);
            }
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(ConfigManagement);
