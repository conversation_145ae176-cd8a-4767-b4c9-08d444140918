import React from 'react';
import { Popconfirm } from 'antd';
import './index.scss';

interface Props {
  message: string;
  btnText: string;
  okText?: string;
  cancelText?: string;
  onConfirm: Function;
}
const CommonConfirmBtn = (props: Props) => {
  const { message, btnText, onConfirm, okText, cancelText } = props;

  return (
    <Popconfirm
      placement="left"
      title={message}
      onConfirm={() => onConfirm()}
      okText={okText ?? '确定'}
      cancelText={cancelText ?? '取消'}
      overlayStyle={{ maxWidth: 800 }}
    >
      <a>{btnText}</a>
    </Popconfirm>
  );
};

export default React.memo(CommonConfirmBtn);
