import React, { useState, useEffect, useRef } from 'react';
import { Form, Select, Input, Table, Col, message, Modal, Spin } from 'antd';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { selectedVehicleSelector } from '@/redux/reducers/selectedVehicle';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { formatLocation } from '@/utils/formatLocation';
import BreadCrumb from '@/components/BreadCrumb';
import FormTitle from '@/components/FormTitle';
import { VehicleConfColumns } from '../utils/columns';
import CheckConf from '../components/CheckConf';
import SaveTemplate from '../components/SaveTemplate';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';
import { vehicleConfigSelector } from '@/redux/reducers/vehicleConfig';
import _ from 'lodash';
import FileRecord from '../components/FileRecord';
const BreadCrumbItemsMap = new Map([
  [
    'single',
    [
      { title: 'OTA管理', route: '' },
      { title: '车辆配置与发布', route: '' },
      { title: '车辆配置', route: '' },
    ],
  ],
  [
    'batch',
    [
      { title: 'OTA管理', route: '' },
      { title: '车辆配置与发布', route: '' },
      { title: '批量车辆配置', route: '' },
    ],
  ],
]);
const VehicleConf = () => {
  const selectedVehicle = useSelector(selectedVehicleSelector).selectedVehicle;
  const [form] = Form.useForm();
  const navigator = useNavigate();
  const urlData: any = formatLocation(window.location.search);
  const [vehicleConfOptions, setVehicleConfOptions] = useState<any[]>();
  const [vehicleModuletableList, setVehicleModuletableList] = useState<any[]>(
    [],
  );
  const [showCheckConf, setShowCheckConf] = useState<boolean>(false);
  const [checkValue, setCheckValue] = useState<any>();
  const [modalTitle, setModalTitle] = useState<string>();
  const [saveTemplateModal, setSaveTemplateModal] = useState<boolean>(false);
  const content = useSelector(vehicleConfigSelector).vehicleConfigContent;
  const [spinning, setSpinning] = useState<boolean>(false);
  const [checkModalShow, setCheckModalShow] = useState<boolean>(false);
  const [conf, setConf] = useState<any>();
  useEffect(() => {
    if (urlData.type === 'single') {
      fetchDetail();
    } else if (urlData.type === 'batch' && content.vehicleConfTemplateNumber) {
      getVehicleModuleDetail(content.vehicleConfTemplateNumber);
    }
    fetchVehicleConfOptions();
    if (!content.vehicleType && selectedVehicle.size <= 0) {
      message.error('请重新选择车辆');
    }
  }, []);
  // 获取车型配置模板下拉框内容
  const fetchVehicleConfOptions = () => {
    request({
      method: 'GET',
      path: api.getVehicleConfTemplateList,
      urlParams: {
        vehicleTypeId: content.vehicleTypeId,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVehicleConfOptions(
            res.data?.map((item: any) => {
              return {
                label: item.name,
                value: item.code,
              };
            }),
          );
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 单车车辆配置详情内容
  const fetchDetail = () => {
    if (content.vehicleConfTemplateNumber) {
      getvehicleConfigDetail();
    }
  };
  // 配置过，获取配置模板详情
  const getvehicleConfigDetail = () => {
    request({
      method: 'GET',
      path: api.getVehicleConfigDetail,
      urlParams: {
        vehicleName: urlData.vehicleName,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVehicleModuletableList(res.data.vehicleConfInfoList);
          form.setFieldsValue({
            canOverwriteName: res.data.vehicleConfTemplateCanOverwriteName,
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 没有配置过，获取车辆配置模板详情
  const getVehicleModuleDetail = (number: any) => {
    request({
      method: 'GET',
      path: api.getVehicleConfDetail,
      urlParams: {
        number: number,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVehicleModuletableList(res.data.addedConfFileList);
          form.setFieldsValue({ canOverwriteName: res.data.canOverwriteName });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const formatColumns = () => {
    return VehicleConfColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${index + 1}`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="table-operation">
                <a
                  onClick={() => {
                    if (urlData.type === 'batch') {
                      setModalTitle('编辑车辆配置');
                    } else if (urlData.type === 'single') {
                      setModalTitle(`编辑车辆配置-[${urlData.vehicleName}]`);
                    }
                    setCheckValue(record);
                    setShowCheckConf(true);
                  }}
                >
                  编辑
                </a>
                {urlData.type === 'single' && (
                  <a
                    className="checkrecord"
                    onClick={() => {
                      checkRecord(record);
                    }}
                  >
                    查看记录
                  </a>
                )}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  // 点击查看记录
  const checkRecord = (value: any) => {
    setConf(value);
    setCheckModalShow(true);
  };

  // 保存编辑的文件内容
  const onSaveModal = (values: any, bothModify: number) => {
    if (bothModify === 1) {
      vehicleModuletableList?.forEach((item: any, index: any) => {
        if (item.name === values.name) {
          item.content = values.content;
        }
      });
    } else if (bothModify === 0) {
      vehicleModuletableList?.forEach((item: any, index: any) => {
        if (item.confTemplateNumber === values.confTemplateNumber) {
          item.content = values.content;
        }
      });
    }
  };
  // 去发布
  const onGopublish = () => {
    const vehicleConfTemplateNumber =
      form.getFieldsValue().vehicleConfTemplateNumber.value;
    if (vehicleConfTemplateNumber) {
      navigator('/vehicleConfigAndRelease/releaseConfSoftware');
    } else {
      message.error('未绑定车辆配置模板，不允许发布！');
    }
  };
  // 点击另存为模板
  const saveTemplate = () => {
    form.validateFields().then((values) => {
      setSaveTemplateModal(true);
    });
  };
  // 确定另存为模板
  const onConfirmSaveTemplate = (values: any) => {
    request({
      method: 'POST',
      path: api.saveAsTemplte,
      body: {
        name: values.name,
        canOverwrite: values.canOverwrite,
        vehicleTypeId: values.vehicleTypeId,
        productType: urlData.productType,
        vehicleConfTemplateInfoList: vehicleModuletableList?.map(
          (item: any) => {
            return {
              confTemplateNumber: item.confTemplateNumber,
              content: item.content,
            };
          },
        ),
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setSaveTemplateModal(false);
          fetchVehicleConfOptions();
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 点击确定
  const onSubmitClick = () => {
    const vehicleConfTemplateNumber =
      form.getFieldsValue().vehicleConfTemplateNumber.value;
    // 不为空在前
    if (!vehicleConfTemplateNumber) {
      message.error('未绑定车辆配置模板，不允许提交！');
    } else if (vehicleConfTemplateNumber) {
      if (urlData.type === 'batch' && selectedVehicle.size > 16) {
        window.scrollTo(0, 0);
        setSpinning(true);
      }
      const vehicleNameList: string[] = [];
      selectedVehicle.forEach((value: any) => {
        vehicleNameList.push(value.vehicleName);
      });
      const requestParam: RequestOptions = {
        method: 'POST',
        path: api.submitVehicleConf,
        body: {
          vehicleNameList:
            urlData.type === 'batch' ? vehicleNameList : [urlData.vehicleName],
          vehicleConfTemplateNumber: vehicleConfTemplateNumber,
          vehicleConfInfoList: vehicleModuletableList?.map((item: any) => {
            return {
              content: item.content,
              confTemplateNumber: item.confTemplateNumber,
            };
          }),
        },
      };
      request(requestParam, false, 35000)
        .then((res: any) => {
          if (
            res &&
            (res.code === HttpStatusCode.Success ||
              res.code === HttpStatusCode.Timeout)
          ) {
            setSpinning(false);
            Modal.info({
              content:
                res.code === HttpStatusCode.Success
                  ? '操作成功！'
                  : '系统响应超时，请返回列表页查看结果！',
              onOk() {
                navigator('/vehicleConfigAndRelease');
              },
            });
          }
        })
        .catch((err) => {
          setSpinning(false);
          Modal.info({
            width: '600px',
            content: <div>{err.message}</div>,
            onOk() {},
          });
        });
    }
  };
  return (
    <div className="vehicle-conf">
      <Spin
        tip={<p style={{ fontSize: '20px' }}>{'稍等一下'}</p>}
        spinning={spinning}
        className={'spin'}
      >
        <BreadCrumb items={BreadCrumbItemsMap.get(urlData.type)} />
        <div className="content">
          <FormTitle
            title={
              urlData.type === 'single'
                ? `${urlData.vehicleName}车辆配置`
                : '批量更新车辆配置'
            }
          />
          {urlData.type === 'single' && (
            <Form labelCol={{ span: 3 }} wrapperCol={{ span: 19 }} form={form}>
              <Form.Item name={'vehicleTypeName'} label={'车型名称'}>
                <div>{content.vehicleType}</div>
              </Form.Item>
              <Form.Item
                name={'vehicleConfTemplateNumber'}
                label={'车辆配置模板'}
                rules={[{ required: true, message: '请选择车辆配置模板' }]}
                extra={
                  '注意：如下拉框无可选车辆配置模板，请先在【车辆配置模板管理】里维护改车型的车辆配置模板。'
                }
                initialValue={{
                  key: content.vehicleConfTemplateNumber,
                  label: content.vehicleConfTemplateName,
                  value: content.vehicleConfTemplateNumber,
                }}
              >
                <Select
                  labelInValue
                  options={vehicleConfOptions}
                  placeholder={'请选择该车使用的车辆配置模板'}
                  onChange={(value: any) => getVehicleModuleDetail(value.value)}
                />
              </Form.Item>
              <Form.Item name={'canOverwriteName'} label={'被引用的模板属性'}>
                <Input bordered={false} disabled style={{ color: 'black' }} />
              </Form.Item>
            </Form>
          )}
          {urlData.type === 'batch' && (
            <Form labelCol={{ span: 3 }} wrapperCol={{ span: 19 }} form={form}>
              <Form.Item name={'selectVehicleNumber'} label={'已选车辆'}>
                {`${selectedVehicle.size}辆`}
              </Form.Item>
              <Form.Item
                name={'vehicleTypeName'}
                label={'车型名称'}
                extra={
                  '说明：本功能用于批量维护车辆的配置及内容，提交确定后，“已选车辆”的配置将改成与【车辆配置模板】相同配置内容。'
                }
              >
                <div>{content.vehicleType}</div>
              </Form.Item>
              <Form.Item
                name={'vehicleConfTemplateNumber'}
                label={'车辆配置模板'}
                rules={[{ required: true, message: '请选择车辆配置模板' }]}
                extra={
                  '注意：如下拉框无可选车辆配置模板，请先在【车辆配置模板管理】里维护改车型的车辆配置模板。'
                }
                initialValue={{
                  key: content.vehicleConfTemplateNumber,
                  label: content.vehicleConfTemplateName,
                  value: content.vehicleConfTemplateNumber,
                }}
              >
                <Select
                  labelInValue
                  options={vehicleConfOptions}
                  placeholder={'请选择该车使用的车辆配置模板'}
                  onChange={(value: any) => getVehicleModuleDetail(value.value)}
                />
              </Form.Item>
            </Form>
          )}
          <Col span={21} push={1}>
            <Table
              pagination={false}
              bordered
              dataSource={vehicleModuletableList}
              columns={formatColumns()}
              rowKey={(record) => record.name + record.position}
            />
          </Col>
          <div className="submit-btns">
            <CustomButton
              onSubmitClick={_.throttle(function () {
                onSubmitClick();
              }, 500)}
              title={'确定'}
            />
            {urlData.type === 'single' && (
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                otherCSSProperties={{ marginLeft: '20px' }}
                onSubmitClick={() => saveTemplate()}
                title={'另存为模板'}
              />
            )}
            {urlData.type === 'batch' && (
              <CustomButton
                onSubmitClick={onGopublish}
                otherCSSProperties={{ marginLeft: '20px' }}
                title={'去发布'}
              />
            )}
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              otherCSSProperties={{ marginLeft: '20px' }}
              onSubmitClick={() => navigator('/vehicleConfigAndRelease')}
              title={'取消'}
            />
          </div>
        </div>
      </Spin>
      {showCheckConf && (
        <CheckConf
          modalTitle={modalTitle}
          show={showCheckConf}
          onCancel={() => {
            setShowCheckConf(false);
          }}
          record={checkValue}
          valueList={vehicleModuletableList}
          onSave={(values: any, bothModify: number) =>
            onSaveModal(values, bothModify)
          }
        />
      )}
      {saveTemplateModal && (
        <SaveTemplate
          onCancelSaveTemplate={() => {
            setSaveTemplateModal(false);
          }}
          visible={saveTemplateModal}
          onConfirmSaveTemplate={(values: any) => onConfirmSaveTemplate(values)}
        />
      )}
      {checkModalShow && (
        <FileRecord
          checkModalShow={checkModalShow}
          onCancel={() => {
            setCheckModalShow(false);
          }}
          conf={conf}
          vehicleName={urlData.vehicleName}
        />
      )}
    </div>
  );
};

export default React.memo(VehicleConf);
