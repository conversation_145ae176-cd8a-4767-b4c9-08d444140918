import { FormConfig } from '@/components/CommonForm/formConfig';
import { Device } from '@/fetch/bussiness';
import {
  getAppTypeList,
  getAppTypeListByType,
  getAppVersionListByModel,
} from '@/fetch/bussiness/releasePlanning';
import { HttpStatusCode } from '@/fetch/core/constant';
import { NULL_GROUP, formatTreeData, isEmpty } from '@/utils/utils';
const deviceApi = new Device();
export enum UpgradeRule {
  DIRECTIONAL = 0,
  UPLOAD = 1,
  CONDITIONAL = 2,
}

export enum Priority {
  FULL = 'full',
  DIFF = 'diff',
}

export enum ActivePush {
  YES = 1,
  NO = 0,
}

export enum ExecuteType {
  DIRECT = 'direct',
  REMIND = 'remind',
}

export enum UpgradeType {
  SILENT = 0,
  FORCE = 1,
  OFFLINE = 2,
  REMIND = 3,
}

export enum PushType {
  IMMEDIATELEY = 1,
  TIME = 0,
}

export enum BatchUpgrade {
  YES = 1,
  NO = 0,
}

export const UpgradeStatus = [
  {
    label: '创建中',
    value: 7,
  },
  {
    label: '创建失败',
    value: 8,
  },
  {
    label: '待生效',
    value: 0,
  },
  {
    label: '已生效',
    value: 9,
  },
  {
    label: '已接收',
    value: 1,
  },
  {
    label: '下载中',
    value: 2,
  },
  {
    label: '下载失败',
    value: 10,
  },
  {
    label: '下载完成',
    value: 3,
  },
  {
    label: '安装中',
    value: 4,
  },
  {
    label: '安装完成',
    value: 5,
  },
  {
    label: '安装失败',
    value: 6,
  },
  {
    label: '已取消',
    value: -1,
  },
  {
    label: '已终止',
    value: -2,
  },
];
export const UpgradeResult = [
  {
    label: '成功',
    value: 'success',
  },
  {
    label: '升级中',
    value: 'upgrading',
  },
  {
    label: '失败',
    value: 'failure',
  },
  {
    label: '取消',
    value: 'cancel',
  },
  {
    label: '已终止',
    value: 'stop',
  },
];

export enum AfterUpgradeOptions {
  NONE = 'none',
  AUTO_START = 'app_auto_start',
  REBOOT = 'sys_reboot',
  RESET = 'reset',
  CLEAR_APP_DATA = 'clear_app_data',
  UNINSTALL = 'uninstall_app',
}

export const AfterUpgradeLabel: any = {
  [AfterUpgradeOptions.NONE]: '无操作',
  [AfterUpgradeOptions.AUTO_START]: '应用自启动',
  [AfterUpgradeOptions.REBOOT]: '系统重启',
  [AfterUpgradeOptions.RESET]: '恢复出厂设置',
  [AfterUpgradeOptions.CLEAR_APP_DATA]: '清除应用数据',
  [AfterUpgradeOptions.UNINSTALL]: '卸载应用',
};

export const batchInterval = (max: number, unit: string = '小时') => {
  const arr: any[] = [];
  for (let i = 1; i <= max; i++) {
    arr.push({
      label: i + unit,
      value: i,
    });
  }

  return arr;
};
export const UpgradeForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '请选择产品型号',
      placeholder: '请选择产品',
      type: 'select',
      labelInValue: false,
      allowClear: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'productModelNoList',
      label: '',
      type: 'select',
      placeholder: '请选择型号',
      multiple: true,
      labelInValue: false,
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择型号',
        },
      ],
    },
    {
      fieldName: 'appType',
      label: '选择发布内容',
      type: 'select',
      placeholder: '请选择升级包类型',
      labelInValue: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择升级包类型',
        },
      ],
    },
    {
      fieldName: 'appName',
      label: '',
      type: 'select',
      labelInValue: false,
      wrapperCol: { span: 24 },
      xl: 6,
      xxl: 6,
      placeholder: '请选择固件/应用',
      validatorRules: [
        {
          required: true,
          message: '请选择固件/应用',
        },
      ],
    },
    {
      fieldName: 'appVersionNumber',
      label: '',
      type: 'select',
      labelInValue: false,
      wrapperCol: { span: 24 },
      xl: 6,
      xxl: 6,
      placeholder: '请选择版本号',
      validatorRules: [
        {
          required: true,
          message: '请选择版本号',
        },
      ],
    },
    {
      fieldName: 'description',
      label: '备注',
      type: 'textarea',
      placeholder: '请输入',
      autoSize: { minRows: 2, maxRows: 6 },
      maxLength: 150,
      showCount: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 17 },
      xl: 23,
      xxl: 23,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appType',
        rule: 'clear',
      },

      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
    ],
    appType: [
      {
        linkFieldName: 'appName',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue(true);
          const res = await getAppTypeListByType({
            productKey: values?.productKey,
            type: val,
            enable: 1,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.appAlias,
              value: item.appName,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
    ],
    productModelNoList: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          const values = commonFormRef.getFieldsValue(true);
          if (
            !val ||
            isEmpty(values?.productModelNoList) ||
            !values?.appName ||
            !values?.appType ||
            !values?.productKey
          ) {
            return [];
          }

          const res = await getAppVersionListByModel({
            productKey: values?.productKey,
            appName: values?.appName,
            type: values?.appType,
            enable: 1,
            productModelNoList: values?.productModelNoList,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue(true);
          const res = await getAppVersionListByModel({
            productKey: values?.productKey,
            appName: values?.appName,
            type: values?.appType,
            enable: 1,
            productModelNoList: values?.productModelNoList,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const UpgradeScopeForm: FormConfig = {
  fields: [
    {
      fieldName: 'deviceChoiceType',
      type: 'radioGroup',
      label: '设备选择',
      options: [
        {
          label: '定向选择',
          value: UpgradeRule.DIRECTIONAL,
        },
        {
          label: '文件上传',
          value: UpgradeRule.UPLOAD,
        },
        {
          label: '条件选中',
          value: UpgradeRule.CONDITIONAL,
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 24,
      xxl: 24,
      validatorRules: [
        {
          required: true,
          message: '请选择设备',
        },
      ],
    },
  ],
};

export const DeviceSearchForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      labelInValue: false,
      disabled: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 8,
      xxl: 8,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      labelInValue: false,
      disabled: true,
      multiple: true,
      xl: 8,
      xxl: 8,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'groupNoList',
      label: '设备分组',
      type: 'cascader',
      placeholder: '请选择',
      labelInValue: false,
      multiple: true,
      maxTagCount: 1,
      xl: 8,
      xxl: 8,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'appType',
      label: '升级包依赖',
      type: 'select',
      labelInValue: false,
      placeholder: '升级包类型',
      xl: 8,
      xxl: 8,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'appName',
      label: '',
      type: 'select',
      labelInValue: false,
      placeholder: '升级包名称',
      xl: 6,
      xxl: 6,
      wrapperCol: { span: 24 },
    },
    {
      fieldName: 'appVersionNumber',
      label: '',
      type: 'select',
      labelInValue: false,
      placeholder: '版本号',
      xl: 6,
      xxl: 6,
      wrapperCol: { span: 24 },
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      type: 'input',
      placeholder: '请输入',
      xl: 8,
      xxl: 8,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.productName,
              value: item.productKey,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    productKey: [
      {
        linkFieldName: 'groupNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.getAllGroupList({
            productKey: val,
          });
          if (res.code === HttpStatusCode.Success) {
            const groupData = formatTreeData({
              origin: res?.data?.groupNoList,
              type: 'Cascader',
              level: 0,
              productKey: val,
            });
            groupData.unshift(NULL_GROUP);
            return groupData;
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appType',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: any) => {
          if (!val) {
            return [];
          }
          const res = await getAppTypeList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.value,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appType: [
      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appName',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue();
          const res = await getAppTypeListByType({
            productKey: values?.productKey,
            type: val,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.appAlias,
              value: item.appName,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue();
          const res = await getAppVersionListByModel({
            productKey: values?.productKey,
            appName: values?.appName,
            type: values?.appType,
            enable: 1,
            productModelNoList: values?.productModelNoList,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const DeviceTableColumns = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 100,
  },
  {
    title: '型号',
    dataIndex: 'productModelName',
    align: 'center',
  },
  {
    title: '分组',
    dataIndex: 'groupLevelName',
    align: 'center',
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    align: 'center',
  },
  {
    title: '设备状态',
    dataIndex: 'isOnlineName',
    align: 'center',
  },
];

export const UpgradeRulesForm: FormConfig = {
  fields: [
    {
      fieldName: 'packagePriority',
      label: '升级包规则',
      type: 'radioGroup',
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 16,
      xxl: 16,
      options: [
        {
          label: '仅全量包',
          value: Priority.FULL,
        },
        {
          label: '差分包优先',
          value: Priority.DIFF,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'activePush',
      label: '是否云端主动推送',
      type: 'radioGroup',
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 16,
      xxl: 16,
      options: [
        {
          label: '是',
          value: ActivePush.YES,
        },
        {
          label: '否',
          value: ActivePush.NO,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'executeType',
      label: '任务执行策略',
      type: 'radioGroup',
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 16,
      xxl: 16,
      options: [
        {
          label: '提示执行',
          value: ExecuteType.REMIND,
        },
        {
          label: '直接执行',
          value: ExecuteType.DIRECT,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'downloadType',
      label: '移动网络下载',
      type: 'radioGroup',
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 16,
      xxl: 16,
      options: [
        {
          label: '提示下载',
          value: ExecuteType.REMIND,
        },
        {
          label: '无提示强制下载',
          value: ExecuteType.DIRECT,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'upgradeType',
      label: '安装类型',
      type: 'radioGroup',
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 16,
      xxl: 16,
      options: [
        {
          label: '提示安装',
          value: UpgradeType.REMIND,
        },
        {
          label: '无提示强制安装',
          value: UpgradeType.FORCE,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'immediately',
      label: '推送时间',
      type: 'radioGroup',
      labelCol: { span: 16 },
      wrapperCol: { span: 8 },
      xl: 12,
      xxl: 12,
      options: [
        {
          label: '立即推送',
          value: PushType.IMMEDIATELEY,
        },
        {
          label: '定时推送',
          value: PushType.TIME,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'issueTime',
      label: '',
      type: 'dateTime',
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择日期',
        },
      ],
    },
    {
      fieldName: 'batch',
      label: '是否分批执行',
      type: 'radioGroup',
      labelCol: { span: 16 },
      wrapperCol: { span: 8 },
      xl: 13,
      xxl: 13,
      options: [
        {
          label: '否',
          value: BatchUpgrade.NO,
        },
        {
          label: '是',
          value: BatchUpgrade.YES,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      fieldName: 'batchInterval',
      label: '批次间隔',
      type: 'select',
      options: batchInterval(24),
      labelInValue: false,
      hidden: true,
      placeholder: '请选择',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
      validatorRules: [
        {
          required: true,
          message: '请选择',
        },
      ],
    },
    {
      fieldName: 'batchCount',
      label: '单次推送数量',
      type: 'inputNumber',
      hidden: true,
      min: 0,
      max: 10000,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 7,
      xxl: 7,
      placeholder: '请输入0-10000之间整数',
      validatorRules: [
        {
          required: true,
          message: '请输入0-10000之间整数',
        },
      ],
    },
    {
      fieldName: 'afterUpgrade',
      label: '升级后完成操作',
      type: 'select',
      labelCol: { span: 16 },
      wrapperCol: { span: 8 },
      labelInValue: false,
      xl: 12,
      xxl: 12,
      placeholder: '请选择',
      allowClear: false,
      validatorRules: [
        {
          required: true,
          message: '请选择',
        },
      ],
    },
    {
      fieldName: 'afterUpgradeApps',
      type: 'input',
      placeholder: '请输入应用包名，英文逗号分隔',
      wrapperCol: { span: 12 },
      hidden: true,
      maxLength: 100,
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请输入应用包名，英文逗号分隔',
        },
      ],
    },
  ],
  linkRules: {
    immediately: [
      {
        linkFieldName: 'issueTime',
        rule: 'visible',
        dependenceData: [PushType.TIME],
      },
    ],
    batch: [
      {
        linkFieldName: 'batchInterval',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
      {
        linkFieldName: 'batchCount',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
    ],
    afterUpgrade: [
      {
        linkFieldName: 'afterUpgradeApps',
        rule: 'visible',
        dependenceData: [
          AfterUpgradeOptions.CLEAR_APP_DATA,
          AfterUpgradeOptions.UNINSTALL,
        ],
      },
    ],
  },
};

export const UpgradeDeviceSearchForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'input',
      hidden: true,
    },
    {
      fieldName: 'groupNoList',
      label: '设备分组',
      type: 'cascader',
      placeholder: '请选择',
      labelInValue: false,
      multiple: false,
      xl: 6,
      xxl: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      type: 'input',
      placeholder: '请输入',
      xl: 6,
      xxl: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'issueDeviceStatusList',
      label: '设备端升级结果',
      type: 'select',
      placeholder: '请选择',
      labelInValue: false,
      xl: 6,
      xxl: 6,
      options: UpgradeStatus,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'online',
      label: '在线状态',
      labelInValue: false,
      type: 'select',
      placeholder: '请选择',
      xl: 6,
      xxl: 6,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      options: [
        {
          label: '在线',
          value: 1,
        },
        {
          label: '离线',
          value: 0,
        },
      ],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'groupNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.getAllGroupList({
            productKey: val,
          });
          if (res.code === HttpStatusCode.Success) {
            const groupData = formatTreeData({
              origin: res?.data?.groupNoList,
              type: 'Cascader',
              level: 0,
              productKey: val,
            });
            groupData.unshift(NULL_GROUP);
            return groupData;
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appType',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: any) => {
          if (!val) {
            return [];
          }
          const res = await getAppTypeList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.value,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const UpgradeDeviceDetail = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 40,
  },
  {
    title: '分组',
    dataIndex: 'groupLevelName',
    align: 'center',
    width: 70,
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    align: 'center',
    width: 100,
  },
  {
    title: '生效时间',
    dataIndex: 'issueTime',
    align: 'center',
    width: 130,
  },
  {
    title: '升级状态',
    dataIndex: 'issueDeviceStatus',
    align: 'center',
    width: 170,
  },
  {
    title: '在线状态',
    dataIndex: 'onlineName',
    align: 'center',
    width: 70,
  },
  {
    title: '最后更新时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 130,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center',
    width: 70,
    ellipsis: true,
  },
  {
    title: '是否终止',
    dataIndex: 'isStopName',
    align: 'center',
    width: 70,
  },
];
