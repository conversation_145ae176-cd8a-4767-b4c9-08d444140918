import { createSlice, PayloadAction } from '@reduxjs/toolkit';
const initialState: any = {
  options: [],
};

const stationInfo = createSlice({
  name: 'stationInfo',
  initialState,
  reducers: {
    actionSaveStationInfo(state, action) {
      state.options = action.payload?.stationInfo;
    },
  },
});

export const stationInfoReducer = stationInfo.reducer;
export const { actionSaveStationInfo } = stationInfo.actions;
