import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import dayjs from 'dayjs';
import {
  Tree,
  Select,
  Button,
  Input,
  Table,
  Space,
  Form,
  DatePicker,
  InputNumber,
  message,
  Tabs,
  ConfigProvider,
  Tooltip,
  Collapse,
} from 'antd';
import type { FormInstance } from 'antd';

import { HttpStatusCode } from '@/fetch/core/constant';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import './index.scss';
import showModal from '@/components/commonModal';
import TextArea from 'antd/es/input/TextArea';

const { Panel } = Collapse;
interface FileItem {
  fileNo: string;
  fileName: string;
  content: string;
  blockNo: string;
  blockName: string;
}

interface TabState {
  selectedKeys: string[];
  filteredFileList: FileItem[];
}

interface ValidationResult {
  valid: boolean;
  errors?: { blockNo: string; itemIdentifier: string; message: string }[];
  data?: Record<string, Record<string, any>>;
}

interface SelectedData {
  selectedProduct: any;
  tabStates: Record<string, TabState>;
}

interface ConfigurationProps {
  selectedConfig: {
    product: any;
    blockData: {
      blockNo: string;
      selectedItems: string[]; // 选中的结构化配置项
      selectedFiles: string[]; // 选中的配置文件
    }[];
  };
}

// 使用forwardRef将表单实例暴露给父组件
const ViewConfigFileModal = React.forwardRef<FormInstance, { data: any }>(
  ({ data }, ref) => {
    const [form] = Form.useForm();

    // 将表单实例暴露给父组件
    React.useImperativeHandle(ref, () => form);

    return (
      <Form
        form={form}
        layout="vertical"
        initialValues={data}
        className="view-config-file-form"
      >
        <Form.Item
          label={<span className="required-label">产品模块</span>}
          required
        >
          <Input.Group compact>
            <Form.Item name="productKey" noStyle>
              <Select
                style={{ width: '50%' }}
                disabled
                options={[{ label: data.productName, value: data.productKey }]}
              />
            </Form.Item>
            <Form.Item name="blockName" noStyle>
              <Select
                style={{ width: '50%' }}
                disabled
                options={[{ label: data.blockName, value: data.blockNo }]}
              />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        <Form.Item
          label={<span className="required-label">配置文件编号</span>}
          name="fileNo"
          required
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label={<span className="required-label">配置文件名称</span>}
          name="fileName"
          required
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label={<span className="required-label">配置路径</span>}
          name="filePath"
          required
        >
          <Input disabled />
        </Form.Item>
        <Form.Item label="描述" name="description">
          <TextArea
            disabled
            autoSize={{ minRows: 3, maxRows: 5 }}
            maxLength={100}
            showCount
          />
        </Form.Item>

        <Form.Item
          label={<span className="required-label">模板内容</span>}
          name="content"
          required
        >
          <TextArea
            autoSize={{ minRows: 6, maxRows: 10 }}
            maxLength={10000}
            showCount
          />
        </Form.Item>
      </Form>
    );
  },
);
const Configuration = React.forwardRef<
  {
    getInputValues: () => Promise<ValidationResult>;
  },
  ConfigurationProps
>(({ selectedConfig }, ref) => {
  const configTemplateApi = useRef(new ConfigTemplateApi());
  const [configData, setConfigData] = useState<any[]>([]);
  const [activeBlockNo, setActiveBlockNo] = useState<string>();
  const [tabStates, setTabStates] = useState<Record<string, TabState>>({});
  const [loading, setLoading] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {},
  );
  // 用于触发数组元素添加/删除后的重新渲染
  const [update, setUpdate] = useState(0);
  // 使用ref保存选中的数据，防止异步操作时数据不同步
  const selectedDataRef = useRef<SelectedData>({
    selectedProduct: {},
    tabStates: {},
  });
  const inputValuesRef = useRef<Record<string, Record<string, any>>>({});
  const formInstancesRef = useRef<Record<string, any>>({});

  const formValidateFields = async () => {
    console.log('Form instances:', formInstancesRef.current);
    const validatePromises = Object.entries(formInstancesRef.current).map(
      async ([blockNo, form]) => {
        try {
          // 等待每个表单的校验结果
          const values = await form.validateFields();
          console.log(`Block ${blockNo} validation passed:`, values);
          return { blockNo, valid: true, values };
        } catch (errors) {
          console.log(`Block ${blockNo} validation failed:`, errors);
          return { blockNo, valid: false, errors };
        }
      },
    );

    // 等待所有表单校验完成
    const results = await Promise.all(validatePromises);
    const hasErrors = results.some((result) => !result.valid);

    if (hasErrors) {
      // 如果有错误，显示第一个错误的表单
      const firstErrorBlock = results.find((result) => !result.valid);
      if (firstErrorBlock) {
        setActiveBlockNo(firstErrorBlock.blockNo);
      }
    }

    return !hasErrors;
  };

  // 校验输入值
  const validateInputValues = () => {
    const errors: {
      blockNo: string;
      itemIdentifier: string;
      message: string;
    }[] = [];

    configData.forEach((block: any) => {
      if (block.itemsTypeList) {
        block.itemsTypeList.forEach((itemType: any) => {
          if (itemType.itemList) {
            itemType.itemList.forEach((item: any) => {
              const content =
                typeof item.content === 'string'
                  ? JSON.parse(item.content)
                  : item.content;

              if (Array.isArray(content)) {
                content.forEach((configItem) => {
                  const value = getInputValue(
                    block.blockNo,
                    configItem.identifier,
                    DataType.STRUCTURE // 从structureInfo中获取
                  );
                  if (
                    configItem.required &&
                    (value === undefined || value === null || value === '')
                  ) {
                    errors.push({
                      blockNo: block.blockNo,
                      itemIdentifier: configItem.identifier,
                      message: `${configItem.name}是必填项`,
                    });
                  }
                  if (configItem.dataType === 'ARRAY') {
                    if (
                      !Array.isArray(value) ||
                      value.length === 0 ||
                      value.every(
                        (v) => v === null || v === undefined || v === '',
                      )
                    ) {
                      errors.push({
                        blockNo: block.blockNo,
                        itemIdentifier: configItem.identifier,
                        message: `${configItem.name}至少需要一个有效值`,
                      });
                    }
                  }
                });
              }
            });
          }
        });
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  };

  // 暴露inputValuesRef和formInstancesRef给父组件
  useImperativeHandle(ref, () => ({
    getInputValues: async () => {
      // 校验数据 - 等待表单校验完成
      const formValid = await formValidateFields();

      if (!formValid) {
        return {
          valid: false,
          errors: [
            {
              blockNo: '',
              itemIdentifier: '',
              message: '表单校验失败，请检查必填项',
            },
          ],
        };
      }

      // 如果表单校验通过，再进行自定义校验
      const validationResult = validateInputValues();
      if (validationResult.valid) {
        return {
          valid: true,
          data: inputValuesRef.current,
        };
      } else {
        return {
          valid: false,
          errors: validationResult.errors,
        };
      }
    },
  }));

  // 获取配置数据
  const fetchConfigData = async () => {
    setLoading(true);
    try {
      // 调用getEnableConfigDetailList获取详细配置
      const detailRes =
        await configTemplateApi.current.getEnableConfigDetailList({
          productKey: selectedConfig.product.value,
          blockConfigList: selectedConfig.blockData.map((v) => ({
            blockNo: v.blockNo,
            itemNoList: v.selectedItems,
            fileNoList: v.selectedFiles,
          })),
        });

      if (detailRes.code === HttpStatusCode.Success) {
        setConfigData(detailRes.data);

        // 初始化每个tab的状态
        if (detailRes.data.length > 0) {
          const initialStates = detailRes.data.reduce(
            (acc: any, block: any) => {
              acc[block.blockNo] = {
                selectedKeys: [],
                filteredFileList: block.fileList || [],
              };
              return acc;
            },
            {},
          );

          setTabStates(initialStates);
          setActiveBlockNo(detailRes.data[0].blockNo);

          // 初始化配置项的展开状态（默认全部展开）
          const initialExpandedItems: Record<string, boolean> = {};
          detailRes.data.forEach((block: any) => {
            if (block.itemsTypeList) {
              block.itemsTypeList.forEach((itemType: any) => {
                if (itemType.itemList) {
                  itemType.itemList.forEach((item: any) => {
                    initialExpandedItems[item.itemNo] = true;
                  });
                }
              });
            }
          });
          setExpandedItems(initialExpandedItems);

          // 同步更新ref中的数据
          selectedDataRef.current = {
            ...selectedDataRef.current,
            tabStates: initialStates,
          };

          // 初始化inputValuesRef，使用新的数据结构
          const initialInputValues: Record<string, any> = {};
          detailRes.data.forEach((block: any) => {
            initialInputValues[block.blockNo] = {
              structureInfo: {},
              fileInfo: {}
            };
          });
          inputValuesRef.current = initialInputValues;
        }
      } else {
        message.error(detailRes.message || '获取配置详情失败');
      }
    } catch (error) {
      message.error('获取配置数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log(selectedConfig);
    if (selectedConfig.product?.value) {
      fetchConfigData();
      selectedDataRef.current = {
        ...selectedDataRef.current,
        selectedProduct: selectedConfig.product,
      };
    }
  }, [JSON.stringify(selectedConfig)]);

  // 渲染不同类型的配置项组件
  const RenderConfigItem: React.FC<{
    item: any;
    blockNo: string;
    onChange?: (value: any) => void;
  }> = ({ item, blockNo, onChange }) => {
    // 使用update变量触发重新渲染
    React.useEffect(() => {
      // 当update变化时会重新执行，触发组件重新渲染
    }, [update]);

    // 获取当前配置项的存储值
    const storedValue = getInputValue(blockNo, item.identifier, DataType.STRUCTURE);

    // 渲染数组类型的每一行
    const renderArrayRow = (index: number) => {
      const { childDataType, dataSpecs } = item.dataSpecs;
      // 获取数组元素的存储值
      const arrayValues =
        storedValue && Array.isArray(storedValue) ? storedValue : [];
      const rowValue = arrayValues[index];

      const renderInput = () => {
        switch (childDataType) {
          case 'TEXT':
            return (
              <Input
                required
                placeholder={`请输入${item.name || '内容'}`}
                value={rowValue}
                onChange={(e) => {
                  const newArrayValues = [...(arrayValues || [])];
                  newArrayValues[index] = e.target.value;
                  onChange?.(newArrayValues);
                }}
                allowClear
              />
            );
          case 'INT':
          case 'LONG':
          case 'DOUBLE':
            return (
              <InputNumber
                style={{ width: '100%' }}
                min={dataSpecs?.min}
                max={dataSpecs?.max}
                step={dataSpecs?.step}
                addonAfter={dataSpecs?.unitName}
                required
                placeholder={`请输入${item.name || '数值'}`}
                value={rowValue}
                onChange={(value) => {
                  const newArrayValues = [...(arrayValues || [])];
                  newArrayValues[index] = value;
                  onChange?.(newArrayValues);
                }}
                controls={false}
                keyboard={true}
              />
            );
        }
      };

      return (
        <div key={index} className="array-row">
          <div className="array-sequence">{index + 1}</div>
          <div className="array-input">{renderInput()}</div>
          <Button
            type="link"
            danger
            className="array-delete-btn"
            onClick={() => {
              // 直接更新存储的值
              const newArrayValues = arrayValues.filter((_, i) => i !== index);
              onChange?.(newArrayValues);
              // 触发重新渲染
              setUpdate((prev) => prev + 1);
            }}
          >
            删除
          </Button>
        </div>
      );
    };

    switch (item.dataType) {
      case 'INT':
      case 'LONG':
      case 'DOUBLE':
        return (
          <InputNumber
            style={{ width: '100%' }}
            min={item.dataSpecs?.min}
            max={item.dataSpecs?.max}
            step={item.dataSpecs?.step}
            addonAfter={item.dataSpecs?.unitName}
            onChange={onChange}
            required
            placeholder={`请输入${item.name || '数值'}`}
            value={storedValue}
            controls={false}
            keyboard={true}
          />
        );
      case 'ENUM':
      case 'BOOL':
      case 'INT_ENUM':
        return (
          <Select
            style={{ width: '100%' }}
            onChange={onChange}
            placeholder={`请选择${item.name || '选项'}`}
            value={storedValue}
          >
            {item.dataSpecsList?.map((spec: any) => (
              <Select.Option key={spec.value} value={spec.value}>
                {spec.name}
              </Select.Option>
            ))}
          </Select>
        );
      case 'TEXT':
        return (
          <Input
            maxLength={item.dataSpecs?.length}
            onChange={(e) => onChange?.(e.target.value)}
            required
            placeholder={`请输入${item.name || '内容'}`}
            value={storedValue}
            allowClear
          />
        );
      case 'DATE':
        return (
          <DatePicker
            style={{ width: '100%' }}
            onChange={(date) => onChange?.(date ? date.valueOf() : null)}
            placeholder={`请选择${item.name || '日期'}`}
            value={storedValue ? dayjs(storedValue) : undefined}
          />
        );
      case 'ARRAY':
        // 从存储的值中派生行索引
        const arrayValues =
          storedValue && Array.isArray(storedValue) ? storedValue : [null];
        const rowIndices = Array.from(
          { length: arrayValues.length },
          (_, i) => i,
        );

        return (
          <div className="array-container">
            {rowIndices.map((index) => renderArrayRow(index))}
            <div className="array-add-btn-container">
              <Button
                type="dashed"
                onClick={() => {
                  if (arrayValues.length >= item.dataSpecs.size) {
                    message.warning('已达到数组元素个数限制');
                    return;
                  }

                  // 直接更新存储的值
                  const newArrayValues = [...arrayValues];
                  newArrayValues.push(null); // 添加一个空值
                  onChange?.(newArrayValues);
                  // 触发重新渲染
                  setUpdate((prev) => prev + 1);
                }}
                className="array-add-btn"
              >
                + 添加元素
              </Button>
              {item.dataSpecs.size && (
                <span className="array-limit-hint">
                  ({arrayValues.length}/{item.dataSpecs.size})
                </span>
              )}
            </div>
          </div>
        );
    }
  };

  // 获取当前tab的状态
  const getCurrentTabState = () => {
    if (!activeBlockNo) return null;
    return tabStates[activeBlockNo] || null;
  };

  // 更新当前tab的状态
  const updateCurrentTabState = (updates: Partial<TabState>) => {
    if (activeBlockNo) {
      setTabStates((prev) => {
        const newTabStates = {
          ...prev,
          [activeBlockNo]: {
            ...prev[activeBlockNo],
            ...updates,
          },
        };

        selectedDataRef.current = {
          ...selectedDataRef.current,
          tabStates: newTabStates,
        };

        return newTabStates;
      });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '模块',
      dataIndex: 'blockName',
      key: 'blockName',
    },
    {
      title: '配置文件编码',
      dataIndex: 'fileNo',
      key: 'fileNo',
    },
    {
      title: '配置文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record.fileNo)}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  // 处理查看操作
  const handleEdit = async (fileNo: string) => {
    try {
      const res = await configTemplateApi.current.getConfigFile({
        fileNo: fileNo,
        productKey: selectedConfig.product.value,
      });

      if (res.code !== HttpStatusCode.Success) {
        message.error(res.message);
        return;
      }

      // 保存原始数据，用于后续比较是否有修改
      const originalData = { ...res.data };

      // 创建一个ref来存储表单实例
      const formRef = React.createRef<FormInstance>();

      showModal({
        title: '查看配置文件',
        content: <ViewConfigFileModal ref={formRef} data={res.data} />,
        width: '800px',
        footer: [
          {
            text: '保存',
            type: 'notCancelBtn',
            onClick: async (cb: () => void) => {
              try {
                // 获取表单实例并验证
                const form = formRef.current;
                if (!form) {
                  message.error('获取表单实例失败');
                  return;
                }

                await form.validateFields();

                const values = form.getFieldsValue();

                if (values.content === originalData.content) {
                  message.info('内容未修改');
                  cb(); // 关闭弹窗
                  return;
                }

                // 将编辑后的模板内容存储到inputValuesRef中
                const { blockNo, fileNo } = values;

                // 确保blockNo和fileNo存在
                if (blockNo && fileNo) {
                  // 使用handleInputChange函数将内容存储到inputValuesRef中
                  // 使用DataType.FILE表示这是文件内容，应该存储在fileInfo中
                  handleInputChange(blockNo, fileNo, values.content, DataType.FILE);

                  // 这里可以调用API保存修改后的内容
                  // 例如: await configTemplateApi.current.updateConfigFile(values);

                  // 显示成功消息
                  message.success('保存成功');

                  // 关闭弹窗
                  cb();

                  // 刷新数据
                  fetchConfigData();
                } else {
                  message.error('保存失败：缺少必要的参数');
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败，请检查表单');
              }
            },
          },
          {
            text: '取消',
            type: 'cancelBtn',
            onClick: (cb: () => void) => {
              cb();
            },
          },
        ],
      });
    } catch (error) {
      console.error('获取配置文件失败:', error);
      message.error('获取配置文件失败');
    }
  };

  // 获取当前模块
  const getCurrentBlock = () => {
    return configData.find((block) => block.blockNo === activeBlockNo);
  };

  // 定义数据类型枚举
  enum DataType {
    STRUCTURE = 'structureInfo',
    FILE = 'fileInfo'
  }

  // 处理输入值变化
  const handleInputChange = (
    blockNo: string,
    itemIdentifier: string,
    value: any,
    dataType: DataType = DataType.STRUCTURE
  ) => {
    // 直接修改ref的值，不触发重新渲染
    if (!inputValuesRef.current[blockNo]) {
      inputValuesRef.current[blockNo] = {
        structureInfo: {},
        fileInfo: {}
      };
    } else if (!inputValuesRef.current[blockNo].structureInfo) {
      // 确保structureInfo和fileInfo都存在
      inputValuesRef.current[blockNo] = {
        ...inputValuesRef.current[blockNo],
        structureInfo: {},
        fileInfo: {}
      };
    }

    // 根据dataType参数决定存储在structureInfo还是fileInfo中
    if (dataType === DataType.FILE) {
      // 存储在fileInfo中
      inputValuesRef.current[blockNo].fileInfo[itemIdentifier] = value;
    } else {
      // 存储在structureInfo中
      inputValuesRef.current[blockNo].structureInfo[itemIdentifier] = value;
    }
  };

  // 获取输入值
  const getInputValue = (
    blockNo: string,
    itemIdentifier: string,
    dataType: DataType = DataType.STRUCTURE
  ) => {
    if (!inputValuesRef.current[blockNo]) {
      return undefined;
    }

    // 根据dataType参数决定从structureInfo还是fileInfo中获取
    if (dataType === DataType.FILE) {
      // 从fileInfo中获取
      return inputValuesRef.current[blockNo].fileInfo?.[itemIdentifier];
    } else {
      // 从structureInfo中获取
      return inputValuesRef.current[blockNo].structureInfo?.[itemIdentifier];
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    const currentBlock = getCurrentBlock();
    if (!currentBlock) return;

    let filteredList = currentBlock.fileList || [];
    if (value && value.trim()) {
      const searchText = value.toLowerCase().trim();
      filteredList = filteredList.filter(
        (file: FileItem) =>
          (file.fileName && file.fileName.toLowerCase().includes(searchText)) ||
          (file.fileNo && file.fileNo.toLowerCase().includes(searchText)),
      );
    }

    // 只更新过滤后的文件列表
    updateCurrentTabState({
      filteredFileList: filteredList,
    });
  };

  // 渲染结构化配置项部分
  const renderStructuredConfig = (itemsTypeList: any[], blockNo: string) => {
    // 处理配置项的展开/收起
    const toggleItemExpand = (itemNo: string) => {
      setExpandedItems((prev) => ({
        ...prev,
        [itemNo]: !prev[itemNo],
      }));
    };

    return (
      <div className="structured-config">
        <Collapse defaultActiveKey={[itemsTypeList[0]?.itemTypeNo]} ghost>
          {itemsTypeList.map((itemType) => (
            <Panel
              key={itemType.itemTypeNo}
              header={
                <div className="type-header">
                  <h3>{itemType.itemTypeName}</h3>
                </div>
              }
            >
              <div className="config-items-list">
                {itemType.itemList.map((item: any) => {
                  const content =
                    typeof item.content === 'string'
                      ? JSON.parse(item.content)
                      : item.content;

                  if (!Array.isArray(content)) return null;

                  const isExpanded = expandedItems[item.itemNo] !== false; // 默认展开

                  return (
                    <div key={item.itemNo} className="config-item-section">
                      <div
                        className="item-header"
                        onClick={() => toggleItemExpand(item.itemNo)}
                        style={{ cursor: 'pointer' }}
                      >
                        <span className="item-name">{item.itemName}</span>
                        <span className="expand-icon">
                          {isExpanded ? (
                            <span className="icon-down">▼</span>
                          ) : (
                            <span className="icon-right">▶</span>
                          )}
                        </span>
                      </div>
                      {isExpanded && (
                        <div className="item-content">
                          {content.map((configItem: any) => (
                            <Form.Item
                              key={configItem.identifier}
                              name={[blockNo, configItem.identifier]}
                              label={
                                <div className="config-item-label">
                                  <span>{configItem.name}</span>
                                  {configItem.description && (
                                    <Tooltip title={configItem.description}>
                                      <span className="help-icon">?</span>
                                    </Tooltip>
                                  )}
                                </div>
                              }
                              required={true}
                              rules={[
                                {
                                  required: true,
                                  message: `请输入${configItem.name}`,
                                },
                              ]}
                            >
                              <div className="config-item-content">
                                <RenderConfigItem
                                  item={{
                                    ...configItem,
                                    required: true,
                                  }}
                                  blockNo={blockNo}
                                  onChange={(value) => {
                                    // 同时更新inputValuesRef和Form的值
                                    // 使用DataType.STRUCTURE表示这是结构化配置项，应该存储在structureInfo中
                                    handleInputChange(
                                      blockNo,
                                      configItem.identifier,
                                      value,
                                      DataType.STRUCTURE
                                    );

                                    // 更新Form中对应字段的值
                                    const form =
                                      formInstancesRef.current[blockNo];
                                    if (form) {
                                      const fieldValues = form.getFieldsValue();
                                      form.setFieldsValue({
                                        ...fieldValues,
                                        [blockNo]: {
                                          ...(fieldValues[blockNo] || {}),
                                          [configItem.identifier]: value,
                                        },
                                      });
                                    }
                                  }}
                                />
                              </div>
                            </Form.Item>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </Panel>
          ))}
        </Collapse>
      </div>
    );
  };

  // 渲染文件部分
  const renderFileSection = () => {
    const currentTabState = getCurrentTabState();
    if (!currentTabState) return null;

    const { filteredFileList } = currentTabState;

    return (
      <div className="file-section">
        <div className="section-header">文件类配置项</div>
        <div className="search-bar">
          <Input.Search
            placeholder="请输入配置文件名或描述进行搜索"
            onSearch={handleSearch}
            allowClear
            enterButton="查询"
          />
        </div>
        <Table
          columns={columns}
          dataSource={filteredFileList}
          rowKey="fileNo"
          pagination={false}
          loading={loading}
        />
      </div>
    );
  };

  return (
    <ConfigProvider>
      <div className="configuration-page">
        <div className="search-item">
          <span className="label">产品</span>
          <Select
            options={[selectedConfig.product]}
            value={selectedConfig.product}
            placeholder="请选择产品"
            disabled
            style={{ width: 200 }}
          />
        </div>

        {configData.length > 0 && (
          <>
            <Tabs
              activeKey={activeBlockNo}
              onChange={setActiveBlockNo}
              items={configData.map((block) => ({
                key: block.blockNo,
                label: block.blockName,
                children: (
                  <div className="content-container">
                    <Form
                      ref={(formInstance) => {
                        if (
                          formInstance &&
                          !formInstancesRef.current[block.blockNo]
                        ) {
                          formInstancesRef.current[block.blockNo] =
                            formInstance;
                        }
                      }}
                      initialValues={{
                        [block.blockNo]:
                          inputValuesRef.current[block.blockNo]?.structureInfo || {},
                      }}
                    >
                      {renderStructuredConfig(
                        block.itemsTypeList || [],
                        block.blockNo,
                      )}
                    </Form>
                    {renderFileSection()}
                  </div>
                ),
              }))}
            />
          </>
        )}
      </div>
    </ConfigProvider>
  );
});

export default Configuration;
