import { FormConfig } from '@/components/CommonForm/formConfig';
import { DataType } from './constant';
export const ProductTableData: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '产品名称',
    width: 190,
    dataIndex: 'productName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品标识',
    width: 150,
    dataIndex: 'productKey',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    width: 90,
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 170,
    fixed: 'right',
  },
];

export const SearchForm: FormConfig = {
  fields: [
    {
      type: 'ReactNode',
      label: '产品标签',
      fieldName: 'tagList',
      placeholder: '请选择产品标签',
      multiple: true,
      labelCol:{span:6},
      wrapperCol: {span:18},
      xl:8,
      xxl:8,
    },
    {
      type: 'select',
      label: '产品名称',
      fieldName: 'productKey',
      showSearch: true,
      placeholder: '请输入产品名称',
      labelCol:{span:6},
      wrapperCol: {span:18},
      xl:8,
      xxl:8,
    },
  ],
};

export const ModelTableData: any[] = [
  { title: '功能类型', dataIndex: 'typeName', align: 'center', width: 70 },
  {
    title: '功能名称(全部)',
    width: 100,
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标识符',
    width: 100,
    dataIndex: 'identifier',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '数据类型',
    width: 90,
    dataIndex: 'dataType',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 100,
    fixed: 'right',
  },
];

export const DataTypeOptions: any[] = [
  {
    label: `${DataType.INT}(整数型)`,
    value: DataType.INT,
  },
  {
    label: `${DataType.LONG}(长整数型)`,
    value: DataType.LONG,
  },
  {
    label: `${DataType.DOUBLE}(双精度浮点型)`,
    value: DataType.DOUBLE,
  },
  {
    label: `${DataType.TEXT}(字符串)`,
    value: DataType.TEXT,
  },
  {
    label: `${DataType.DATE}(日期)`,
    value: DataType.DATE,
  },
  {
    label: `${DataType.ENUM}(字符串枚举型)`,
    value: DataType.ENUM,
  },
  {
    label: `${DataType.INT_ENUM}(整数枚举型)`,
    value: DataType.INT_ENUM,
  },
  {
    label: `${DataType.BOOL}(布尔型)`,
    value: DataType.BOOL,
  },
  {
    label: `${DataType.ARRAY}(数组)`,
    value: DataType.ARRAY,
  },
  {
    label: `${DataType.STRUCT}(结构体)`,
    value: DataType.STRUCT,
  },
];

export const ArrayDataTypeOptions: any[] = [
  {
    label: `${DataType.INT}32`,
    value: DataType.INT,
  },
  {
    label: `${DataType.LONG}`,
    value: DataType.LONG,
  },
  {
    label: `${DataType.DOUBLE}`,
    value: DataType.DOUBLE,
  },
  {
    label: `${DataType.TEXT}`,
    value: DataType.TEXT,
  },
  {
    label: `${DataType.STRUCT}`,
    value: DataType.STRUCT,
  },
];

export const rwFlagOptions: any[] = [
  {
    label: '读写',
    value: 'READ_WRITE',
  },
  {
    label: '只读',
    value: 'READ_ONLY',
  },
];

export const dataProtocolOptions: any[] = [
  {
    label: 'JSON',
    value: 'JSON',
  },
  {
    label: 'PB',
    value: 'PB',
  },
];

export const PublishFormConfig: FormConfig = {
  fields: [
    {
      type: 'textarea',
      label: '版本号',
      fieldName: 'version',
      placeholder: '请输入版本号',
      maxLength: 16,
      showCount: true,
      validatorRules: [
        { required: true, message: '请输入版本号' },
        {
          pattern: /^[A-Za-z0-9.]*$/g,
          message: '版本号支持英文字母、数字和小数点，长度限制 1～16 个字符',
        },
      ],
    },
    {
      type: 'textarea',
      label: '备注',
      fieldName: 'versionDescription',
      placeholder: '请输入描述',
      showCount: true,
      maxLength: 200,
    },
  ],
};

export const EditBlockFormConfig: FormConfig = {
  fields: [
    {
      type: 'input',
      label: '模块名称',
      fieldName: 'blockName',
      placeholder: '请输入模块名称',
      maxLength: 30,
      validatorRules: [
        { required: true, message: '请输入模块名称' },
        {
          pattern:
            /^[A-Za-z0-9\u4e00-\u9fa5\u3040-\u309f\u30a0-\u30ff_@()-]+$/g,
          message: '请输入格式正确的内容',
        },
      ],
    },
    {
      type: 'input',
      label: '模块标识',
      fieldName: 'blockNo',
      disabled: true,
      validatorRules: [
        { required: true, message: '请输入模块标识符' },
        {
          pattern: /^[a-zA-Z0-9_]+$/,
          message: '支持英文大小写字母、数字和下划线，不超过 30 个字符。',
        },
      ],
    },
    {
      type: 'radioGroup',
      label: '数据协议',
      fieldName: 'dataProtocol',
      options: dataProtocolOptions,
      validatorRules: [{ required: true, message: '请选择数据协议' }],
    },
    {
      type: 'textarea',
      label: '模块描述',
      fieldName: 'description',
      placeholder: '请输入模块描述',
      showCount: true,
      maxLength: 400,
    },
  ],
};

export const AddBlockFormConfig: FormConfig = {
  fields: [
    {
      type: 'input',
      label: '模块名称',
      fieldName: 'blockName',
      placeholder: '请输入模块名称',
      maxLength: 30,
      validatorRules: [
        { required: true, message: '请输入模块名称' },
        {
          pattern:
            /^[A-Za-z0-9\u4e00-\u9fa5\u3040-\u309f\u30a0-\u30ff_@()-]+$/g,
          message: '请输入格式正确的内容',
        },
      ],
    },
    {
      type: 'input',
      label: '模块标识',
      fieldName: 'blockNo',
      placeholder: '请输入模块标识',
      validatorRules: [
        { required: true, message: '请输入模块标识符' },
        {
          pattern: /^[a-zA-Z0-9_]+$/,
          message: '支持英文大小写字母、数字和下划线，不超过 30 个字符。',
        },
      ],
    },
    {
      type: 'radioGroup',
      label: '数据协议',
      fieldName: 'dataProtocol',
      options: dataProtocolOptions,
      validatorRules: [{ required: true, message: '请选择数据协议' }],
    },
    {
      type: 'textarea',
      label: '模块描述',
      fieldName: 'description',
      placeholder: '请输入模块描述',
      showCount: true,
      maxLength: 400,
    },
  ],
};

export const unitOptions = [
  {
    value: 'L/min',
    label: '升每分钟',
  },
  {
    value: 'mg/kg',
    label: '毫克每千克',
  },
  {
    value: 'NTU',
    label: '浊度',
  },
  {
    value: 'pH',
    label: 'PH值',
  },
  {
    value: 'dS/m',
    label: '土壤EC值',
  },
  {
    value: 'W/㎡',
    label: '太阳总辐射',
  },
  {
    value: 'mm/hour',
    label: '降雨量',
  },
  {
    value: 'var',
    label: '乏',
  },
  {
    value: 'cP',
    label: '厘泊',
  },
  {
    value: 'aw',
    label: '饱和度',
  },
  {
    value: 'pcs',
    label: '个',
  },
  {
    value: 'cst',
    label: '厘斯',
  },
  {
    value: 'bar',
    label: '巴',
  },
  {
    value: 'ppt',
    label: '纳克每升',
  },
  {
    value: 'ppb',
    label: '微克每升',
  },
  {
    value: 'uS/cm',
    label: '微西每厘米',
  },
  {
    value: 'N/C',
    label: '牛顿每库仑',
  },
  {
    value: 'V/m',
    label: '伏特每米',
  },
  {
    value: 'ml/min',
    label: '滴速',
  },
  {
    value: 'mmHg',
    label: '毫米汞柱',
  },
  {
    value: 'mmol/L',
    label: '血糖',
  },
  {
    value: 'mm/s',
    label: '毫米每秒',
  },
  {
    value: 'turn/m',
    label: '转每分钟',
  },
  {
    value: 'count',
    label: '次',
  },
  {
    value: 'gear',
    label: '档',
  },
  {
    value: 'stepCount',
    label: '步',
  },
  {
    value: 'Nm3/h',
    label: '标准立方米每小时',
  },
  {
    value: 'kV',
    label: '千伏',
  },
  {
    value: 'kVA',
    label: '千伏安',
  },
  {
    value: 'kVar',
    label: '千乏',
  },
  {
    value: 'uw/cm2',
    label: '微瓦每平方厘米',
  },
  {
    value: '只',
    label: '只',
  },
  {
    value: '%RH',
    label: '相对湿度',
  },
  {
    value: 'm³/s',
    label: '立方米每秒',
  },
  {
    value: 'kg/s',
    label: '公斤每秒',
  },
  {
    value: 'r/min',
    label: '转每分钟',
  },
  {
    value: 't/h',
    label: '吨每小时',
  },
  {
    value: 'KCL/h',
    label: '千卡每小时',
  },
  {
    value: 'L/s',
    label: '升每秒',
  },
  {
    value: 'Mpa',
    label: '兆帕',
  },
  {
    value: 'm³/h',
    label: '立方米每小时',
  },
  {
    value: 'kvarh',
    label: '千乏时',
  },
  {
    value: 'μg/L',
    label: '微克每升',
  },
  {
    value: 'kcal',
    label: '千卡路里',
  },
  {
    value: 'GB',
    label: '吉字节',
  },
  {
    value: 'MB',
    label: '兆字节',
  },
  {
    value: 'KB',
    label: '千字节',
  },
  {
    value: 'B',
    label: '字节',
  },
  {
    value: 'μg/(d㎡·d)',
    label: '微克每平方分米每天',
  },
  {
    value: '',
    label: '无',
  },
  {
    value: 'ppm',
    label: '百万分率',
  },
  {
    value: 'pixel',
    label: '像素',
  },
  {
    value: 'Lux',
    label: '照度',
  },
  {
    value: 'grav',
    label: '重力加速度',
  },
  {
    value: 'dB',
    label: '分贝',
  },
  {
    value: '%',
    label: '百分比',
  },
  {
    value: 'lm',
    label: '流明',
  },
  {
    value: 'bit',
    label: '比特',
  },
  {
    value: 'g/mL',
    label: '克每毫升',
  },
  {
    value: 'g/L',
    label: '克每升',
  },
  {
    value: 'mg/L',
    label: '毫克每升',
  },
  {
    value: 'μg/m³',
    label: '微克每立方米',
  },
  {
    value: 'mg/m³',
    label: '毫克每立方米',
  },
  {
    value: 'g/m³',
    label: '克每立方米',
  },
  {
    value: 'kg/m³',
    label: '千克每立方米',
  },
  {
    value: 'nF',
    label: '纳法',
  },
  {
    value: 'pF',
    label: '皮法',
  },
  {
    value: 'μF',
    label: '微法',
  },
  {
    value: 'F',
    label: '法拉',
  },
  {
    value: 'Ω',
    label: '欧姆',
  },
  {
    value: 'μA',
    label: '微安',
  },
  {
    value: 'mA',
    label: '毫安',
  },
  {
    value: 'kA',
    label: '千安',
  },
  {
    value: 'A',
    label: '安培',
  },
  {
    value: 'mV',
    label: '毫伏',
  },
  {
    value: 'V',
    label: '伏特',
  },
  {
    value: 'ms',
    label: '毫秒',
  },
  {
    value: 's',
    label: '秒',
  },
  {
    value: 'min',
    label: '分钟',
  },
  {
    value: 'h',
    label: '小时',
  },
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'week',
    label: '周',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'year',
    label: '年',
  },
  {
    value: 'kn',
    label: '节',
  },
  {
    value: 'km/h',
    label: '千米每小时',
  },
  {
    value: 'm/s',
    label: '米每秒',
  },
  {
    value: '″',
    label: '秒',
  },
  {
    value: '′',
    label: '分',
  },
  {
    value: '°',
    label: '度',
  },
  {
    value: 'rad',
    label: '弧度',
  },
  {
    value: 'Hz',
    label: '赫兹',
  },
  {
    value: 'μW',
    label: '微瓦',
  },
  {
    value: 'mW',
    label: '毫瓦',
  },
  {
    value: 'kW',
    label: '千瓦特',
  },
  {
    value: 'W',
    label: '瓦特',
  },
  {
    value: 'cal',
    label: '卡路里',
  },
  {
    value: 'kW·h',
    label: '千瓦时',
  },
  {
    value: 'Wh',
    label: '瓦时',
  },
  {
    value: 'eV',
    label: '电子伏',
  },
  {
    value: 'kJ',
    label: '千焦',
  },
  {
    value: 'J',
    label: '焦耳',
  },
  {
    value: '℉',
    label: '华氏度',
  },
  {
    value: 'K',
    label: '开尔文',
  },
  {
    value: 't',
    label: '吨',
  },
  {
    value: '°C',
    label: '摄氏度',
  },
  {
    value: 'mPa',
    label: '毫帕',
  },
  {
    value: 'hPa',
    label: '百帕',
  },
  {
    value: 'kPa',
    label: '千帕',
  },
  {
    value: 'Pa',
    label: '帕斯卡',
  },
  {
    value: 'mg',
    label: '毫克',
  },
  {
    value: 'g',
    label: '克',
  },
  {
    value: 'kg',
    label: '千克',
  },
  {
    value: 'N',
    label: '牛',
  },
  {
    value: 'mL',
    label: '毫升',
  },
  {
    value: 'L',
    label: '升',
  },
  {
    value: 'mm³',
    label: '立方毫米',
  },
  {
    value: 'cm³',
    label: '立方厘米',
  },
  {
    value: 'km³',
    label: '立方千米',
  },
  {
    value: 'm³',
    label: '立方米',
  },
  {
    value: 'h㎡',
    label: '公顷',
  },
  {
    value: 'c㎡',
    label: '平方厘米',
  },
  {
    value: 'm㎡',
    label: '平方毫米',
  },
  {
    value: 'k㎡',
    label: '平方千米',
  },
  {
    value: '㎡',
    label: '平方米',
  },
  {
    value: 'nm',
    label: '纳米',
  },
  {
    value: 'μm',
    label: '微米',
  },
  {
    value: 'mm',
    label: '毫米',
  },
  {
    value: 'cm',
    label: '厘米',
  },
  {
    value: 'dm',
    label: '分米',
  },
  {
    value: 'km',
    label: '千米',
  },
  {
    value: 'm',
    label: '米',
  },
];
