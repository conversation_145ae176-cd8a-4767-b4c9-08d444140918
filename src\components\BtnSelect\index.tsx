import React, { useState } from 'react';
import { Button } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import './index.scss';

const BtnSelect = ({
  btnText,
  options,
  handleSelect,
}: {
  btnText: string;
  options: { label: string; value: string }[];
  handleSelect: AnyFunc;
}) => {
  const [open, setOpen] = useState(false);

  const clickOption = (val: string) => {
    handleSelect(val);
    setOpen(false);
  };

  return (
    <div className="btn-select">
      <Button onClick={() => setOpen(!open)}>
        <span className="btn-text">{btnText}</span>
        {open ? <UpOutlined /> : <DownOutlined />}
      </Button>
      {open && (
        <ul className="select-options">
          {options.map((v: { label: string; value: string }) => {
            return (
              <li key={v.value} onClick={() => clickOption(v.value)}>
                {v.label}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default React.memo(BtnSelect);
