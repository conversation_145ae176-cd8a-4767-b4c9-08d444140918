import {
  <PERSON><PERSON>,
  Row,
  Col,
  Select,
  Flex,
  Table,
  Modal,
  message,
  Popconfirm,
} from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import './index.scss';
import { useNavigate, useLocation } from 'react-router-dom';
import TSLModelFetch from '@/fetch/bussiness/TSLModel';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ModelTableData } from '../../utils/column';
import { TSLFunctionType } from '@/utils/constant';

const TSLPart = ({
  type,
  productKey,
  selectBlock,
  version,
  backUrl,
}: {
  type: 'check' | 'edit';
  productKey: string;
  selectBlock: any;
  version: string;
  backUrl: string;
}) => {
  const fetchApi = new TSLModelFetch();
  const navigator = useNavigate();
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (selectBlock.value) {
      getModelListData(selectBlock.value);
    }
  }, [productKey, selectBlock]);

  const getModelListData = async (blockNo: string) => {
    const res = await fetchApi.getThingModel({
      productKey,
      blockNo,
      version,
    });
    if (res.code === HttpStatusCode.Success) {
      const info = JSON.parse(res.data.content);
      info.properties &&
        info.properties.forEach((v: any) => {
          v.type = TSLFunctionType.Properties;
          v.typeName = '属性';
        });
      info.services &&
        info.services.forEach((v: any) => {
          v.type = TSLFunctionType.Server;
          v.typeName = '服务';
        });
      info.events &&
        info.events.forEach((v: any) => {
          v.type = TSLFunctionType.Event;
          v.typeName = '事件';
        });
      const TableData = info.properties
        .concat(info.services ?? [])
        .concat(info.events ?? [])
        .sort((a: any, b: any) => b.createTs - a.createTs);
      setTableData(TableData);
    }
  };

  const addProperty = () => {
    navigator(
      `/product/EditAddTSL?type=add&productKey=${productKey}&blockName=${selectBlock.label}&blockNo=${selectBlock.value}&backUrl=${backUrl}`,
    );
  };

  const formatColumns = () => {
    return ModelTableData?.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                {type === 'check' && (
                  <a
                    onClick={() => {
                      navigator(
                        `/product/EditAddTSL?type=check&productKey=${productKey}&blockNo=${selectBlock.value}&identifier=${record.identifier}&blockName=${selectBlock.label}&backUrl=${backUrl}&version=${version}&funcType=${record.type}`,
                      );
                    }}
                  >
                    查看
                  </a>
                )}
                {type === 'edit' && (
                  <a
                    onClick={() => {
                      navigator(
                        `/product/EditAddTSL?type=edit&productKey=${productKey}&blockNo=${selectBlock.value}&identifier=${record.identifier}&blockName=${selectBlock.label}&backUrl=${backUrl}&funcType=${record.type}`,
                      );
                    }}
                  >
                    编辑
                  </a>
                )}
                {type === 'edit' && (
                  <Popconfirm
                    title="是否确认删除！"
                    onConfirm={() => handleDelProterty(record)}
                  >
                    <a>删除</a>
                  </Popconfirm>
                )}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const handleDelProterty = async (record: any) => {
    const params =
      record.type === TSLFunctionType.Server
        ? {
            productKey: productKey,
            blockNo: selectBlock.value,
            serviceIdentifiers: [record.identifier],
          }
        : {
            productKey: productKey,
            blockNo: selectBlock.value,
            propertyIdentifiers: [record.identifier],
          };
    const res = await fetchApi.delBlockOrTSL(params);
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      getModelListData(selectBlock.value);
    } else {
      message.error(res.message);
    }
  };

  return (
    <div className="right">
      <div className="title">{selectBlock.label}</div>
      {type === 'edit' && (
        <Button className="add-btn" type="primary" onClick={addProperty}>
          添加功能
        </Button>
      )}
      <Table
        columns={formatColumns()}
        key={'identifier'}
        dataSource={tableData}
        pagination={false}
        scroll={{ y: 400 }}
      />
    </div>
  );
};

export default React.memo(TSLPart);
