import { CommonForm } from '@jd/x-coreui';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  ActivePush,
  AfterUpgradeLabel,
  AfterUpgradeOptions,
  BatchUpgrade,
  ExecuteType,
  Priority,
  PushType,
  UpgradeRulesForm,
  UpgradeType,
} from '../../utils/constant';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
const defaultValue = {
  packagePriority: Priority.DIFF,
  activePush: ActivePush.YES,
  executeType: ExecuteType.REMIND,
  downloadType: ExecuteType.REMIND,
  upgradeType: UpgradeType.REMIND,
  immediately: PushType.IMMEDIATELEY,
  batch: BatchUpgrade.NO,
  afterUpgrade: AfterUpgradeOptions.NONE,
};
const UpgradeRules = forwardRef(
  (
    props: {
      appType: string;
    },
    ref: any,
  ) => {
    const [formData, setFormData] = useState<FormConfig>(UpgradeRulesForm);
    const formRef = useRef<any>(null);
    const validateFormValues = () => {
      return formRef.current.validateFields();
    };
    const getFormValues = () => {
      return formRef.current.getFieldsValue();
    };
    useImperativeHandle(ref, () => {
      return {
        validateFormValues,
        getFormValues,
      };
    });

    useEffect(() => {
      // 固件
      const field = formData.fields.find(
        (field: FieldItem) => field.fieldName === 'afterUpgrade',
      );
      if (props.appType === 'firmware') {
        field!.options = [
          {
            value: AfterUpgradeOptions.NONE,
            label: AfterUpgradeLabel[AfterUpgradeOptions.NONE],
          },
          {
            value: AfterUpgradeOptions.RESET,
            label: AfterUpgradeLabel[AfterUpgradeOptions.RESET],
          },
          {
            value: AfterUpgradeOptions.CLEAR_APP_DATA,
            label: AfterUpgradeLabel[AfterUpgradeOptions.CLEAR_APP_DATA],
          },
          {
            value: AfterUpgradeOptions.UNINSTALL,
            label: AfterUpgradeLabel[AfterUpgradeOptions.UNINSTALL],
          },
        ];
      } else if (props.appType === 'app') {
        // 应用
        field!.options = [
          {
            value: AfterUpgradeOptions.NONE,
            label: AfterUpgradeLabel[AfterUpgradeOptions.NONE],
          },
          {
            value: AfterUpgradeOptions.AUTO_START,
            label: AfterUpgradeLabel[AfterUpgradeOptions.AUTO_START],
          },
          {
            value: AfterUpgradeOptions.REBOOT,
            label: AfterUpgradeLabel[AfterUpgradeOptions.REBOOT],
          },
        ];
      }

      setFormData({ ...formData });
    }, [props.appType]);
    return (
      <CommonForm
        defaultValue={defaultValue}
        layout="inline"
        getFormInstance={(ref: any) => {
          formRef.current = ref;
        }}
        formConfig={formData}
      />
    );
  },
);

export default UpgradeRules;
