import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Radio,
  Checkbox,
  Row,
  Col,
  Modal,
  message,
  FormInstance,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import BreadCrumb from '@/components/BreadCrumb';
import { formatLocation } from '@/utils/formatLocation';
import FormTitle from '@/components/FormTitle';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';
import CommonForm from '@/components/CommonForm';
import { formConfig } from '../utils/constant';
import ConfigManagementRequest from '@/fetch/bussiness/configManagement';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import { cloneDeep } from 'lodash';
import { ProductType } from '@/utils/constant';
import { CommonApi } from '@/fetch/bussiness/commonFetch';
const configApi = new ConfigManagementRequest();
const commonApi = new CommonApi();
const BreadCrumbItemsMap = new Map([
  [
    'edit',
    [
      { title: 'OTA管理', route: '' },
      { title: '配置模板管理', route: '' },
      { title: '编辑配置模板', route: '' },
    ],
  ],
  [
    'add',
    [
      { title: 'OTA管理', route: '' },
      { title: '配置模板管理', route: '' },
      { title: '新建配置模板', route: '' },
    ],
  ],
]);
const FormTitleMap = new Map([
  ['edit', '编辑配置模板'],
  ['add', '新建配置模板'],
]);
const ConfigEditAndAdd = () => {
  const navigator = useNavigate();
  const formInstance = useRef<any>(null);
  const urlData = formatLocation(window.location.search);
  const [editFormConfig, setEditFormConfig] = useState<FormConfig>(
    cloneDeep(formConfig),
  );
  const type: string = urlData.type;
  const [bothModifyModal, setBothModifyModal] = useState<boolean>(false);
  const [bothModify, setBothModify] = useState<number | undefined>();
  useEffect(() => {
    commonApi
      .getCommonDropDown({
        keyList: ['productTypeList'],
      })
      .then((res: any) => {
        if (res.code === HttpStatusCode.Success) {
          const productType = editFormConfig?.fields?.find(
            (field: FieldItem) => field.fieldName === 'productType',
          );
          productType!.options = res?.data?.productTypeList?.map(
            (item: { code: string; name: string }) => ({
              label: item.name,
              value: item.code,
            }),
          );
          setEditFormConfig({
            ...editFormConfig,
          });
        }
      })
      .catch((e) => {});
  }, []);
  useEffect(() => {
    if (type === 'edit') {
      fetchDetail();
      const productType = editFormConfig?.fields?.find(
        (field: FieldItem) => field.fieldName === 'productType',
      );
      productType!.disabled = true;
      setEditFormConfig({
        ...editFormConfig,
      });
    } else if (type === 'add') {
      formInstance.current.setFieldsValue({
        productType: ProductType.VEHICLE,
        number: '系统生成',
        enable: 1,
      });
    }
  }, [type, urlData.number]);

  const fetchDetail = () => {
    configApi
      .getConfTemplateDetail({
        number: urlData.number,
      })
      .then((res) => {
        if (res && res.code === HttpStatusCode.Success) {
          const position = editFormConfig?.fields?.find(
            (field: FieldItem) => field.fieldName === 'position',
          );
          const tempName = editFormConfig?.fields?.find(
            (field: FieldItem) => field.fieldName === 'name',
          );
          position!.disabled = true;
          tempName!.disabled = true;
          if (res?.data?.productType === ProductType.ROBOT) {
            position!.options = [
              {
                label: res?.data?.position,
                value: res?.data?.position,
              },
            ];
          }
          setEditFormConfig({
            ...editFormConfig,
          });
          formInstance.current.setFieldsValue(res.data);
        }
      });
  };
  const onConfirmClick = () => {
    formInstance.current
      .validateFields()
      .then((values: any) => {
        let reg = /^[^\u4e00-\u9fa5]+$/g; // 不可以输入中文
        let regEmpty = /^\s*$/g; // 不可以输入空格
        if (!reg.test(values.name)) {
          message.error('模板名称中存在汉字，请修改后提交！');
        } else if (regEmpty.test(values.name)) {
          message.error('模板名称中存在空格，请修改后提交！');
        } else if (regEmpty.test(values.content)) {
          message.error('模板内容不能为空！');
        } else {
          if (type === 'add') {
            addConf(values);
          } else if (type === 'edit') {
            isExistSame(values);
          }
        }
      })
      .catch((e) => {});
  };
  const addConf = (data: any) => {
    configApi
      .addConfTemplate({
        name: data.name,
        description: data.description,
        positionList: data.position,
        content: data.content,
        enable: data.enable,
        productType: data.productType,
      })
      .then((res) => {
        if (res && res.code === HttpStatusCode.Success) {
          message.success(res.message);
          navigator('/configManagement');
        }
      })
      .catch((e) => {});
  };
  const isExistSame = (data: any) => {
    request({
      method: 'GET',
      path: api.isExistSameNameConfTemplate,
      urlParams: {
        number: data.number,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success && res.data === 1) {
          setBothModifyModal(true);
        } else {
          saveEditConf(data, 0);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const saveEditConf = (data: any, bothModify: any) => {
    request({
      method: 'PUT',
      path: api.editConfTemplate,
      body: {
        number: data.number,
        name: data.name,
        description: data.description,
        position: data.position,
        content: data.content,
        enable: data.enable,
        productType: data.productType,
        bothModify: bothModify,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          message.success(res.message);
          navigator('/configManagement');
        }
      })
      .catch((e: any) => {
        if (e.message) {
          message.error(e.message);
        }
      });
  };
  const onConfirmModal = () => {
    if (bothModify === 0 || bothModify === 1) {
      saveEditConf(formInstance.current.getFieldsValue(), bothModify);
      setBothModifyModal(false);
      setBothModify(undefined);
    }
  };
  return (
    <div className="edit-content">
      <BreadCrumb items={BreadCrumbItemsMap.get(type)} />
      <div className="config-edit">
        <div className="edit-container">
          <FormTitle title={FormTitleMap.get(type)} />
          <CommonForm
            className="config-edit-form"
            formConfig={editFormConfig}
            layout={'vertical'}
            getFormInstance={(formRef: FormInstance) => {
              formInstance.current = formRef;
            }}
          />
        </div>
        <div className="submit">
          <CustomButton onSubmitClick={onConfirmClick} title={'确定'} />
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            otherCSSProperties={{ marginLeft: '20px' }}
            onSubmitClick={() => navigator('/configManagement')}
            title={'取消'}
          />
        </div>
      </div>
      {bothModifyModal && (
        <Modal
          bodyStyle={{ textAlign: 'center' }}
          title="提示"
          visible={bothModifyModal}
          onCancel={() => {
            setBothModifyModal(false);
          }}
          footer={
            <div>
              <CustomButton
                onSubmitClick={() => onConfirmModal()}
                title={'确定'}
              />
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  setBothModifyModal(false);
                  setBothModify(undefined);
                }}
                title={'取消'}
              />
            </div>
          }
        >
          <p>{'control和compute存在一样的文件名，请确认是否同时修改？'}</p>
          <Radio.Group
            onChange={(e) => setBothModify(e.target.value)}
            value={bothModify}
          >
            <Radio value={0}>{'不需要同时修改'}</Radio>
            <Radio value={1}>{'需要同时修改'}</Radio>
          </Radio.Group>
        </Modal>
      )}
    </div>
  );
};
export default React.memo(ConfigEditAndAdd);
