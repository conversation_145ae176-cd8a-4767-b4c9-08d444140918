@import '../../assets/css/main.scss';

.config-manage {

  .searchform {
    padding: 10px;
    background-color: white;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .ant-form-item-control-input-content {
      display: flex;
    }

    .ant-form-item {
      margin-bottom: 10px;
    }
  }

  .table-container {
    min-height: 120px;
    height: 100%;
    margin-top: 16px;
    padding: 16px;
    background-color: white;

    .operate-btn {
      text-align: center;
      display: flex;
      justify-content: center;

      a {
        display: block;
        margin-right: 10px;
        margin-left: 10px;
      }

      .active {
        color: #1ABC9C;
      }
    }
  }
}