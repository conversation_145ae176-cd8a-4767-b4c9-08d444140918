import { TSLFunctionType } from '@/utils/constant';

interface Data {
  pageType: 'add' | 'edit' | 'check';
  tslInfo: any[];
  formInstanceList: any[];
  maxLevel: number;
  curEditLevel: number;
  functionType: TSLFunctionType;
}

export default class TSLContext {
  private initData: Data = {
    pageType: 'add', // 页面类型  add | edit | check
    tslInfo: [], //  物模型信息
    formInstanceList: [],
    maxLevel: 1,
    curEditLevel: 0,
    functionType: TSLFunctionType.Properties,
  };

  saveTSLInfo(data: any) {
    this.initData = {
      ...this.initData,
      ...data,
    };
  }

  changeCurEditLevel(l: number) {
    this.initData = {
      ...this.initData,
      curEditLevel: l,
    };
  }

  getTSLInfo<K extends keyof Data>(key: K): Data[K] {
    return this.initData[key];
  }

  changeEditLevel() {}
}
