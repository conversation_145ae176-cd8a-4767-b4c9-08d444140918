import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  TableConfig,
  AppTableConfig,
  SearchConfig,
  CreateFirmwareFormConfig,
  CreateAppFormConfig,
} from './utils/column';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';
import { HttpStatusCode } from '@/fetch/core/constant';
import { FirmwareFetch, Device } from '@/fetch/bussiness';
import { RootState } from '@/redux/store';
import CommonConfirmBtn from '@/components/CommonConfirmBtn';
import { FormConfig } from '@/components/CommonForm/formConfig';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import showModal from '@/components/commonModal';
import './index.scss';

const Firmware = ({ pageType }: { pageType: 'firmware' | 'app' }) => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const fetchApi = new FirmwareFetch();
  const deviceApi = new Device();
  const historySearchValue = useSelector(
    (state: RootState) => state.searchform,
  );
  const initSearchCondition = useRef<any>({
    searchForm: {
      productKey: null,
      type: null,
    },
    pageNum: 1,
    pageSize: 10,
  });
  const defaultSearchVals = useRef<any>(
    historySearchValue?.searchValues
      ? historySearchValue?.searchValues
      : initSearchCondition.current,
  );
  const [searchCondition, setSearchCondition] = useState<any>(
    defaultSearchVals.current,
  );
  const typeRef = useRef(location.pathname.split('/').pop());
  const [formatSearchConfig, setFormatSearchConfig] =
    useState<FormConfig>(SearchConfig);
  const createAppFormConfig = useRef<FormConfig>(CreateFirmwareFormConfig);
  const [autoFetch, setAutoFetch] = useState<boolean>(false);
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    fetchApi.getTableList,
    typeRef.current,
    autoFetch,
  );

  useEffect(() => {
    typeRef.current = pageType;
    createAppFormConfig.current =
      typeRef.current === 'firmware'
        ? CreateFirmwareFormConfig
        : CreateAppFormConfig;

    formatSearch();
  }, [pageType]);

  const formatSearch = async () => {
    const res = await deviceApi.queryProductList();
    let productOptions: any[] = [];
    if (res.code === HttpStatusCode.Success) {
      productOptions = res.data?.map((v: any) => ({
        label: v.productName,
        value: v.productKey,
      }));
      initSearchCondition.current = {
        ...initSearchCondition.current,
        searchForm: {
          ...initSearchCondition.current.searchForm,
          productKey: productOptions[0],
          type: typeRef.current,
        },
      };
      defaultSearchVals.current = historySearchValue?.searchValues
        ? historySearchValue?.searchValues
        : initSearchCondition.current;
      setSearchCondition(defaultSearchVals.current);
      setAutoFetch(true);
    }
    const val = {
      ...SearchConfig,
      fields: formatSearchConfig.fields.map((v) => {
        if (v.fieldName === 'productKey') {
          v = {
            ...v,
            options: productOptions,
          };
        }
        return v;
      }),
    };
    setFormatSearchConfig(val);
    createAppFormConfig.current = {
      ...createAppFormConfig.current,
      fields: createAppFormConfig.current?.fields?.map((v) => {
        if (v.fieldName === 'productKey') {
          v = {
            ...v,
            options: productOptions,
          };
        }
        return v;
      }),
    };
  };

  const handleEnableApp = async (
    productKey: string,
    name: string,
    enable: 0 | 1,
  ) => {
    const res = await fetchApi.enableApp({
      productKey,
      name,
      enable,
      type: typeRef.current!,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success('成功');
      reloadTable();
    } else {
      message.error(res.message || '失败');
    }
  };

  const formatColumns = () => {
    const config =
      typeRef.current === 'firmware' ? TableConfig : AppTableConfig;
    return config?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    const path =
                      typeRef.current === 'firmware' ? '/firmware' : '/app';
                    navigator(
                      path +
                        '/firmwareInfo?type=' +
                        typeRef.current +
                        '&productKey=' +
                        searchCondition.searchForm.productKey.value +
                        '&productName=' +
                        searchCondition.searchForm.productKey.label +
                        '&appName=' +
                        record.name +
                        '&appAlias=' +
                        record.alias +
                        '&appEnable=' +
                        record.enable,
                    );
                  }}
                >
                  {typeRef.current === 'firmware' ? '固件信息' : '应用信息'}
                </a>
                {record.enable === 1 && (
                  <a
                    onClick={() => {
                      handleAdd(
                        'edit',
                        record.productKey,
                        record.name,
                        typeRef.current,
                      );
                    }}
                  >
                    编辑
                  </a>
                )}
                {record.enable === 1 && (
                  <CommonConfirmBtn
                    message={
                      typeRef.current === 'firmware'
                        ? '确认禁用此固件么?'
                        : '确认禁用此应用么'
                    }
                    btnText="禁用"
                    onConfirm={() =>
                      handleEnableApp(record.productKey, record.name, 0)
                    }
                  />
                )}
                {record.enable === 0 && (
                  <CommonConfirmBtn
                    message={
                      typeRef.current === 'firmware'
                        ? '确认启用此固件么?'
                        : '确认启用此应用么'
                    }
                    btnText="启用"
                    onConfirm={() =>
                      handleEnableApp(record.productKey, record.name, 1)
                    }
                  />
                )}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onSearchClick = (val: any) => {
    const newValue = {
      searchForm: { ...val, type: typeRef.current },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(newValue);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newValue,
      }),
    );
  };
  const onResetClick = () => {
    defaultSearchVals.current = initSearchCondition.current;
    setSearchCondition(initSearchCondition.current);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition.current,
      }),
    );
  };
  const getAppInfo = async (productKey: string, name: string, type: string) => {
    const res = await fetchApi.getAppInfo({ productKey, type, name });
    if (res.code === HttpStatusCode.Success) {
      return {
        ...res.data,
      };
    }
    return {};
  };
  const handleAdd = async (
    operateType: 'add' | 'edit',
    productKey?: any,
    name?: any,
    type?: any,
  ) => {
    let formInstance: any = null;
    let info: any = {
      productKey: initSearchCondition.current.searchForm.productKey?.value,
    };
    let formConfig = createAppFormConfig.current!;
    if (operateType === 'edit') {
      info = await getAppInfo(productKey, name, type);
      info = {
        ...info,
        blockNo: {
          label: info.blockName,
          value: info.blockNo,
        },
      };
    }
    showModal({
      title: typeRef.current === 'firmware' ? '固件信息' : '应用信息',
      width: '600px',
      content: (
        <CommonForm
          name="create-app-form"
          formConfig={{
            ...formConfig,
          }}
          defaultValue={{ ...info, operateType }}
          getFormInstance={(v: any) => {
            formInstance = v;
            v.setFieldsValue({ ...info });
          }}
        />
      ),
      footer: [
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            const val = await formInstance.validateFields();
            let res;
            if (operateType === 'add') {
              res = await fetchApi.createApp({
                ...val,
                blockNo: val?.blockNo?.value,
                type: typeRef.current,
              });
            } else {
              res = await fetchApi.editApp({
                ...val,
                blockNo: val?.blockNo?.value,
                type: typeRef.current,
              });
            }

            if (res.code === HttpStatusCode.Success) {
              message.success(operateType === 'add' ? '创建成功' : '编辑成功');
              reloadTable();
              cb();
            } else {
              message.error(res.message);
            }
          },
        },
        {
          text: '取消',
          type: 'cancelBtn',
          otherCSSProperties: { marginLeft: '10px' },
          onClick: (cb: any) => {
            cb();
          },
        },
      ],
    });
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: typeRef.current === 'firmware' ? '创建固件' : '创建应用',
      key: 'add',
      onClick: () => handleAdd('add'),
    },
  ];

  return (
    <div className="firmware-page">
      <CommonForm
        name="firmware-searchForm"
        formConfig={formatSearchConfig}
        layout={'inline'}
        defaultValue={defaultSearchVals.current.searchForm}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        getFormInstance={(ref: any) => {
          ref?.setFieldsValue(searchCondition.searchForm);
        }}
      />

      <CommonTable
        searchCondition={searchCondition}
        loading={loading}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        middleBtns={middleBtns}
        columns={formatColumns()}
        rowKey={'appId'}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      ></CommonTable>
    </div>
  );
};

export default React.memo(Firmware);
