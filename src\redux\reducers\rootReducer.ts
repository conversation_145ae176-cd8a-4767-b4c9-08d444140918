import { combineReducers } from '@reduxjs/toolkit';
import { routerReducer } from './router';
import { searchformReducer } from './searchform';
import { selectedVehicleReducer } from './selectedVehicle';
import { vehicleConfigReducer } from './vehicleConfig';
import { stationInfoReducer } from './stationInfo';
import { createOTATaskReducer } from './createOTATask';
export const rootReducer = combineReducers({
  router: routerReducer,
  searchform: searchformReducer,
  selectedVehicle: selectedVehicleReducer,
  vehicleConfig: vehicleConfigReducer,
  stationInfo: stationInfoReducer,
  createOTATask: createOTATaskReducer,
});
