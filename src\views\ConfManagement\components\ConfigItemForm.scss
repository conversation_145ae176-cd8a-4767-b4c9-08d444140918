.config-item-form {
  padding: 20px;

  .ant-card {
    margin-bottom: 20px;
  }

  .param-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .param-card {
    .no-params {
      text-align: center;
      color: #999;
      padding: 20px 0;
    }

    .param-item {
      margin-bottom: 20px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f9f9f9;

      .param-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

      .param-type-fields {
        padding: 10px;
        margin-top: 10px;
        background-color: #f0f0f0;
        border-radius: 4px;
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
  }

  .ant-form-item-required::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
