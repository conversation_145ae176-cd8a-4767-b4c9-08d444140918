.block-part {
  width: 20%;
  border-right: 1px solid #ebebeb;
  .add-icon {
    width: 30px;
    height: 30px;
    color: white;
    background-color: #3c6ef0;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
  }
  .block-name {
    padding: 8px 12px;
    cursor: pointer;
    .name {
      line-height: 20px;
      font-family: PingFangSC-Medium;
      font-size: 12px;
      letter-spacing: 0;
      color: #555;
      position: relative;
      border-right: 2px solid transparent;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .edit-btns {
        span:first-child {
          color: #0070cc;
          margin-right: 10px;
        }
        span:nth-child(2) {
          color: red;
        }
      }
    }
    .identifier {
      opacity: 0.6;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #888;
      letter-spacing: 0;
    }
    &.selected {
      color: #0070cc;
      border-right: 2px solid #0070cc;
      background: #f5f5f5;
      .name {
        color: #0070cc;
      }
      .identifier {
        color: #0070cc;
      }
    }
  }
}
