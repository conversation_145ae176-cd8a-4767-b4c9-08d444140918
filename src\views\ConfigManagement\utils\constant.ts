import { FormConfig } from '@/components/CommonForm/formConfig';

export enum EnableType {
  ENABLE = 1, // 有效
  UNENABLE = 0, //  无效
}

export const formConfig: FormConfig = {
  fields: [
    {
      type: 'input',
      fieldName: 'number',
      label: '模板编号',
      disabled: true,
    },
    {
      type: 'radioGroup',
      fieldName: 'productType',
      label: '所属产品',
      options: [
        {
          label: '无人车',
          value: 'vehicle',
        },
        {
          label: '机器人',
          value: 'robot',
        },
      ],
    },
    {
      type: 'input',
      fieldName: 'name',
      label: '模板名称',
      placeholder: '请输入配置文件名',
      validatorRules: [
        {
          required: true,
          message: '请输入配置文件名',
        },
      ],
    },
    {
      type: 'input',
      fieldName: 'description',
      label: '用途说明',
      placeholder: '请描述一下配置文件使用用途',
    },
    {
      type: 'checkboxGroup',
      fieldName: 'position',
      label: '发布路径',
      options: [
        {
          label: 'control',
          value: 'control',
        },
        {
          label: 'compute',
          value: 'compute',
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择发布路径',
        },
      ],
    },
    {
      type: 'textarea',
      fieldName: 'content',
      label: '模板内容',
      placeholder: '请输入配置内容',
      showCount: false,
      autoSize: {
        minRows: 15,
        maxRows: 15,
      },
      validatorRules: [
        {
          required: true,
          message: '请输入配置内容',
        },
      ],
    },
    {
      type: 'radioGroup',
      fieldName: 'enable',
      label: '模板状态',
      options: [
        {
          label: '有效',
          value: 1,
        },
        {
          label: '无效',
          value: 0,
        },
      ],
    },
  ],
  linkRules: {
    productType: [
      {
        linkFieldName: 'position',
        rule: 'valueDisable',
        disabledValue: [0],
      },
      {
        linkFieldName: 'position',
        rule: 'refresh',
        refreshFunc: (value: any) => {
          return value == 'robot'
            ? [
                {
                  label: 'control',
                  value: 'control',
                },
              ]
            : [
                {
                  label: 'control',
                  value: 'control',
                },
                {
                  label: 'compute',
                  value: 'compute',
                },
              ];
        },
      },
    ],
  },
};
