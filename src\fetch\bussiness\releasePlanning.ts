import { request } from '../core';
import { Method } from '../core/util';

export const getAppTypeList = async (): Promise<any> => {
  return request({
    path: '/intelligent/device/web/app/get_application_type_list',
    method: Method.POST,
    newGeteway: true,
  });
};

export const getAppTypeListByType = async (params: {
  productKey: string;
  type: string;
  enable?: number;
}): Promise<any> => {
  return request({
    path: '/intelligent/device/web/app/get_application_list',
    method: Method.POST,
    newGeteway: true,
    body: params,
  });
};

export const getAppVersionListByModel = async (params: {
  productKey: string;
  type: string;
  enable?: number;
  appName: string;
  productModelNoList: string[];
}): Promise<any> => {
  return request({
    path: '/intelligent/device/web/app/get_application_version_list_by_model',
    method: Method.POST,
    newGeteway: true,
    body: params,
  });
};

export const getTaskList = async (params: {
  pageNum: any;
  pageSize: any;
  searchForm: any;
}): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/get_issue_task_page_list',
    method: Method.POST,
    newGeteway: true,
    body: {
      ...params.searchForm,
      appName: params?.searchForm?.appName?.value,
      appType: params?.searchForm?.appType?.value,
      productModelNo: params?.searchForm?.productModelNo?.value,
      appVersionNumber: params?.searchForm?.appVersionNumber?.value,
      issueTaskStatus: params?.searchForm?.issueTaskStatus?.value,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    },
  };
  return request(options);
};
export const getTaskStatus = (): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/get_issue_task_status_list',
    method: Method.POST,
    newGeteway: true,
  };
  return request(options);
};
export const cancelTask = (params: { issueTaskNumber: any }): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/cancel_issue_task',
    method: Method.POST,
    newGeteway: true,
    body: params,
  };
  return request(options);
};

export const createUpgradeTask = (params: any): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/create_issue_task',
    method: Method.POST,
    newGeteway: true,
    body: params,
  };
  return request(options);
};

export const getTaskDetail = (issueTaskNumber: string): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/get_issue_task_info',
    method: Method.POST,
    newGeteway: true,
    body: {
      issueTaskNumber,
    },
  };
  return request(options);
};

export const getIssueDeviceList = (params: {
  pageNum: number;
  pageSize: number;
  productKey: string;
  issueTaskNumber: string;
  groupNoList: string[];
  deviceName: string;
  issueDeviceStatusList: number[];
  online: number;
}): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/get_issue_device_page_list',
    method: Method.POST,
    newGeteway: true,
    body: params,
  };
  return request(options);
};

export const stopIssueDevice = (params: {
  issueTaskNumber: string;
  deviceNameList: string[];
}): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/stop_issue_device',
    method: Method.POST,
    newGeteway: true,
    body: params,
  };
  return request(options);
};

export const getIssueDeviceCount = (params: {
  productKey: string;
  issueTaskNumber: string;
  groupNoList?: string[];
  online?: number;
  issueDeviceStatusList?: number[];
  deviceName?: string;
}): Promise<any> => {
  const options = {
    path: '/intelligent/device/web/issue/get_issue_device_count',
    method: Method.POST,
    newGeteway: true,
    body: params,
  };
  return request(options);
};

export const checkUploadDevice = (params: {
  productKey: string;
  productModelNoList: string[];
  fileS3BucketName: string;
  fileS3Key: string;
  fileS3Md5: string;
}): Promise<any> => {
  const requestOptions: RequestOptions = {
    method: 'POST',
    path: '/intelligent/device/web/issue/check_issue_device_file',
    body: params,
    newGeteway: true,
  };
  return request(requestOptions);
};
