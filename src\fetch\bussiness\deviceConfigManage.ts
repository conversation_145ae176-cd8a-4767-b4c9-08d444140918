// 基于新数据结构生成的mock数据，包含21条记录
const mockDataList = [
  {
    recordNo: 'REC001',
    operateVersion: 'v1.0.1',
    templateNo: 'TPL001',
    templateName: '基础配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 08:00:00',
    issueTaskNo: 'TASK001',
    modifyUser: '张三',
    modifyTime: '2025-01-01 08:30:00',
    issueTime: '2025-01-01 09:00:00',
  },
  {
    recordNo: 'REC002',
    operateVersion: 'v1.0.2',
    templateNo: 'TPL002',
    templateName: '网络配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 09:15:00',
    issueTaskNo: 'TASK002',
    modifyUser: '李四',
    modifyTime: '2025-01-01 09:45:00',
    issueTime: '2025-01-01 10:15:00',
  },
  {
    recordNo: 'REC003',
    operateVersion: 'v1.0.3',
    templateNo: 'TPL003',
    templateName: '安全配置模板',
    changeType: 'DELETE',
    changeTypeName: '删除',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-01 10:30:00',
    issueTaskNo: 'TASK003',
    modifyUser: '王五',
    modifyTime: '2025-01-01 11:00:00',
    issueTime: '2025-01-01 11:30:00',
  },
  {
    recordNo: 'REC004',
    operateVersion: 'v1.1.0',
    templateNo: 'TPL004',
    templateName: '性能优化模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 12:00:00',
    issueTaskNo: 'TASK004',
    modifyUser: '赵六',
    modifyTime: '2025-01-01 12:30:00',
    issueTime: '2025-01-01 13:00:00',
  },
  {
    recordNo: 'REC005',
    operateVersion: 'v1.1.1',
    templateNo: 'TPL005',
    templateName: '监控配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 13:15:00',
    issueTaskNo: 'TASK005',
    modifyUser: '陈七',
    modifyTime: '2025-01-01 13:45:00',
    issueTime: '2025-01-01 14:15:00',
  },
  {
    recordNo: 'REC006',
    operateVersion: 'v1.1.2',
    templateNo: 'TPL006',
    templateName: '备份配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-01 14:30:00',
    issueTaskNo: 'TASK006',
    modifyUser: '孙八',
    modifyTime: '2025-01-01 15:00:00',
    issueTime: '2025-01-01 15:30:00',
  },
  {
    recordNo: 'REC007',
    operateVersion: 'v1.2.0',
    templateNo: 'TPL007',
    templateName: '日志配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 16:00:00',
    issueTaskNo: 'TASK007',
    modifyUser: '周九',
    modifyTime: '2025-01-01 16:30:00',
    issueTime: '2025-01-01 17:00:00',
  },
  {
    recordNo: 'REC008',
    operateVersion: 'v1.2.1',
    templateNo: 'TPL008',
    templateName: '告警配置模板',
    changeType: 'DELETE',
    changeTypeName: '删除',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-01 17:15:00',
    issueTaskNo: 'TASK008',
    modifyUser: '吴十',
    modifyTime: '2025-01-01 17:45:00',
    issueTime: '2025-01-01 18:15:00',
  },
  {
    recordNo: 'REC009',
    operateVersion: 'v1.2.2',
    templateNo: 'TPL009',
    templateName: '权限配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 18:30:00',
    issueTaskNo: 'TASK009',
    modifyUser: '郑十一',
    modifyTime: '2025-01-01 19:00:00',
    issueTime: '2025-01-01 19:30:00',
  },
  {
    recordNo: 'REC010',
    operateVersion: 'v1.3.0',
    templateNo: 'TPL010',
    templateName: '系统配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 20:00:00',
    issueTaskNo: 'TASK010',
    modifyUser: '王十二',
    modifyTime: '2025-01-01 20:30:00',
    issueTime: '2025-01-01 21:00:00',
  },
  {
    recordNo: 'REC011',
    operateVersion: 'v1.3.1',
    templateNo: 'TPL011',
    templateName: '数据库配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-01 21:15:00',
    issueTaskNo: 'TASK011',
    modifyUser: '冯十三',
    modifyTime: '2025-01-01 21:45:00',
    issueTime: '2025-01-01 22:15:00',
  },
  {
    recordNo: 'REC012',
    operateVersion: 'v1.3.2',
    templateNo: 'TPL012',
    templateName: '缓存配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-01 22:30:00',
    issueTaskNo: 'TASK012',
    modifyUser: '陈十四',
    modifyTime: '2025-01-01 23:00:00',
    issueTime: '2025-01-01 23:30:00',
  },
  {
    recordNo: 'REC013',
    operateVersion: 'v1.4.0',
    templateNo: 'TPL013',
    templateName: '消息队列配置模板',
    changeType: 'DELETE',
    changeTypeName: '删除',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-02 00:00:00',
    issueTaskNo: 'TASK013',
    modifyUser: '卫十五',
    modifyTime: '2025-01-02 00:30:00',
    issueTime: '2025-01-02 01:00:00',
  },
  {
    recordNo: 'REC014',
    operateVersion: 'v1.4.1',
    templateNo: 'TPL014',
    templateName: '负载均衡配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-02 01:15:00',
    issueTaskNo: 'TASK014',
    modifyUser: '蒋十六',
    modifyTime: '2025-01-02 01:45:00',
    issueTime: '2025-01-02 02:15:00',
  },
  {
    recordNo: 'REC015',
    operateVersion: 'v1.4.2',
    templateNo: 'TPL015',
    templateName: '防火墙配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-02 02:30:00',
    issueTaskNo: 'TASK015',
    modifyUser: '沈十七',
    modifyTime: '2025-01-02 03:00:00',
    issueTime: '2025-01-02 03:30:00',
  },
  {
    recordNo: 'REC016',
    operateVersion: 'v1.5.0',
    templateNo: 'TPL016',
    templateName: 'SSL证书配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-02 04:00:00',
    issueTaskNo: 'TASK016',
    modifyUser: '韩十八',
    modifyTime: '2025-01-02 04:30:00',
    issueTime: '2025-01-02 05:00:00',
  },
  {
    recordNo: 'REC017',
    operateVersion: 'v1.5.1',
    templateNo: 'TPL017',
    templateName: 'API网关配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-02 05:15:00',
    issueTaskNo: 'TASK017',
    modifyUser: '杨十九',
    modifyTime: '2025-01-02 05:45:00',
    issueTime: '2025-01-02 06:15:00',
  },
  {
    recordNo: 'REC018',
    operateVersion: 'v1.5.2',
    templateNo: 'TPL018',
    templateName: '容器配置模板',
    changeType: 'DELETE',
    changeTypeName: '删除',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-02 06:30:00',
    issueTaskNo: 'TASK018',
    modifyUser: '朱二十',
    modifyTime: '2025-01-02 07:00:00',
    issueTime: '2025-01-02 07:30:00',
  },
  {
    recordNo: 'REC019',
    operateVersion: 'v1.6.0',
    templateNo: 'TPL019',
    templateName: '微服务配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-02 08:00:00',
    issueTaskNo: 'TASK019',
    modifyUser: '秦二十一',
    modifyTime: '2025-01-02 08:30:00',
    issueTime: '2025-01-02 09:00:00',
  },
  {
    recordNo: 'REC020',
    operateVersion: 'v1.6.1',
    templateNo: 'TPL020',
    templateName: '云存储配置模板',
    changeType: 'MODIFY',
    changeTypeName: '修改',
    issued: 1,
    issuedName: '是',
    createTime: '2025-01-02 09:15:00',
    issueTaskNo: 'TASK020',
    modifyUser: '尤二十二',
    modifyTime: '2025-01-02 09:45:00',
    issueTime: '2025-01-02 10:15:00',
  },
  {
    recordNo: 'REC021',
    operateVersion: 'v1.6.2',
    templateNo: 'TPL021',
    templateName: '边缘计算配置模板',
    changeType: 'ADD',
    changeTypeName: '新增',
    issued: 0,
    issuedName: '否',
    createTime: '2025-01-02 10:30:00',
    issueTaskNo: 'TASK021',
    modifyUser: '许二十三',
    modifyTime: '2025-01-02 11:00:00',
    issueTime: '2025-01-02 11:30:00',
  },
];

class DeviceConfigManageApi {
  public getDeviceConfigChangeRecordList = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/device_config/get_device_config_change_record_list',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: mockDataList,
      message: 'ok',
    });
  };
}

export default DeviceConfigManageApi;
