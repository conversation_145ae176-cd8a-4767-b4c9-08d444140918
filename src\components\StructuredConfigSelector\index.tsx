import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Tree, Button, Tabs, message } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

interface ItemType {
  itemNo: string;
  itemName: string;
}

interface ItemsType {
  itemTypeNo: string;
  itemTypeName: string;
  itemList: ItemType[];
}

interface BlockData {
  blockNo: string;
  blockName: string;
  itemsTypeList: ItemsType[];
}

interface StructuredConfigSelectorProps {
  selectedData?: any;
}

export interface StructuredConfigSelectorRef {
  getSelectedData: () => Promise<Record<string, string[]>>;
}

const StructuredConfigSelector = forwardRef<StructuredConfigSelectorRef, StructuredConfigSelectorProps>(
  ({ selectedData }, ref) => {
    const configTemplateApi = new ConfigTemplateApi();
    
    const [configData, setConfigData] = useState<BlockData[]>([]);
    const [activeBlockNo, setActiveBlockNo] = useState<string>();
    const [tabStates, setTabStates] = useState<Record<string, { selectedKeys: string[] }>>({});
    const [loading, setLoading] = useState(false);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getSelectedData: async () => {
        return tabStates;
      },
    }));

    // 当选中数据变化时，获取配置数据
    useEffect(() => {
      if (selectedData?.productKey) {
        fetchConfigData(selectedData.productKey);
      }
    }, [selectedData?.productKey]);

    const fetchConfigData = async (productKey: string) => {
      setLoading(true);
      try {
        const res = await configTemplateApi.getAllEnableConfigBaseInfoList({
          productKey,
        });

        if (res && res.code === HttpStatusCode.Success) {
          setConfigData(res.data || []);
          
          // 设置默认选中第一个tab
          if (res.data && res.data.length > 0) {
            setActiveBlockNo(res.data[0].blockNo);
            
            // 初始化每个tab的状态
            const initialStates: Record<string, { selectedKeys: string[] }> = {};
            res.data.forEach((block: BlockData) => {
              initialStates[block.blockNo] = { selectedKeys: [] };
            });
            setTabStates(initialStates);
          }
        } else {
          message.error(res?.message || '获取配置列表失败');
        }
      } catch (error) {
        message.error('获取配置列表失败');
      } finally {
        setLoading(false);
      }
    };

    // 构建树形数据
    const buildTreeData = (itemsTypeList: ItemsType[]): DataNode[] => {
      return itemsTypeList.map((type) => ({
        key: type.itemTypeNo,
        title: type.itemTypeName,
        checkable: false, // 配置类不可选
        children: type.itemList.map((item) => ({
          key: item.itemNo,
          title: item.itemName,
          checkable: true, // 允许选择
        })),
      }));
    };

    // 获取当前tab的状态
    const getCurrentTabState = () => activeBlockNo && tabStates[activeBlockNo];

    // 更新当前tab的状态
    const updateCurrentTabState = (updates: Partial<{ selectedKeys: string[] }>) => {
      if (activeBlockNo) {
        setTabStates(prev => ({
          ...prev,
          [activeBlockNo]: {
            ...prev[activeBlockNo],
            ...updates,
          },
        }));
      }
    };

    // 获取当前模块数据
    const getCurrentBlock = () => {
      return configData.find((block) => block.blockNo === activeBlockNo);
    };

    // 获取所有可选择的配置项key
    const getAllSelectableKeys = () => {
      const keys: string[] = [];
      const currentBlock = getCurrentBlock();
      currentBlock?.itemsTypeList?.forEach((type: any) => {
        type.itemList?.forEach((item: any) => {
          keys.push(item.itemNo);
        });
      });
      return keys;
    };

    // 全选处理
    const handleSelectAll = () => {
      const allKeys = getAllSelectableKeys();
      updateCurrentTabState({ selectedKeys: allKeys });
    };

    // 清空处理
    const handleClear = () => {
      updateCurrentTabState({ selectedKeys: [] });
    };

    // 处理树节点选择
    const handleTreeCheck = (checked: any) => {
      const checkedKeys = Array.isArray(checked) ? checked : checked.checked;
      updateCurrentTabState({ selectedKeys: checkedKeys });
    };

    if (!selectedData?.productKey) {
      return (
        <div className="structured-config-selector">
          <div className="section-title">选择结构化配置项</div>
          <div className="no-product">请先选择产品</div>
        </div>
      );
    }

    return (
      <div className="structured-config-selector">
        <div className="section-title">选择结构化配置项</div>
        <div className="section-content">
          {configData.length > 0 ? (
            <>
              <Tabs
                activeKey={activeBlockNo}
                onChange={setActiveBlockNo}
                items={configData.map((block) => ({
                  key: block.blockNo,
                  label: block.blockName,
                }))}
              />
              
              <div className="config-tree-container">
                <div className="tree-header">
                  <span>配置项列表</span>
                  <div className="header-actions">
                    <Button type="link" size="small" onClick={handleSelectAll}>
                      全选
                    </Button>
                    <Button type="link" size="small" onClick={handleClear}>
                      清空
                    </Button>
                  </div>
                </div>
                
                <Tree
                  treeData={buildTreeData(getCurrentBlock()?.itemsTypeList || [])}
                  checkedKeys={getCurrentTabState()?.selectedKeys || []}
                  defaultExpandAll={true}
                  checkable={true}
                  selectable={false}
                  onCheck={handleTreeCheck}
                  loading={loading}
                />
                
                {getCurrentTabState()?.selectedKeys?.length > 0 && (
                  <div className="selected-info">
                    已选择 {getCurrentTabState()?.selectedKeys?.length} 个配置项
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="no-data">暂无配置数据</div>
          )}
        </div>
      </div>
    );
  }
);

StructuredConfigSelector.displayName = 'StructuredConfigSelector';

export default StructuredConfigSelector;
