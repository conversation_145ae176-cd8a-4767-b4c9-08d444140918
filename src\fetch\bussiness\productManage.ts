import { request } from '@/fetch/core';
class ProductManageFetch {
  getProduct = (params: {
    pageNum: number;
    pageSize: number;
    productKey?: AnyObj;
    tagList?: any[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/product/query_list_with_page',
      body: { ...params, productKey: params.productKey?.value },
      newGeteway: true,
    };
    return request(requestOptions);
  };

  addProduct = (params: {
    productName: string;
    productKey?: string;
    productModelList?: string[];
    productDescription?: string;
    tagList?: any[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/product/add',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  delProduct = (params: { productKey: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/product/delete',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  getProductDetail = (params: { productKey: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: 'intelligent/device/web/product/query_info',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };

  editProductInfo = ({
    type,
    info,
    productKey,
  }: {
    type: string;
    info: any;
    productKey: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      newGeteway: true,
    };
    switch (type) {
      case 'productName':
        requestOptions.path = 'intelligent/device/web/product/modify_name';
        requestOptions.body = {
          productKey: productKey,
          productName: info,
        };
        break;
      case 'productDescription':
        requestOptions.path =
          'intelligent/device/web/product/modify_description';
        requestOptions.body = {
          productKey: productKey,
          productDescription: info,
        };
        break;
      case 'productModel':
        requestOptions.path = 'intelligent/device/web/product/modify_model';
        requestOptions.body = {
          productKey: productKey,
          productModelVOList: info,
        };
        break;
      case 'productTag':
        requestOptions.path = 'intelligent/device/web/tag/update';
        requestOptions.body = {
          dataNo: productKey,
          tagType: 1, // 1表示标签类型是产品
          tagInfoList: info,
        };
        break;
    }
    return request(requestOptions);
  };

  checkHasDevice = (params: { productModelNo: string }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/product/verify_delete_model',
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  };
}

export default ProductManageFetch;
