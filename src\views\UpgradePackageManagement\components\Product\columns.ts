import { FormConfig } from '@/components/CommonForm/formConfig';
import { ProductType, ProductTypeName } from '@/utils/constant';
import { dropDownKey, dropDownListKey } from '@/utils/searchFormEnum';

export const ProducTableData: any[] = [
  {
    title: '包ID',
    dataIndex: 'productPackageNumber',
    align: 'center',
    width: 200,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属设备',
    dataIndex: 'applyVehicleBusinessTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品包内容',
    dataIndex: 'appInfoListName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    align: 'center',
    ellipsis: true,
    render: (record: any) => record || '—',
  },
  {
    title: '更新时间',
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 300,
    fixed: 'right',
  },
];

export const SearchForm: FormConfig = {
  fields: [{}],
};

export const EditForm: FormConfig = {
  fields: [
    {
      fieldName: 'productType',
      label: '所属产品',
      type: 'radioGroup',
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请选择所属产品',
        },
      ],
      options: [
        {
          label: ProductTypeName.VEHICLE,
          value: ProductType.VEHICLE,
        },
        {
          label: ProductTypeName.ROBOT,
          value: ProductType.ROBOT,
        },
      ],
    },
    {
      fieldName: 'businessType',
      label: '所属设备',
      type: 'radioGroup',
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请选择所属设备',
        },
      ],
    },
    {
      fieldName: 'version',
      label: '版本号',
      type: 'input',
      validatorRules: [
        {
          required: true,
          message: '请填写版本号',
        },
      ],
    },
    {
      fieldName: 'packageType',
      type: 'ReactNode',
      label: '选择应用包',
      // validatorRules: [
      //   {
      //     required: true,
      //     message: '请选择应用包',
      //   },
      // ],
    },
    {
      fieldName: 'description',
      label: '备注信息',
      type: 'textarea',
    },
  ],
  linkRules: {
    productType: [
      {
        linkFieldName: 'businessType',
        rule: 'refresh',
        refreshFunc: (value: any) => {
          return value == ProductType.VEHICLE
            ? [
                {
                  label: '配送车',
                  value: 'DISPATCH',
                },
                {
                  label: '售卖车',
                  value: 'VENDING',
                },
              ]
            : [
                {
                  label: '京麟',
                  value: 'JING_LIN',
                },
                {
                  label: '重德',
                  value: 'ZHONG_DE',
                },
              ];
        },
      },
    ],
  },
};

export const AppListColumn: any[] = [
  {
    title: '应用',
    dataIndex: 'appName',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    ellipsis: true,
  },
  {
    title: '更新内容',
    dataIndex: 'updateInfo',
    ellipsis: true,
  },
  {
    title: '缺陷',
    dataIndex: 'defect',
    ellipsis: true,
  },
];

export const AppFormData: FormConfig = {
  fields: [
    {
      fieldName: 'appName',
      label: '应用',
      placeholder: '请选择应用',
      type: 'select',
    },
    {
      fieldName: 'appVersionNumber',
      label: '版本号',
      placeholder: '请选择版本号',
      type: 'select',
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请选择所属产品',
      type: 'select',
      dropDownKey: dropDownKey.productTypeList,
      dropDownListKey: dropDownListKey.productTypeList,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'applyVehicleBusinessType',
      label: '所属设备',
      placeholder: '请选择所属设备',
      type: 'select',
    },
    {
      fieldName: 'description',
      label: '描述',
      placeholder: '请输入内容',
      type: 'input',
    },
  ],
};
